{"Back to home": "Back to home", "Discover world best online courses here. 24k online course is waiting for you": "Discover world best online courses here. 24k online course is waiting for you", "Sign In": "Sign In", "New User": "New User", "Create an Account": "Create an Account", "Email or Phone": "Email or Phone", "Type your email or phone number": "Type your email or phone number", "Password": "Password", "Forgot Password": "Forgot Password", "Sign in with Google": "Sign in with Google", "Sign in with Facebook": "Sign in with Facebook", "Sign in with Twitter": "Sign in with Twitter", "Sure! You want to delete?": "Sure! You want to delete?", "You wont be able to revert this!": "You wont be able to revert this!", "Yes, Delete It!": "Yes, Delete It!", "Item has been deleted": "Item has been deleted", "Dashboard": "Dashboard", "Total Admin": "Total Admin", "Total Instructors": "Total Instructors", "Total Students": "Total Students", "Total Courses": "Total Courses", "Active Courses": "Active Courses", "Pending Courses": "Pending Courses", "Free Courses": "Free Courses", "Paid Courses": "Paid Courses", "Total Lessons": "Total Lessons", "Total Lectures": "Total Lectures", "Total Blogs": "Total Blogs", "Total Paid Sales": "Total Paid Sales", "Total Free Sales": "Total Free Sales", "Total Platform Charge": "Total Platform Charge", "Total Platform Charge (Current Month)": "Total Platform Charge (Current Month)", "Total Sell Commission": "Total Sell Commission", "Total Sell Commission (Current Month)": "Total Sell Commission (Current Month)", "Total Revenue": "Total Revenue", "Total Request Withdraw": "Total Request Withdraw", "Total Complete Withdraw": "Total Complete Withdraw", "Enrollment": "Enrollment", "Month": "Month", "Year": "Year", "Total Enrollment": "Total Enrollment", "Top Seller": "Top Seller", "Top Courses": "Top Courses", "View All": "View All", "Course": "Course", "Instructor Name": "Instructor Name", "Price": "Price", "Total": "Total", "Requested Withdrawal": "Requested <PERSON><PERSON><PERSON>", "Instructor": "<PERSON><PERSON><PERSON><PERSON>", "Payment Method": "Payment Method", "Request Date": "Request Date", "Amount": "Amount", "No Requested Found": "No Requested Found", "Manage Course": "Manage Course", "Review Pending": "Review Pending", "Hold": "Hold", "Approved": "Approved", "All Courses": "All Courses", "Enroll In Course": "Enroll In Course", "Course Reference": "Course Reference", "Categories": "Categories", "Subcategory": "Subcategory", "Tags": "Tags", "Languages": "Languages", "Difficulty Levels": "Difficulty Levels", "Promotional Tag": "Promotional Tag", "Rules & Benefits": "Rules & Benefits", "Manage Instructor": "Manage Instructor", "Pending Instructor": "Pending Instructor", "Approved Instructors": "Approved Instructors", "Blocked Instructors": "Blocked Instructors", "All Instructors": "All Instructors", "Add Instructor": "Add Instructor", "Manage Organization": "Manage Organization", "Pending Organizations": "Pending Organizations", "Approved Organizations": "Approved Organizations", "Blocked Organizations": "Blocked Organizations", "All Organizations": "All Organizations", "Add Organizations": "Add Organizations", "Manage Student": "Manage Student", "All Student": "All Student", "Add Student": "Add Student", "Manage Subscription": "Manage Subscription", "All Subscription": "All Subscription", "Add Subscription": "Add Subscription", "Subscription Sale Pending": "Subscription Sale Pending", "Subscription Sale": "Subscription Sale", "Manage SaaS": "Manage SaaS", "All SaaS": "All SaaS", "Add SaaS": "Add SaaS", "SaaS Sale Pending": "SaaS Sale Pending", "SaaS Sale": "SaaS Sale", "Manage Coupon": "Manage Coupon", "Coupon List": "Coupon List", "Add Coupon": "Add Coupon", "Manage Promotion": "Manage Promotion", "Promotion List": "Promotion List", "Add Promotion": "Add Promotion", "Manage Payout": "Manage Payout", "Request Withdrawal": "Request Withdrawal", "Complete Withdrawal": "Complete Withdrawal", "Rejected Withdrawal": "Rejected <PERSON>", "Subscription Payment": "Subscription Payment", "Financial Report": "Financial Report", "Revenue Report": "Revenue Report", "Order Report": "Order Report", "Order Pending": "Order Pending", "Order Cancelled": "Order Cancelled", "Consultation Cancel": "Consultation Cancel", "Certificate": "Certificate", "All Certificates": "All Certificates", "Add Certificate": "Add Certificate", "Manage Badge": "Manage Badge", "Badges": "Badges", "Manage Skill": "Manage Skill", "Skills": "Skills", "Manage Language": "Manage Language", "Language Settings": "Language Settings", "Support Ticket": "Support Ticket", "All Tickets": "All Tickets", "Open Ticket": "Open Ticket", "Admin Management": "Admin Management", "Add User": "Add User", "All Users": "All Users", "Roles": "Roles", "Email Management": "Email Management", "Email Template": "<PERSON>ail Te<PERSON>late", "Send Email": "Send Email", "Manage Page": "Manage Page", "Add Page": "Add Page", "All Pages": "All Pages", "Manage Menu": "<PERSON><PERSON>", "Nav Menu": "Nav Menu", "Nav Dynamic Menu": "Nav Dynamic Menu", "Footer Left": "Footer Left", "Footer Right": "Footer Right", "Application Settings": "Application Settings", "Global Settings": "Global Settings", "Location Settings": "Location Settings", "Home Settings": "Home Settings", "Mail Configuration": "Mail Configuration", "Payment Options": "Payment Options", "Become Instructor": "Become Instructor", "FAQ": "FAQ", "About Us": "About Us", "Contact Us": "Contact Us", "Maintenance Mode": "Maintenance Mode", "Cache Settings": "<PERSON><PERSON>", "Migrate Settings": "<PERSON><PERSON><PERSON>", "Policy Setting": "Policy Setting", "Terms Conditions": "Terms Conditions", "Privacy Policy": "Privacy Policy", "Cookie Policy": "<PERSON><PERSON>", "All Contact Us": "All Contact Us", "All Contact Us Issue": "All Contact Us Issue", "Add Contact Us Issue": "Add Contact Us Issue", "Manage Blog": "Manage Blog", "Add Blog": "Add Blog", "All Blog": "All Blog", "Blog Comment List": "Blog Comment List", "Blog Category": "Blog Category", "Manage Forum": "Manage Forum", "Forum Category": "Forum Category", "Account Settings": "Account <PERSON><PERSON>", "Profile": "Profile", "Change Password": "Change Password", "Manage Affiliate": "Manage Affiliate", "Affiliate Request List": "Affiliate Request List", "Affiliate Settings": "Affiliate <PERSON><PERSON>s", "Affiliate history": "Affiliate history", "Version Update": "Version Update", "Software Version": "Software Version", "Visit Site": "Visit Site", "Course overview has been updated": "Course overview has been updated", "Course category has been updated": "Course category has been updated", "New lesson has been added": "New lesson has been added", "Course has been sold": "Course has been sold", "Logout": "Logout", "© 2021 LMSZAI. All Rights Reserved.": "© 2021 LMSZAI. All Rights Reserved.", "ZainikLab": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "No data available in table": "No data available in table", "Showing _START_ To _END_ Of _TOTAL_ Entries": "Showing _START_ To _END_ Of _TOTAL_ Entries", "Showing 0 to 0 of 0 entries": "Showing 0 to 0 of 0 entries", "(filtered from _MAX_ total entries)": "(filtered from _MAX_ total entries)", "Show _MENU_ entries": "Show _MENU_ entries", "Loading...": "Loading...", "Processing...": "Processing...", "Search:": "Search:", "No matching records found": "No matching records found", "First": "First", "Last": "Last", "Next": "Next", "Previous": "Previous", "activate to sort column ascending": "activate to sort column ascending", "activate to sort column descending": "activate to sort column descending", "Come": "Come", "for": "for", "Learn": "Learn", "A Better": "A Better", "Learning": "Learning", "Future": "Future", "Platform": "Platform", "Era": "Era", "Starts Here.": "Starts Here.", "While the lovely valley teems with vapour around me, and the meridian sun strikes the upper": "While the lovely valley teems with vapour around me, and the meridian sun strikes the upper", "Browse Course": "Browse Course", "Learn From Experts": "Learn From Experts", "Mornings of spring which I enjoy with my whole heart about the gen": "Mornings of spring which I enjoy with my whole heart about the gen", "Earn a Certificate": "Earn a <PERSON>", "Meridian sun strikes the upper surface of the impenetrable foliage": "Meridian sun strikes the upper surface of the impenetrable foliage", "5000+ Courses": "5000+ Courses", "serenity has taken possession of my entire soul, like these sweet spring": "serenity has taken possession of my entire soul, like these sweet spring", "A Broad Selection Of Courses.": "A Broad Selection Of Courses.", "CHOOSE FROM 5,000 ONLINE VIDEO COURSES WITH NEW ADDITIONS": "CHOOSE FROM 5,000 ONLINE VIDEO COURSES WITH NEW ADDITIONS", "Development": "Development", "Business": "Business", "IT & Software": "IT & Software", "Design": "Design", "Free": "Free", "Latest Bundle Courses": "Latest Bundle Courses", "Choose From 5,000 Online Video Courses With New Additions": "Choose From 5,000 Online Video Courses With New Additions", "Courses": "Courses", "Our Top Categories": "Our Top Categories", "All Categories": "All Categories", "One to one consultation": "One to one consultation", "Consult with your favorite consultant!": "Consult with your favorite consultant!", "off": "off", "Hour": "Hour", "Book Schedule": "Book Schedule", "Subscribe Now!": "Subscribe Now!", "#Choose a subscription plan and save money!": "#Choose a subscription plan and save money!", "Monthly": "Monthly", "Yearly": "Yearly", "Starter": "Starter", "Full Free": "Full Free", "Unlimited access to ": "Unlimited access to ", " course": " course", "Access to ": "Access to ", "bundle course": "bundle course", "Buy": "Buy", "Consultancy Hour": "Consultancy Hour", "Devices Access": "Devices Access", "Get Started": "Get Started", "Recomended": "Recomended", "Advanced": "Advanced", "Premium": "Premium", "Top Rated Courses From Our Top Instructor.": "Top Rated Courses From Our Top Instructor.", "View All Instructor": "View All Instructor", "View Profile": "View Profile", "We Only Accept Professional Courses Form Professional Instructors": "We Only Accept Professional Courses Form Professional Instructors", "Become an Instructor": "Become an Instructor", "SaaS Plan": "SaaS Plan", "#Choose a saas plan and save money!": "#Choose a saas plan and save money!", "Organization": "Organization", "Unlimited Create ": "Unlimited Create ", "Create": "Create", "Bundle Course": "Bundle Course", "Enable": "Enable", "Subscription Course": "Subscription Course", "Give": "Give", "hour consultancy": "hour consultancy", "Minimum of": "Minimum of", "sale commission": "sale commission", "Unlimited create of": "Unlimited create of", "instructor": "instructor", "Create ": "Create ", "student": "student", "What Our Valuable Customers Say.": "What Our Valuable Customers Say.", "DANIEL JHON": "DANIEL JHON", "UI/UX DESIGNER": "UI/UX DESIGNER", "Great instructor, great course": "Great instructor, great course", "Wonderful serenity has taken possession of my entire soul, like these sweet mornings of spring which I enjoy with my whole heart. I am alone, and feel the charm of existence in this spot": "Wonderful serenity has taken possession of my entire soul, like these sweet mornings of spring which I enjoy with my whole heart. I am alone, and feel the charm of existence in this spot", "NORTH": "NORTH", "DEVELOPER": "DEVELOPER", "Awesome course & good response": "Awesome course & good response", "Noticed by me: when I hear the buzz of the little world among the stalks, and grow familiar with the countless indescribable forms of the insects and flies, then I feel the presence": "Noticed by me: when I hear the buzz of the little world among the stalks, and grow familiar with the countless indescribable forms of the insects and flies, then I feel the presence", "HIBRUPATH": "HIBRUPATH", "MARKETER": "MARKETER", "Fantastic course": "Fantastic course", "Successfully trained": "Successfully trained", "2000+ students": "2000+ students", "Video courses": "Video courses", "2000+ courses": "2000+ courses", "Expert instructor": "Expert instructor", "2000+ instructor": "2000+ instructor", "Proudly Received": "<PERSON><PERSON><PERSON>", "Frequently Ask Questions.": "Frequently Ask Questions.", "which I enjoy with my whole heart am alone feel?": "which I enjoy with my whole heart am alone feel?", "Ranquil existence, that I neglect my talents. I should be incapable of drawing a single stroke at the present moment; and yet I feel that was a greater artist than now. When, while the lovely valley with vapour around me, and the meridian.": "Ranquil existence, that I neglect my talents. I should be incapable of drawing a single stroke at the present moment; and yet I feel that was a greater artist than now. When, while the lovely valley with vapour around me, and the meridian.", "Still no luck?": "Still no luck?", "Then feel free to": "Then feel free to", "Contact With Us": "Contact With Us", "We are 24/7 for you": "We are 24/7 for you", "Quality Course, Instructor And Support": "Quality Course, Instructor And Support", "Single stroke at the present moment and yet I feel that was": "Single stroke at the present moment and yet I feel that was", "Popular Courses": "Popular Courses", "Explore Instructor": "Explore Instructor", "27/4 online support": "27/4 online support", "Support Center": "Support Center", "Booking Now": "Booking Now", "Select Date": "Select Date", "Hours": "Hours", "Type": "Type", "In Person": "In Person", "Online": "Online", "Make Payment": "Make Payment", "Home": "Home", "Search Course": "Search Course", "Pages": "Pages", "Blogs": "Blogs", "About": "About", "Contact": "Contact", "Support": "Support", "Terms & Conditions": "Terms & Conditions", "Forum": "Forum", "Blog": "Blog", "Admin Dashboard": "Admin Dashboard", "Help Support": "Help Support", "Mere tranquil existence, that I neglect my talents. I should be incapable of drawing a single stroke at the present": "Mere tranquil existence, that I neglect my talents. I should be incapable of drawing a single stroke at the present", "Company": "Company", "Contact Info": "Contact Info", "45/7 dreem street, albania dnobod, USA": "45/7 dreem street, albania dnobod, USA", "(123-458-987254824185)": "(123-458-987254824185)", "<EMAIL>": "<EMAIL>", "Verify Certificate": "Verify Certificate", "Proceed version update": "Proceed version update", "Update File": "Update File", "learn": "learn", "Point": "Point", "Area": "Area", "Serenity has taken possession of my entire soul, like these sweet spring": "Serenity has taken possession of my entire soul, like these sweet spring", "A Broad Selection Of Courses": "A Broad Selection Of Courses", "Office Productivity": "Office Productivity", "Personal Development": "Personal Development", "FQA": "FQA", "No Data Found": "No Data Found", "Zainiktheme": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Add Role": "Add Role", "Name": "Name", "Action": "Action", "Manage Roles": "Manage Roles", "Edit Role": "Edit Role", "Select Permission": "Select Permission", "Update": "Update", "60 Common C# Interview Questions in 2022: Ace Your Next Interview": "60 Common C# Interview Questions in 2022: Ace Your Next Interview", "Read More": "Read More", "PostgreSQL vs. MySQL: Which SQL Platform Should You Use?": "PostgreSQL vs. MySQL: Which SQL Platform Should You Use?", "Java vs. Python: Which Is the Best Programming Language for You?": "Java vs. Python: Which Is the Best Programming Language for You?", "Learn Coding in Scratch with a Cool Game Idea": "Learn Coding in Scratch with a Cool Game Idea", "Search...": "Search...", "Recent Blogs": "Recent Blogs", "Data Science": "Data Science", "Soft Skills": "Soft Skills", "Marketing": "Marketing", "Blog List": "Blog List", "Image": "Image", "Title": "Title", "Category": "Category", "Status": "Status", "Published": "Published", "Edit Blog": "Edit Blog", "Slug": "Slug", "Blog category": "Blog category", "Select Option": "Select Option", "Unpublished": "Unpublished", "Tag": "Tag", "Details": "Details", "Accepted Files": "Accepted Files", "Recommend Size": "Recommend <PERSON><PERSON>", "Menus": "Menus", "Menu": "<PERSON><PERSON>", "Footer Left Menu List": "Footer Left Menu List", "Add Menu": "<PERSON><PERSON>", "URL": "URL", "Active": "Active", "Type name": "Type name", "Type Url": "Type Url", "Deactivated": "Deactivated", "Save": "Save", "Edit Menu": "<PERSON>", "Footer Company Menu List": "Footer Company Menu List", "Mere Tranquil Existence, That I Neglect My Talents Should": "Mere Tranquil Existence, That I Neglect My Talents Should", "Possession Of My Entire Soul, Like These Sweet Mornings Of Spring Which I Enjoy With My Whole Heart. I Am Alone, And Charm Of Existence In This Spot, Which Was Created For The Bliss Of Souls Like Mine. I Am So Happy, My Dear Friend, So Absorbed In The Exquisite Sense Of Mere Tranquil Existence": "Possession Of My Entire Soul, Like These Sweet Mornings Of Spring Which I Enjoy With My Whole Heart. I Am Alone, And Charm Of Existence In This Spot, Which Was Created For The Bliss Of Souls Like Mine. I Am So Happy, My Dear Friend, So Absorbed In The Exquisite Sense Of Mere Tranquil Existence", "Our History": "Our History", "Possession Of My Entire Soul, Like These Sweet Mornings Of Spring Which I Enjoy With My Whole Heart. I Am Alone, And Charm Of Existence In This Spot Which": "Possession Of My Entire Soul, Like These Sweet Mornings Of Spring Which I Enjoy With My Whole Heart. I Am Alone, And Charm Of Existence In This Spot Which", "Upgrade Your Skills Today For Upgrading Your Life.": "Upgrade Your Skills Today For Upgrading Your Life.", "Noticed by me when I hear the buzz of the little world among the stalks, and grow familiar with the countless indescribable forms of the insects and flies, then I feel the presence stalks, and grow familiar with the countless": "Noticed by me when I hear the buzz of the little world among the stalks, and grow familiar with the countless indescribable forms of the insects and flies, then I feel the presence stalks, and grow familiar with the countless", "Find Your Course": "Find Your Course", "Our Passionate Team Members": "Our Passionate Team Members", "Arnold keens": "<PERSON>s", "CREATIVE DIRECTOR": "CREATIVE DIRECTOR", "James Bond": "<PERSON>", "Designer": "Designer", "Ketty Perry": "<PERSON><PERSON>", "Customer Support": "Customer Support", "Scarlett Johansson": "<PERSON>", "Ovita": "<PERSON><PERSON><PERSON>", "Vigon": "Vigon", "Betribe": "Betribe", "Parsit": "Parsit", "Karika": "<PERSON><PERSON>", "Get in Touch": "Get in Touch", "32 Yaool, myself down around dupal the street, London": "32 <PERSON><PERSON>, myself down around <PERSON><PERSON> the street, London", "328-456-07875": "328-456-07875", "128-456-07875": "128-456-07875", "Strikes the upper surface of the impenetrable foliage of my trees, and but a few stray gleams steal about the human. It might take 6 -12 hour to replay": "Strikes the upper surface of the impenetrable foliage of my trees, and but a few stray gleams steal about the human. It might take 6 -12 hour to replay", "Send Us a Message": "Send Us a Message", "Your name": "Your name", "Your Email": "Your Email", "Select an Issue": "Select an Issue", "Submit": "Submit", "Frequently Ask Questions. 2": "Frequently Ask Questions. 2", "CHOOSE FROM 5,000 ONLINE VIDEO COURSES WITH NEW ADDITIONS 3": "CHOOSE FROM 5,000 ONLINE VIDEO COURSES WITH NEW ADDITIONS 3", "Where can I see the status of my refund?": "Where can I see the status of my refund?", " In the Refund Status column you can see the date your refund request was submitted or when it was processed.": " In the Refund Status column you can see the date your refund request was submitted or when it was processed.", "When will I receive my refund?": "When will I receive my refund?", " Refund requests are submitted immediately to your payment processor or financial institution after Udemy has received and processed your request. It may take 5 to 10 business days or longer to post the funds in your account, depending on your financial institution or location.": " Refund requests are submitted immediately to your payment processor or financial institution after Udemy has received and processed your request. It may take 5 to 10 business days or longer to post the funds in your account, depending on your financial institution or location.", "Why was my refund request denied?": "Why was my refund request denied?", " All eligible courses purchased on Udemy can be refunded within 30 days, provided the request meets the guidelines in our refund policy. ": " All eligible courses purchased on Udemy can be refunded within 30 days, provided the request meets the guidelines in our refund policy. ", "What is a “credit refund”?": "What is a “credit refund”?", " In cases where a transaction is not eligible for a refund to your original payment method, the refund will be granted using LMSZAI Credit": " In cases where a transaction is not eligible for a refund to your original payment method, the refund will be granted using LMSZAI Credit", "Is That Helpful?": "Is That Helpful?", "Are You Still Confusion?": "Are You Still Confusion?", "Create New Ticket": "Create New Ticket", "View Ticket": "View Ticket", "Ops! You have entered invalid credentials": "Ops! You have entered invalid credentials", "Grow Your Skill, Make a Freelancing Career With Us": "Grow Your Skill, Make a Freelancing Career With Us", "Unlimited access to": "Unlimited access to", "Access to": "Access to", "Current Plan": "Current Plan", "Organization Panel": "Organization Panel", "Student": "Student", "My Learning": "My Learning", "My Consultation": "My Consultation", "Wishlist": "Wishlist", "Subscription Panel": "Subscription Panel", "Subscription Plan": "Subscription Plan", "SaaS Panel": "SaaS Panel", "Profile Settings": "Profile Settings", "My Wallet": "My Wallet", "Become an Affiliator": "Become an Affiliator", "Earning": "Earning", "This Month": "This Month", "Total Enroll": "Total Enroll", "Total Course": "Total Course", "Total Instructor": "Total Instructor", "Total Student": "Total Student", "Best Selling Course": "Best Selling Course", "Recently Added Courses": "Recently Added Courses", "Enroll": "Enroll", "Top Instructor (This Month)": "Top Instructor (This Month)", "Upload Your Course Today": "Upload Your Course Today", "Upload Course": "Upload Course", "Sale Statistics": "Sale Statistics", "Student Panel": "Student Panel", "All Instructor": "All Instructor", "All Course": "All Course", "Add Course": "Add Course", "Bundles Courses": "Bundles Courses", "Notice Board": "Notice Board", "Live Class": "Live Class", "Consultation": "Consultation", "Booking Request": "Booking Request", "Booking History": "Booking History", "Discussion": "Discussion", "Finance": "Finance", "Followings": "Followings", "Followers": "Followers", "Basic Information": "Basic Information", "Address & Location": "Address & Location", "Zoom Settings": "<PERSON>m <PERSON>s", "Merithub Settings": "<PERSON><PERSON><PERSON>", "Gmeet Settings": "<PERSON><PERSON><PERSON>", "My Courses": "My Courses", "Edit": "Edit", "Delete": "Delete", "Resources": "Resources", "Quiz": "Quiz", "Assignment": "Assignment", "Draft": "Draft", "My Course": "My Course", "Course Overview": "Course Overview", "Upload Video": "Upload Video", "Submit process": "Submit process", "What you will get & rules to follow:": "What you will get & rules to follow:", "Course Details": "Course Details", "Course Type": "Course Type", "Select Course Type": "Select Course Type", "Course Title": "Course Title", "Course Subtitle": "Course Subtitle", "Private for organization": "Private for organization", "Disabled": "Disabled", "Enable for subscription": "Enable for subscription", "Course Description Key Points": "Course Description Key Points", "Add": "Add", "Course Description": "Course Description", "Back": "Back", "Save and continue": "Save and continue", "Category & Tags": "Category & Tags", "Course Category": "Course Category", "Select Category": "Select Category", "Course Subcategory": "Course Subcategory", "Select Subcategory": "Select Subcategory", "Learners Accessibility & others": "Learners Accessibility & others", "Drip Content": "Drip Content", "Show all lesson": "Show all lesson", "Available sequentially": "Available sequentially", "Unlock after x day from enrollment": "Unlock after x day from enrollment", "Unlock content by date": "Unlock content by date", "Unlock after finish pre-requisite": "Unlock after finish pre-requisite", "All lecture will open after purchase.": "All lecture will open after purchase.", "Lecture will available sequentially one after other.": "Lecture will available sequentially one after other.", "Lecture will available after x days of enrollment. In the lecture add step you have to set the days.": "Lecture will available after x days of enrollment. In the lecture add step you have to set the days.", "Lecture will available on the inputted date. In the lecture add step you have to set the date.": "Lecture will available on the inputted date. In the lecture add step you have to set the date.", "Lecture will available after view the pre-requisite lecture. In the lecture add step you have to set the pre-requisite lecture.": "Lecture will available after view the pre-requisite lecture. In the lecture add step you have to set the pre-requisite lecture.", "Course Access Period": "Course Access Period", "If there is no expiry duration, leave the field blank.": "If there is no expiry duration, leave the field blank.", "Enrollment will expire after this number of days. Set 0 for no expiration": "Enrollment will expire after this number of days. Set 0 for no expiration", "Learners Accessibility": "Learners Accessibility", "Paid": "Paid", "Course Price": "Course Price", "Old Price": "Old Price", "Language": "Language", "Difficulty Level": "Difficulty Level", "Select Difficulty Level": "Select Difficulty Level", "Course Thumbnail": "Course Thumbnail", "Accepted image format & size": "Accepted image format & size", "Accepted image filetype": "Accepted image filetype", "Course Introduction Video": "Course Introduction Video", "Optional": "Optional", "Video Upload": "Video Upload", "Youtube Video": "Youtube Video", "write only video Id": "write only video Id", "Type your youtube video ID": "Type your youtube video ID", "Private": "Private", "Laravel For Beginners - Become A Laravel Master - CMS Project": "<PERSON>vel For Beginners - Become A Laravel Master - CMS Project", "Php For Beginners: Learn Everything You Need To Become A professional Php Developer With practical Exercises & Projects.": "Php For Beginners: Learn Everything You Need To Become A professional Php Developer With practical Exercises & Projects.", "Students": "Students", "Overview": "Overview", "Curriculum": "Curriculum", "Review": "Review", "Instructors": "Instructors", "What you will learn": "What you will learn", "Laravel Fundamental": "Laravel Fundamental", "Preview": "Preview", "Install Laravel From Scratch": "Install Laravel From Scratch", "Create First Project": "Create First Project", "Creating Our First Laravel Project": "Creating Our First Laravel Project", "How to setup Code Editor": "How to setup Code Editor", "How to setup VS Code for Laravel": "How to setup VS Code for Lara<PERSON>", "Reviews": "Reviews", "Meet Your Instructor": "Meet Your Instructor", "Rating": "Rating", "About Instructor": "About Instructor", "Course Duration": "Course Duration", "Course Level": "Course Level", "Student Enrolled": "Student Enrolled", "Enroll the Course": "Enroll the Course", "Add to Wishlist": "Add to Wishlist", "Share Course": "Share Course", "This Course Includes": "This Course Includes", "Video Lectures": "Video Lectures", "Quizzes": "Quizzes", "Assignments": "Assignments", "Downloadable Resources": "Downloadable Resources", "Full Lifetime Access": "Full Lifetime Access", "Certificate of Completion": "Certificate of Completion", "Share This Course": "Share This Course", "Student Enroll": "Student Enroll", "New Lesson Has Been Added": "New Lesson Has Been Added", "Course Category Has Been Updated": "Course Category Has Been Updated", "Course Overview Has Been Updated": "Course Overview Has Been Updated", "Showing": "Showing", "to": "to", "of": "of", "results": "results", "Course Lessons": "Course Lessons", "Course Lessons and Lectures": "Course Lessons and Lectures", "Enrolled Courses": "Enrolled Courses", "Validity": "Validity", "Revoke": "Revoke", "Lifetime": "Lifetime", "Are you sure to change status?": "Are you sure to change status?", "You won`t be able to revert this!": "You won`t be able to revert this!", "Yes, Change it!": "Yes, Change it!", "No, cancel!": "No, cancel!", "Enrollment status has been updated": "Enrollment status has been updated", "Approved Courses": "Approved Courses", "SCORM Course": "SCORM Course", "No Student Found": "No Student Found", "Affiliator Panel": "Affiliator Panel", "Browse More Course": "Browse More Course", "Sort By": "Sort By", "Select": "Select", "Newest": "Newest", "Oldest": "Oldest", "Author": "Author", "Order ID": "Order ID", "Progress": "Progress", "Give Review": "Give Review", "Invoice": "Invoice", "View": "View", "Write a Review": "Write a Review", "Feedback": "<PERSON><PERSON><PERSON>", "Please write your feedback here": "Please write your feedback here", "Cancel": "Cancel", "Submit Review": "Submit Review", "My Learning Courses": "My Learning Courses", "Student Profile": "Student Profile", "Personal Information": "Personal Information", "Phone": "Phone", "Email": "Email", "Address": "Address", "Location": "Location", "Total Enrolled Courses": "Total Enrolled Courses", "JavaScript: Understanding the Weird Parts": "JavaScript: Understanding the Weird Parts", "Angular - The Complete Guide": "Angular - The Complete Guide", "APIs in PHP: from Basic to Advanced": "APIs in PHP: from Basic to Advanced", "PHP for Beginners - Become a PHP Master - CMS Project": "PHP for Beginners - Become a PHP Master - CMS Project", "Python for Beginners - Learn Programming": "Python for Beginners - Learn Programming", "The Complete 2022 Web Development Bootcamp": "The Complete 2022 Web Development Bootcamp", "React + Next JS with TypeScript": "React + Next JS with TypeScript", "React - The Complete Guide (incl Hooks, React Router, Redux)": "React - The Complete Guide (incl Hooks, React Router, Redux)", "Microservices with Node JS and React": "Microservices with Node JS and React", "Docker & Kubernetes: The Practical Guide": "Docker & Kubernetes: The Practical Guide", "Advanced CSS and Sass: Flexbox, Grid": "Advanced CSS and Sass: Flexbox, Grid", "Become a Certified HTML, CSS, JavaScript Developer": "Become a Certified HTML, CSS, JavaScript Developer", "Learn C# Programming (In Ten Easy Steps)": "Learn C# Programming (In Ten Easy Steps)", "Mastering Data Structures & Algorithms using C and C++": "Mastering Data Structures & Algorithms using C and C++", "Competitive Programming Essentials, Master Algorithms 2022": "Competitive Programming Essentials, Master Algorithms 2022", "MERN Stack Doctor Appointment Booking App": "MERN Stack Doctor Appointment Booking App", "Ethical Hacking - Drip Content Course": "Ethical Hacking - Drip Content Course", "When you complete the course you'll be able to use APIs in your PHP applications to leverage third-party components and services. You'll also be able to create your own API, using various authentication techniques depending on the type of API you want to create.": "When you complete the course you'll be able to use APIs in your PHP applications to leverage third-party components and services. You'll also be able to create your own API, using various authentication techniques depending on the type of API you want to create.", "new Home": "new Home", "Copy to Clipboard": "Copy to Clipboard", "Added to cart": "Added to cart", "Cart": "<PERSON><PERSON>", "Items In Card": "Items In Card", "Continue Buying": "Continue Buying", "Remove": "Remove", "Coupon Code": "Coupon Code", "Apply": "Apply", "Order Summery": "Order Summery", "Platform Charge": "Platform Charge", "This is for using the platform and get support lifetime": "This is for using the platform and get support lifetime", "Proceed to Checkout": "Proceed to Checkout", "Cancel order": "Cancel order", "All student": "All student", "All Students": "All Students", "Country": "Country", "Total Course Enroll": "Total Course Enroll", "Blocked": "Blocked", "Student status has been updated": "Student status has been updated", "Add Student In Course": "Add Student In Course", "Select Student": "Select Student", "Select Course": "Select Course", "Expired After Days": "Expired After Days", "Course Enroll": "Course Enroll", "Student has already purchased the course!": "Student has already purchased the course!", "New student enrolled": "New student enrolled", "New course enrolled by Admin": "New course enrolled by Admin", "Student enroll in course": "Student enroll in course", "Best Seller": "Best Seller", "days left": "days left", "left": "left", "General Setting": "General Setting", "BigBlueButton Meeting Settings": "BigBlueButton Meeting Settings", "Jitsi Meeting Settings": "Jitsi Meeting Settings", "Google Meet Settings": "Google Meet Settings", "Social Login Settings": "Social Login Settings", "Cookie Settings": "<PERSON><PERSON>", "S3 Storage Settings": "S3 Storage Settings", "Vimeo Settings": "V<PERSON>o Settings", "Currency Settings": "<PERSON><PERSON><PERSON><PERSON>", "Meta Management": "Meta Management", "Site Share Content": "Site Share Content", "Geo Location Api Key": "Geo Location Api Key", "Color Settings": "Color Settings", "Font Settings": "Font Settings", "Device Control": "Device Control", "Private Mode": "Private Mode", "Subscription Mode": "Subscription Mode", "SaaS Mode": "SaaS Mode", "State": "State", "City": "City", "Section Settings": "Section Settings", "Banner Section": "Banner Section", "Special Feature Section": "Special Feature Section", "Course Section": "Course Section", "Bundle Course Section": "Bundle Course Section", "Top Category Section": "Top Category Section", "Top Instructor Section": "Top Instructor Section", "Become Instructor Video Section": "Become Instructor Video Section", "Customer Say Section": "Customer Say Section", "Achievement Section": "Achievement Section", "Bank": "Bank", "Instructor Feature": "Instructor Feature", "Instructor Procedure": "Instructor Procedure", "Instructor CMS": "Instructor CMS", "FAQ CMS": "FAQ CMS", "FAQ Tab": "FAQ Tab", "Question & Answer": "Question & Answer", "Support Ticket CMS": "Support Ticket CMS", "Support Ticket Department Field": "Support Ticket Department Field", "Support Ticket Priority Field": "Support Ticket Priority Field", "Support Ticket Related Service": "Support Ticket Related Service", "Gallery Area": "Gallery Area", "Upgrade Skills": "Upgrade Skills", "Team Member": "Team Member", "Instructor Support": "Instructor Support", "Client": "Client", "Others Settings": "Others Settings", "App Name": "App Name", "App Email": "App Email", "App Contact Number": "App Contact Number", "App Location": "App Location", "App Copyright": "App Copyright", "Developed By": "Developed By", "Date Format": "Date Format", "Default Currency": "<PERSON><PERSON><PERSON>", "Default Language": "Default Language", "Sell Commission": "Sell Commission", "Allow Preloader": "Allow Preloader", "Disable": "Disable", "Preloader": "Preloader", "App Logo": "App Logo", "App Fav Icon": "App Fav Icon", "App PWA Icon": "App PWA Icon", "PWA enable": "PWA enable", "Sign up Left Text": "Sign up Left Text", "Registration Email Verification": "Registration Email Verification", "Sign up Left Image": "Sign up Left Image", "Forgot Title": "Forgot Title", "Forgot Subtitle": "Forgot Subtitle", "Forgot Button Name": "Forgot <PERSON> Name", "Footer Quote": "<PERSON>er Quote", "Social Media Profile Link": "Social Media Profile Link", "Facebook URL": "Facebook URL", "Twitter URL": "Twitter URL", "LinkedIn URL": "LinkedIn URL", "Pinterest URL": "Pinterest URL", "Instagram URL": "Instagram URL", "Tiktok URL": "Tiktok URL", "Time Zone": "Time Zone", "Successfully Updated": "Successfully Updated", "Asia/Dhaka": "Asia/Dhaka", "sdf": "sdf", "df": "df", "sdfds": "sdfds", "dsf": "dsf", "Instructor Panel": "Instructor Panel", "Your Rank": "Your Rank", "List of Rank": "List of Rank", "Min Sale": "<PERSON>", "Max Sale": "Max Sale", "Instructor Request": "Instructor Request", "Live Class Course List": "Live Class Course List", "Course Name": "Course Name", "Upcoming Live Class": "Upcoming Live Class", "Past Live Class": "Past Live Class", "Create Live Class": "Create Live Class", "Create Live": "Create Live", "Live Class Topic": "Live Class Topic", "Live Class Date": "Live Class Date", "Time Duration (Write minutes)": "Time Duration (Write minutes)", "Meeting Host Name": "Meeting Host Name", "Moderator Password": "Moderator Password", "Attendee Password": "Attendee Password", "Create Meeting": "Create Meeting", "Live Class Create": "Live Class Create", "Jitsi Setting": "<PERSON><PERSON><PERSON>", "Jitsi Status": "Jitsi Status", "Jitsi Server Base URL": "Jitsi Server Base URL", "Jitsi Meeting ID/Room": "Jitsi Meeting ID/Room", "New Live Class Added": "New Live Class Added", "Live Class List": "Live Class List", "Upcoming": "Upcoming", "Past": "Past", "Empty Live Class": "Empty Live Class", "Topic": "Topic", "Date & Time": "Date & Time", "Time Duration": "Time Duration", "Add Live Class": "Add Live Class", "View Meeting": "View Meeting", "Meeting ID": "Meeting ID", "Start URL": "Start URL", "Start Now": "Start Now", "People Also Search": "People Also Search", "Filter": "Filter", "Search result for": "Search result for", "Result Found": "Result Found", "Default": "<PERSON><PERSON><PERSON>", "Newest Course": "Newest Course", "Oldest Course": "Oldest Course", "Higher": "Higher", "Medium": "Medium", "5 star": "5 star", "4 star or above": "4 star or above", "3 star or above": "3 star or above", "2 star or above": "2 star or above", "1 star or above": "1 star or above", "Min": "Min", "Max": "Max", "Duration": "Duration", "Less Than 24 Hours": "Less Than 24 Hours", "24 to 36 Hours": "24 to 36 Hours", "36 to 72 Hours": "36 to 72 Hours", "Above 72 Hours": "Above 72 Hours", "Load More": "Load More", "Your name *": "Your name *", "Your Email *": "Your Email *", "Web": "Web", "Google": "Google", "People also search": "People also search", "result found": "result found", "Newest course": "Newest course", "Oldest course": "Oldest course", "Subcategory Courses": "Subcategory Courses", "Are you available for 1 to 1 consultation?": "Are you available for 1 to 1 consultation?", "Yes": "Yes", "No": "No", "Available type for 1 to 1 consultation?": "Available type for 1 to 1 consultation?", "Both": "Both", "Consultancy Area": "Consultancy Area", "In City": "In City", "All Over The World": "All Over The World", "Hourly Rate": "Hourly Rate", "Hourly Old Rate": "Hourly Old Rate", "Offline Status": "Offline Status", "Offline Message": "Offline Message", "Message": "Message", "If you put your account offline, a message will be displayed in your profile and it will be noticed to users. You can type a personalized message in the following input.": "If you put your account offline, a message will be displayed in your profile and it will be noticed to users. You can type a personalized message in the following input.", "Is Subscription Enable": "Is Subscription Enable", "Your Time Slot List": "Your Time Slot List", "Days": "Days", "Saturday": "Saturday", "Add Slot": "Add Slot", "Off day": "Off day", "Sunday": "Sunday", "Monday": "Monday", "On day": "On day", "Tuesday": "Tuesday", "Wednesday": "Wednesday", "Thursday": "Thursday", "Friday": "Friday", "Time": "Time", "Start Time": "Start Time", "End Time": "End Time", "Consultation Dashboard": "Consultation Dashboard", "No Record Found": "No Record Found", "No Schedule Found": "No Schedule Found", "Your Meeting Details": "Your Meeting Details", "Meeting Date & Time": "Meeting Date & Time", "Total Duration": "Total Duration", "Total Cost": "Total Cost", "Status Change Successfully": "Status Change Successfully", "Consultation added to cart": "Consultation added to cart", "Removed from cart list!": "Removed from cart list!", "Checkout": "Checkout", "Billing Address": "Billing Address", "First Name": "First Name", "Last Name": "Last Name", "Email Address": "Email Address", "Zip Code": "Zip Code", "Zip code": "Zip code", "Type your phone number": "Type your phone number", "You will be redirected to the PayPal website after submitting your order": "You will be redirected to the PayPal website after submitting your order", "Pay with Credit Card": "Pay with Credit Card", "Card Number": "Card Number", "1234 5678 9101 3456": "1234 5678 9101 3456", "Card Security Code": "Card Security Code", "Type your security code": "Type your security code", "Expiration Month": "Expiration Month", "Expiration Year": "Expiration Year", "Select One": "Select One", "Account Number": "Account Number", "Deposit By": "Deposit By", "Deposit Slip": "<PERSON><PERSON><PERSON><PERSON>", "We protect your payment information using encryption to provide bank-level security": "We protect your payment information using encryption to provide bank-level security", "Order Review": "Order Review", "Billing Summary": "Billing Summary", "Subtotal": "Subtotal", "Discount": "Discount", "Grand Total": "Grand Total", "Conversion Rate": "Conversion Rate", "Pay": "Pay", "Something went wrong!": "Something went wrong!", "Paypal payment gateway is off!": "Paypal payment gateway is off!", "New course enrolled pending request": "New course enrolled pending request", "Request has been Placed! Please Wait for Approve": "Request has been Placed! Please Wait for Approve", "Thank you for Purchasing": "Thank you for Purchasing", "New Enrolled": "New Enrolled", "Browse More Consultation": "Browse More Consultation", "Pending": "Pending", "Approve": "Approve", "Completed": "Completed", "Order Id": "Order Id", "How to Join The Call?": "How to Join The Call?", "If you want to join online call. You need to join below meeting host.": "If you want to join online call. You need to join below meeting host.", "My Consultation Courses": "My Consultation Courses", "All Booking Request": "All Booking Request", "Student Name": "Student Name", "Date": "Date", "Cancel Reason": "Cancel Reason", "Are you sure?": "Are you sure?", "All Booking History": "All Booking History", "Cancelled": "Cancelled", "Order Report Pending": "Order Report Pending", "Order Number": "Order Number", "Sub total": "Sub total", "Payment Method & Details": "Payment Method & Details", "Total Admin Commission": "Total Admin Commission", "Total Instructor Commission": "Total Instructor Commission", "Payment Currency": "Payment Currency", "Payment": "Payment", "Deposit Bank Name": "Deposit Bank Name", "Download": "Download", "Pending Order Report List": "Pending Order Report List", "Your new course has been approved and added.": "Your new course has been approved and added.", "In-person": "In-person", "Student Details": "Student Details", "Phone Number": "Phone Number", "Instructor Details": "Instructor <PERSON>", "Instructor Consultation": "Instructor Consultation", "Newest Instructor": "Newest Instructor", "Oldest Instructor": "Oldest Instructor", "Search Instructor Name": "Search Instructor Name", "Search": "Search", "": "", "If you want to join online call. You need to create link using below meeting host.": "If you want to join online call. You need to create link using below meeting host.", "BigBlueButton": "BigBlueButton", "Jitsi": "<PERSON><PERSON><PERSON>", "Type moderator password (min length 6)": "Type moderator password (min length 6)", "Type attendee password (min length 6)": "Type attendee password (min length 6)", "Save Meeting": "Save Meeting", "Meeting Create Successfully": "Meeting Create Successfully", "Start Meeting Now": "Start Meeting Now", "Submit Process": "Submit Process", "Upload lesson": "Upload lesson", "Add More Section": "Add More Section", "Edit Lesson Name": "<PERSON> <PERSON>on Name", "Youtube": "Youtube", "Text": "Text", "PDF": "PDF", "Slide Document": "Slide Document", "Audio": "Audio", "Lesson Youtube Video ID": "Lesson Youtube Video ID", "Type Your Youtube Video ID": "Type Your Youtube Video ID", "Vimeo Upload Type": "Vimeo Upload Type", "Video File Upload": "Video File Upload", "Vimeo Uploaded Video ID": "Vimeo Uploaded Video ID", "Uploaded Video ID": "Uploaded Video ID", "Type your uploaded video ID (ex: 123654)": "Type your uploaded video ID (ex: 123654)", "Lesson File Duration": "Lesson File Duration", "Type file duration": "Type file duration", "Lesson Description": "Lesson Description", "Lesson Image": "Lesson Image", "Preferable image size:": "Preferable image size:", "Preferable filetype:": "Preferable filetype:", "Upload PDF": "Upload PDF", "Write your Slide Embed Code": "Write your Slide Embed Code", "Upload Audio": "Upload Audio", "Lesson Title": "Lesson Title", "First steps": "First steps", "Learner's Visibility": "<PERSON>rner's Visibility", "Show": "Show", "Lock": "Lock", "First file duration": "First file duration", "Upload Lecture": "Upload Lecture", "Video": "Video", "Preview Video": "Preview Video", "No file selected": "No file selected", "Preferable image size": "Preferable image size", "Preferable filetype": "Preferable filetype", "Write Your Slide Embed Code": "Write Your Slide Embed Code", "Learners Visibility": "Learners Visibility", "Edit Lecture": "Edit Lecture", "Ultimate Renewable Energy Bundle Course for Beginners": "Ultimate Renewable Energy Bundle Course for Beginners", "Courses list": "Courses list", "Add to Cart": "Add to Cart", "Node.js, Express, MongoDB & More": "Node.js, Express, MongoDB & More", "Johnny Depp": "<PERSON>", "PHP Developer": "PHP Developer", "Freelancers and entrepreneurs Freelancers and entrepreneurs use about.me to grow their audience and get more clients. · Create a page to present who you are and what you do in one link.use about.me to grow their audience and get more clients. · Create a page to present who you are and what you do in one link.": "Freelancers and entrepreneurs Freelancers and entrepreneurs use about.me to grow their audience and get more clients. · Create a page to present who you are and what you do in one link.use about.me to grow their audience and get more clients. · Create a page to present who you are and what you do in one link.", "Enroll the bundle": "Enroll the bundle", "Add to wishlist": "Add to wishlist", "Share bundle": "Share bundle", "Share this bundle": "Share this bundle", "Bundle Details": "Bundle Details", "Bitcoin & Ethereum CryptoCurrency Course (2 Course Bundle)": "Bitcoin & Ethereum CryptoCurrency Course (2 Course Bundle)", "Policy": "Policy", "Description": "Description", "Refund Policy": "Refund Policy", "Menu List": "Menu List", "Static Menu List": "Static Menu List", "Page Name": "Page Name", "Meta Content": "Meta Content", "Meta Title": "Meta Title", "Meta Description": "Meta Description", "Meta Keywords": "Meta Keywords", "Edit Meta": "<PERSON>", "Separate with commas": "Separate with commas", "meta description": "meta description", "meta keywords": "meta keywords", "Created Successful": "Created Successful", "All Page": "All Page", "Page List": "Page List", "gfjhgj": "gfjhgj", "Edit Page": "Edit Page", "Page successfully updated": "Page successfully updated", "Expired": "Expired", "Renew": "<PERSON>w", "Your Subscription Enable Course Create limit has been finish.": "Your Subscription Enable Course Create limit has been finish.", "gfhgfhgf": "gfhgfhgf", "sdff": "sdff", "Edit Section": "Edit Section", "Updated successful.": "Updated successful.", "SL": "SL", "Section Name": "Section Name", "Special Feature Area": "Special Feature Area", "Courses Area": "Courses Area", "Bundle Area": "Bundle Area", "Top Category Area": "Top Category Area", "Consultation Area": "Consultation Area", "Instructor Area": "Instructor Area", "Video Area": "Video Area", "Customer Says Area": "Customer Says Area", "Achievement Area": "Achievement Area", "FAQ Area": "FAQ Area", "Instructor Support Area": "Instructor Support Area", "Subscription list area": "Subscription list area", "SaaS List area": "SaaS List area", "Section status has been changed": "Section status has been changed", "Mini Title": "Mini Title", "First Line Title": "First Line Title", "Second Line Title": "Second Line Title", "Second Line Changeable Word Title": "Second Line Changeable Word Title", "Third Line Title": "Third Line Title", "Subtitle": "Subtitle", "First Button Name": "First Button Name", "First Button Link": "First Button Link", "Second Button Name": "Second Button Name", "Second Button Link": "Second Button Link", "Banner Image": "Banner Image", "Area Code": "Area Code", "Select Code": "Select Code", "Phone number": "Phone number", "Postal Code": "Postal Code", "Select Country": "Select Country", "Select State": "Select State", "Select City": "Select City", "Gender": "Gender", "Male": "Male", "Female": "Female", "Others": "Others", "About Student": "About Student", "Accepted Image Files": "Accepted Image Files", "Accepted Size": "Accepted <PERSON><PERSON>", "Select Your Picture": "Select Your Picture", "Type your email": "Type your email", "Bio": "Bio", "Type about yourself": "Type about yourself", "Save Profile Now": "Save Profile Now", "Type your address": "Type your address", "Lat": "Lat", "Long": "<PERSON>", "Select Location": "Select Location", "No skills to show": "No skills to show", "Certifications": "Certifications", "No certificate to show": "No certificate to show", "Awards": "Awards", "No award to show": "No award to show", "My courses": "My courses", "Following": "Following", "(Ranking)": "(Ranking)", " Meetings": " Meetings", "The complete Scorm demo course - web design & development": "The complete Scorm demo course - web design & development", "Master Node by building a real-world RESTful API and web app (with authentication, Node.js security, payments & more)": "Master Node by building a real-world RESTful API and web app (with authentication, Node.js security, payments & more)", "Welcome to the Course dsff": "Welcome to the Course dsff", "inndfd": "inndfd", "hfgjvvjyf": "hfgjvvjyf", "No Discussion Found": "No Discussion Found", "pagination.previous": "pagination.previous", "pagination.next": "pagination.next", "Preview Slide": "Preview Slide", "Preview PDF": "Preview PDF", "Preview Image": "Preview Image", "In this course you will gain a deep understanding of Javascript, learn how Javascript works under the hood, and how that knowledge helps you avoid common pitfalls and drastically improve your ability to debug problems. You will find clarity in the parts that others, even experienced coders, may find weird, odd, and at times incomprehensible.": "In this course you will gain a deep understanding of Javascript, learn how Javascript works under the hood, and how that knowledge helps you avoid common pitfalls and drastically improve your ability to debug problems. You will find clarity in the parts that others, even experienced coders, may find weird, odd, and at times incomprehensible.", "Section Intro": "Section Intro", "Slide document": "Slide document", "JavaScript Fundamentals": "JavaScript Fundamentals", "Values and Variables": "Values and Variables", "Block label variables": "Block label variables", "This is pdf document": "This is pdf document", "Developer Skills": "Developer Skills", "Data Types": "Data Types", "let, const and var": "let, const and var", "Javscript Scope": "Javscript <PERSON>", "Editor Setup": "Editor Setup", "Basic Operators": "Basic Operators", "Operator Precedence": "Operator Precedence", "This is audio tutorial": "This is audio tutorial", "HTML & CSS Crash Course": "HTML & CSS Crash Course", "Truthy and Falsy Values": "Truthy and Falsy Values", "Boolean Logic": "Boolean Logic", "Youtube video": "Youtube video", "Lectures": "Lectures", "sections": "sections", "Course Content": "Course Content", "Introduction": "Introduction", "Enrolled": "Enrolled", "Write a review": "Write a review", "Last update": "Last update", "Notice": "Notice", "Empty Resources": "Empty Resources", "Empty Quiz": "Empty Quiz", "Empty Notice": "Empty Notice", "Empty Notice Board": "Empty Notice Board", "Empty Upcoming Class": "Empty Upcoming Class", "Empty Past Class": "Empty Past Class", "Start a Conversation": "Start a Conversation", "Share tips and shortcuts or simply start a discussion about this class ....": "Share tips and shortcuts or simply start a discussion about this class ....", "Post": "Post", "After completing the course, you will receive a certificate.": "After completing the course, you will receive a certificate.", "Submit review": "Submit review", "Empty Assignment": "Empty Assignment", "Reply": "Reply", "Go to Course": "Go to Course", "Lesson has been updated": "Lesson has been updated", "Quiz Name": "Quiz Name", "Quiz Types": "Quiz Types", "Total Question": "Total Question", "minutes": "minutes", "See Result": "See Result", "Leaderboard": "Leaderboard", "Meeting Link": "Meeting Link", "Assignment Topic": "Assignment Topic", "Marks": "Marks", "View Details": "View Details", "Sign Up": "Sign Up", "Updated Successfully": "Updated Successfully", "Updated Successful": "Updated Successful", "Python For Beginners : This course is meant for absolute beginners in programming or in python.": "Python For Beginners : This course is meant for absolute beginners in programming or in python.", "User Model": "User Model", "Authorization and Protected Routes": "Authorization and Protected Routes", "User Registration": "User Registration", "Redux toolkit setup": "Redux toolkit setup", "Applying for Doctor": "Applying for Doctor", "Alert messages and loaders": "Alert messages and loaders", "Seperate Menu for User and Admin": "Seperate Menu for User and Admin", "Notifications": "Notifications", "Apply Doctor API": "Apply Doctor API", "Display All Doctors": "Display All Doctors", "Update Doctor Profile": "Update Doctor Profile", "Booking Screen UI": "Booking Screen UI", "Your Course Create limit has been finish.": "Your Course Create limit has been finish.", "See All Courses": "See All Courses", "RECORDED COURSE": "RECORDED COURSE", "Offline": "Offline", "recorded": "recorded", "course": "course", "All": "All", "Offline course": "Offline course", "Buy Now": "Buy Now", "SCORM": "SCORM", "Recorded": "Recorded", "LIVE CLASS": "LIVE CLASS", "live class": "live class", "FREE COURSE": "FREE COURSE", "Offline and Online ": "Offline and Online ", "CORE FEATURES": "CORE FEATURES", "Amazing": "Amazing", "features": "features", "you will get": "you will get", "TESTIMONIAL": "TESTIMONIAL", "Our": "Our", "achievements": "achievements", "from students": "from students", "NEWSLETTER": "NEWSLETTER", "To get daily updated news from us subscribe our": "To get daily updated news from us subscribe our", "newsletter": "newsletter", "Frequently": "Frequently", "asked question": "asked question", "Refund policy": "Refund policy", "Privacy policy": "Privacy policy", "Terms & condition": "Terms & condition", "Quick Links": "Quick Links", "Live class": "Live class", "Student journey": "Student journey", "Copyright 2022 Zainik Lab Academy": "Copyright 2022 Zainik Lab Academy", "All rights reserved.": "All rights reserved.", "Class Begin": "Class Begin", "Class Day": "Class Day", "Class Time": "Class Time", "Fb group member": "Fb group member", "Fb Group Name": "Fb Group Name", "Fb group link": "Fb group link", "Course Description Sections": "Course Description Sections", "Already have an account?": "Already have an account?", "Code": "Code", "Your registration is successful.": "Your registration is successful.", "Build your Bran": "Build your <PERSON>ran", "Serenity has taken possession of my entire soul, like these sweet mornings of spring which I enjoy with": "Serenity has taken possession of my entire soul, like these sweet mornings of spring which I enjoy with", "Inspire learners": "Inspire learners", "Get rewarded": "Get rewarded", "Plan Your Curriculum": "Plan Your Curriculum", "Serenity has taken possession of my entire soul, like these sweet mornings spring which I enjoy with my whole heart. I am alone, and feel the charm existence in this spot, which was created for the bliss of souls like mine so happy, my dear friend, so absorbed in the exquisite sense of mere tranquil existence, that I neglect my talents.": "Serenity has taken possession of my entire soul, like these sweet mornings spring which I enjoy with my whole heart. I am alone, and feel the charm existence in this spot, which was created for the bliss of souls like mine so happy, my dear friend, so absorbed in the exquisite sense of mere tranquil existence, that I neglect my talents.", "Enrollments": "Enrollments", "Satisfaction": "Satisfaction", "Join One Of The World’s Largest Learning Marketplaces.": "Join One Of The World’s Largest Learning Marketplaces.", "Donald valley teems with vapour around me, and the meridian sun strikes the upper surface of the impenetrable foliage of my tree": "Donald valley teems with vapour around me, and the meridian sun strikes the upper surface of the impenetrable foliage of my tree", "Submit your application": "Submit your application", "Account Type": "Account Type", "Professional Title": "Professional Title", "New organization request": "New organization request", "Request successfully send": "Request successfully send", "Request Pending": "Request Pending", "CV": "CV", "Pending for Review": "Pending for Review", "You Don't have default SAAS Package For Instructor": "You Don't have default SAAS Package For Instructor", "Manage SaaS Packages": "Manage SaaS Packages", "SaaS Packages": "SaaS Packages", "All SaaS Package": "All SaaS Package", "Icon": "Icon", "Admin Comission": "Admin Comission", "Consultancy": "Consultancy", "SaaS status has been updated": "SaaS status has been updated", "Edit SaaS Packages": "Edit SaaS Packages", "Update Saas Package": "Update Saas Package", "SaaS Type": "SaaS Type", "Price Monthly": "Price Monthly", "Discounted Price Monthly": "Discounted Price Monthly", "Price Yearly": "Price Yearly", "Discounted Price Yearly": "Discounted Price Yearly", "Admin Commission": "Admin Commission", "Student Limit": "Student Limit", "student Limit": "student Limit", "Instructor Limit": "<PERSON><PERSON><PERSON><PERSON>", "Course Limit": "Course Limit", "Bundle Course Limit": "Bundle Course Limit", "Subscription Course Limit": "Subscription Course Limit", "Consulaltency Limit": "Consulaltency Limit", "Show in Home": "Show in Home", "YES": "YES", "NO": "NO", "Order": "Order", "Add SaaS Packages": "Add SaaS Packages", "Add SaaS Package": "Add SaaS Package", "Show In Home": "Show In Home", "Application Setting": "Application Setting", "SaaS Mode Settings": "SaaS Mode Settings", "If SaaS mode is enable then all instructor and organization have to use any of the SaaS package.": "If SaaS mode is enable then all instructor and organization have to use any of the SaaS package.", "SaaS": "SaaS", "Default SAAS for Instructor": "Default SAAS for Instructor", "Default SAAS for Organization": "Default SAAS for Organization", "Status has been changed": "Status has been changed", "Professional_title": "Professional_title", "Approved Instructor": "Approved Instructor", "Total Showing: 12": "Total Showing: 12", "Meeting Types": "Meeting Types", "In person": "In person", "Empty Course": "Empty Course", "SaaS panel": "SaaS panel", "SaaS Package Checkout": "SaaS Package Checkout", "Payment has been completed": "Payment has been completed", "Subscribe": "Subscribe", "Thank you for Subscribing": "Thank you for Subscribing", "App Footer Payment Banner": "App Footer Payment Banner", "No Course Found": "No Course Found", "Login": "<PERSON><PERSON>", "Coming Soon Mode": "Coming Soon Mode", "Web Development": "Web Development", "Health & Fitness": "Health & Fitness", "No Subcategory Found": "No Subcategory Found", "Courses Not Found": "Courses Not Found", "Agora Settings": "<PERSON><PERSON><PERSON>", "Re-Captcha Key": "Re-<PERSON><PERSON>", "Google Analytics": "Google Analytics", "Registration Bonus": "Registration Bonus", "Refund System": "Refund System", "Cashback System": "Cashback System", "Course Gift System": "Course Gift System", "Chat System": "Chat System", "Wallet Checkout System": "Wallet Checkout System", "Wallet Recharge": "Wallet Recharge", "Category Course Section": "Category Course Section", "Upcoming Course Section": "Upcoming Course Section", "© 2023 InverseSchool. All Rights Reserved.": "© 2023 InverseSchool. All Rights Reserved.", "Tanha": "<PERSON><PERSON>", "Mirpur DOHS": "Mirpur DOHS", "01854043400": "01854043400", "Country Setting": "Country Setting", "Add Country": "Add Country", "Short Name": "Short Name", "Phone Code": "Phone Code", "Continent": "Continent", "Type country name": "Type country name", "Type short name": "Type short name", "Type phone code": "Type phone code", "Type continent": "Type continent", "You have the latest version of this app.": "You have the latest version of this app.", "System Details": "System Details", "Current Version": "Current Version", "PHP Version": "PHP Version", "LMSZAI Official Addons": "LMSZAI Official Addons", "Version Update Execute": "Version Update Execute", "Organizations": "Organizations", "Organization status has been updated": "Organization status has been updated", "Category List": "Category List", "Add Category": "Add Category", "Feature": "Feature", "Manage Category": "Manage Category", "Added_date": "Added_date", " Certificates": " Certificates", "Chat": "Cha<PERSON>", "Type to search for solutions...": "Type to search for solutions...", "or": "or", "Ask a Question": "Ask a Question", "Forum Categories": "Forum Categories", "Post Topic": "Post Topic", "Answers": "Answers", "Members": "Members", "Recent Discussions": "Recent Discussions", "Top Contributors": "Top Contributors", "Community Blog Articles": "Community Blog Articles", "All Blogs": "All Blogs", "Topic Title": "Topic Title", "Enter your topic title": "Enter your topic title", "Select a Category": "Select a Category", "Publish Question": "Publish Question", "Forum Ask Question": "Forum Ask Question", "Auto Content Approval": "Auto Content Approval", "Are you sure to change?": "Are you sure to change?", "Auto content status has been updated": "Auto content status has been updated", "Facebook": "Facebook", "Twitter": "Twitter", "Linkedin": "Linkedin", "Pinterest": "Pinterest", "Withdraw Request": "Withdraw Request", "Transaction ID": "Transaction ID", "User": "User", "Note": "Note", "New Withdraw Request": "New Withdraw Request", "Refund Request": "Refund Request", "Refund Amount": "Refund Amount", "Reason": "Reason", "Please write your refund request reason": "Please write your refund request reason", "Wallet Dashboard": "Wallet Dashboard", "Request a Withdrawal": "Request a Withdrawal", "My Beneficiary": "My Beneficiary", "Transaction history": "Transaction history", "Withdrawal history": "Withdrawal history", "Hash": "Hash", "Transaction Id": "Transaction Id", "Available Balance": "Available Balance", "Type amount": "Type amount", "Beneficiary": "Beneficiary", "Make Withdraw": "Make Withdraw", "Recharge": "Recharge", "Wallet": "Wallet", "Chat Box": "Chat Box", "Instructors with courses": "Instructors with courses", "Instructor status has been updated": "Instructor status has been updated", "Refund List": "Refund List", "Empty Course Live Class": "Empty Course Live Class", "Zoom API key": "Zoom API key", "Zoom API Secret": "Zoom API Secret", "Timezone": "Timezone", "Host Video": "Host Video", "Participant Video": "Participant Video", "Waiting Room": "Waiting Room", "Zoom Setting": "Zoom <PERSON>ting", "Students with enrolled courses": "Students with enrolled courses", "Payment Method Setting": "Payment Method Setting", "PayPal": "PayPal", "Stripe": "Stripe", "Currency ISO Code": "Currency ISO Code", "PayPal Mode": "PayPal Mode", "Sandbox": "Sandbox", "Live": "Live", "PayPal Client ID": "PayPal Client ID", "PayPal Secret": "PayPal Secret", "Stripe Mode": "Stripe Mode", "Stripe Public Key": "Stripe Public Key", "Stripe Secret Key": "Stripe Secret Key", "RAZORPAY": "RAZORPAY", "SSLCOMMERZ": "SSLCOMMERZ", "RAZORPAY KEY": "RAZORPAY KEY", "RAZORPAY SECRET": "RAZORPAY SECRET", "Sslcommerz Mode": "Sslcommerz Mode", "Sslcommerz Store ID": "Sslcommerz Store ID", "Sslcommerz store password": "Sslcommerz store password", "Mollie": "<PERSON><PERSON>", "Instamojo": "Instamojo", "MOLLIE KEY": "MOLLIE KEY", "API KEY": "API KEY", "AUTH TOKEN": "AUTH TOKEN", "Payment Mode": "Payment Mode", "Paystack": "Paystack", "Mercado PAGO": "Mercado PAGO", "Paystack Public Key": "Paystack Public Key", "Paystack Secret Key": "Paystack Secret Key", "Mercado Client ID": "Mercado Client ID", "Mercado Client Secret": "Mercado Client Secret", "Flutterwave": "Flutterwave", "Coinbase": "Coinbase", "Flutterwave Public Key": "Flutterwave Public Key", "Flutterwave Secret Key": "Flutterwave Secret Key", "Flutterwave Secret Hash": "Flutterwave Secret Hash", "Coinbase Mode": "Coinbase Mode", "Coinbase Key": "Coinbase Key", "S3 Storage Setting": "S3 Storage Setting", "Video Storage Driver": "Video Storage Driver", "AWS Access Key ID": "AWS Access Key ID", "AWS Secret Access Key": "AWS Secret Access Key", "AWS Default Region": "AWS Default Region", "AWS Bucket": "AWS Bucket", "WAS Access Key ID": "WAS Access Key ID", "WAS Secret Access Key": "WAS Secret Access Key", "WAS Default Region": "WAS Default Region", "WAS Bucket": "WAS Bucket", "VULTR Access Key": "VULTR Access Key", "VULTR Secret Key": "VULTR Secret Key", "VULTR Region": "VULTR Region", "VULTR Bucket": "VULTR Bucket", "Google Meet Setting": "Google Meet Setting", "Gmeet Status": "Gmeet Status", "Gmeet CLIENT ID": "Gmeet CLIENT ID", "Gmeet CLIENT SECRET": "Gmeet CLIENT SECRET", "Agora Setting": "<PERSON><PERSON><PERSON>", "Agora Status": "Agora Status", "Agora App ID": "Agora App ID", "Agora App Certificate": "Agora App Certificate", "Device Control Settings": "Device Control Settings", "Instructions": "Instructions", "If device control on it will restrict student to login more than the limited devices": "If device control on it will restrict student to login more than the limited devices", "Device limit": "Device limit", "Subscription Mode Settings": "Subscription Mode Settings", "If subscription mode is active then by default all student have to use the subscription package.": "If subscription mode is active then by default all student have to use the subscription package.", "Subscription": "Subscription", "Default Subscription Type": "Default Subscription Type", "BigBlueButton Setting": "BigBlueButton Setting", "BBB Status": "BBB Status", "BBB SECURITY SALT": "BBB SECURITY SALT", "BBB SERVER BASE URL": "BBB SERVER BASE URL", "Manage Subject": "Manage Subject", "Manage Exam": "Manage Exam", "Manage Chapter": "Manage Chapter", "Finance & Accounting": "Finance & Accounting", "Review Courses": "Review Courses", "Review Pending Courses": "Review Pending Courses", "Coupon Code Name": "Coupon Code Name", "Type coupon code name": "Type coupon code name", "Start Date": "Start Date", "End Date": "End Date", "Coupon Type": "Coupon Type", "Global": "Global", "Minimum Amount to Apply Coupon": "Minimum Amount to Apply Coupon", "Type minimum amount": "Type minimum amount", "Create Coupon": "Create Coupon", "Add Subject": "Add Subject", "Subject Name": "Subject Name", "Type Subject Name": "Type Subject Name", "Acadymic Type": "Acadymic Type", "Settings": "Settings", "Add Language": "Add Language", "Flag": "Flag", "ISO Code": "ISO Code", "RTL": "RTL", "Translator": "Translator", "Subject successfully Created": "Subject successfully Created", "Edit Subject": "Edit Subject", "Subject successfully Updated": "Subject successfully Updated", "Subject cannot deleted": "Subject cannot deleted", "Subject successfully deleted": "Subject successfully deleted", "Hold Courses": "Hold Courses", "Add Chapter": "Add Chapter", "Chapter Name": "Chapter Name", "Type Chapter Name": "Type Chapter Name", "Chapter successfully Created": "Chapter successfully Created", "Edit Chapter": "Edit Chapter", "Manage Chaper": "Manage Chaper", "Chapter successfully Updated": "Chapter successfully Updated", "Manage Institues": "Manage Institues", "Edit Institue": "Edit Institue", "Manage Institutes": "Manage Institutes", "Create Institute": "Create Institute", "Add Special Promotion Tag": "Add Special Promotion Tag", "Institute updated successfully.": "Institute updated successfully", "Manage Units": "Manage Units", "Create Unit": "Create Unit", "Unit created successfully": "Unit created successfully", "Unit updated successfully": "Unit updated successfully", "Manage Question": "Manage Question", "Follow": "Follow", "Create Question": "Create Question", "Manage Questions": "Manage Questions", "Add Question": "Add Question", "Academy Type ": "Academy Type ", "Select Academy Type": "Select Academy Type", "Subject ": "Subject ", "Select Subject Name": "Select Subject Name", "Chapter ": "Chapter ", "Select Chapter Name": "Select Chapter Name", "Academy Type": "Academy Type", "Subject": "Subject", "Question Type": "Question Type", " Difficulty Level ": " Difficulty Level ", "marks": "marks", "Whrite Question": "Whrite Question", "Option 1": "Option 1", "Option 2": "Option 2", "Option 3": "Option 3", "Option 4": "Option 4", "Correct Option ": "Correct Option ", "Hins - If Have": "Hins - If Have", "Question Detail (If any)": "Question Detail (If any)", "Solution (If any)": "Solution (If any)", "Institute Id": "Institute Id", "Select Institute ": "Select Institute ", "Select Question Type": "Select Question Type", "Unit": "Unit", "Select Unit": "Select Unit", "Select Unit Name": "Select Unit Name", "Years": "Years", "Board Name": "Board Name", "Board Year": "Board Year", "Question saved successfully!": "Question saved successfully!", "Details Question": "Details Question", "Featured": "Featured", "Feature has been updated": "Feature has been updated", "Edit Question": "Edit Question", "Are you sure to delete question?": "Are you sure to delete question?", "No!": "No!", "Question has been deleted": "Question has been deleted", "Successfully Saved": "Successfully Saved", "Question update successfully!": "Question update successfully!", "Update Password": "Update Password", "Old Password": "Old Password", "New Password": "New Password", "Empty Consultation": "Empty Consultation", "My Exam Module": "My <PERSON><PERSON>", "Add Your Bundles Courses": "Add Your Bundles Courses", "Create Bundles Courses": "Create Bundles Courses", "Empty Bundles Courses": "Empty Bundles Courses", "Bundle Courses": "Bundle Courses", "Search By Name": "Search By Name", "Search by Name": "Search by Name", "Search By Course": "Search By Course", "Empty Student": "Empty Student", "Student Information": "Student Information", "Course Join Date": "Course Join Date", "Notice Board Course List": "Notice Board Course List", "Empty Course Notice": "Empty Course Notice", "Manage Certificate": "Manage Certificate", "Empty Certificate": "Empty Certificate", "Requested By": "Requested By", "Share (%)": "Share (%)", "Empty Request": "Empty Request", "Category Courses Area": "Category Courses Area", "Upcoming Courses Area": "Upcoming Courses Area", "Send Test Mail": "Send Test Mail", "MAIL DRIVER": "MAIL DRIVER", "MAIL HOST": "MAIL HOST", "MAIL PORT": "MAIL PORT", "MAIL USERNAME": "MAIL USERNAME", "MAIL PASSWORD": "MAIL PASSWORD", "MAIL ENCRYPTION": "MAIL ENCRYPTION", "MAIL FROM ADDRESS": "MAIL FROM ADDRESS", "MAIL FROM NAME": "MAIL FROM NAME", "Test Mail": "Test Mail", "Recipient": "Recipient", "Recipient Mail": "Recipient Mail", "Your Message": "Your Message", "Hi, This is a test mail": "Hi, This is a test mail", "Send": "Send", "Close": "Close", "Logo": "Logo", "Recommended Size": "Recommended <PERSON><PERSON>", "Type title": "Type title", "Type subtitle": "Type subtitle", "Main Image": "Main Image", "JPG,PNG, JPEG": "JPG,PNG, JPEG", "Image Title": "Image Title", "Ticket Title": "Ticket Title", "Ticket Subtitle": "Ticket Subtitle", "Gallery Area Title": "Gallery Area Title", "Type gallery area title": "Type gallery area title", "Gallery Area Subtitle": "Gallery Area Subtitle", "Gallery First Image": "Gallery First Image", "Gallery Second Image": "Gallery Second Image", "Gallery Third Image": "Gallery Third Image", "Contact Us CMS": "Contact Us CMS", "Get In Touch Title": "Get In Touch Title", "Send Us Msg Title": "Send Us Msg Title", "Type location": "Type location", "Email One": "Email One", "Email Two": "Email Two", "Contact Number One": "Contact Number One", "Type phone one": "Type phone one", "Contact Number Two": "Contact Number Two", "Type phone two": "Type phone two", "Location Map Link": "Location Map Link", "Type map link": "Type map link", "Maintenance Mode Settings": "Maintenance Mode Settings", "Maintenance On": "Maintenance On", "Maintenance Mode Secret Key": "Maintenance Mode Secret Key", "Maintenance Mode Url": "Maintenance Mode Url", "Coming Soon Mode Settings": "Coming Soon Mode Settings", "Coming Soon On": "Coming Soon On", "Coming Soon Mode Secret Key": "Coming Soon Mode Secret Key", "Launch Time": "Launch Time", "Coming Soon Mode Page Title": "Coming Soon Mode Page Title", "Coming Soon Mode Page Description": "Coming Soon Mode Page Description", "Coming Soon Mode Url": "Coming Soon Mode Url", "Clear View Cache": "Clear View Cache", "Click Here": "Click Here", "Clear Route Cache": "Clear Route Cache", "Clear Config Cache": "Clear Config Cache", "Application Clear Cache": "Application Clear Cache", "Migrate Database (If needed for update database)": "Migrate Database (If needed for update database)", "Social Login Setting": "Social Login Setting", "Google Credentials": "Google Credentials", "Google Login Status": "Google Login Status", "Select option": "Select option", "Google Client ID": "Google Client ID", "Google Client Secret": "Google Client Secret", "Google Redirect URL": "Google Redirect URL", "Facebook Credentials": "Facebook Credentials", "Facebook Login Status": "Facebook Login Status", "Facebook Client ID": "Facebook Client ID", "Facebook Client Secret": "Facebook Client Secret", "Facebook Redirect URL": "Facebook Redirect URL", "Twitter Credentials": "Twitter Credentials", "Twitter Login Status": "Twitter Login Status", "Twitter Client ID": "Twitter Client ID", "Twitter Client Secret": "Twitter Client Secret", "Twitter Redirect URL": "Twitter Redirect URL", "Cookie Setting": "<PERSON><PERSON>", "Cookie Message": "<PERSON><PERSON>", "Cookie Agree Button Name": "<PERSON><PERSON> Name", "Cookies Status": "Cookies Status", "Cookie Status": "<PERSON><PERSON>", "Vimeo Setting": "Vimeo Setting", "Vimeo Client ID": "Vimeo Client ID", "Vimeo Secret": "Vimeo Secret", "Vimeo Token Access": "Vimeo Token Access", "Vimeo Status": "Vimeo Status", "Currency Setting": "<PERSON><PERSON><PERSON><PERSON>", "Currency": "<PERSON><PERSON><PERSON><PERSON>", "Add Currency": "Add <PERSON>cy", "Currency Code": "Currency Code", "Symbol": "Symbol", "Currency Placement": "C<PERSON>rency Placement", "Before Amount": "Before Amount", "After Amount": "After Amount", "Type currency code": "Type currency code", "Type symbol": "Type symbol", "Current Currency": "Current Currency", "Site Share Content Setting": "Site Share Content Setting", "Map Api Key Setting": "Map Api Key Setting", "Key": "Key", "re-Captcha Setting": "re-<PERSON><PERSON>", "Site Key": "Site Key", "Secret Key": "Secret Key", "Google Analytics Setting": "Google Analytics Setting", "Measurement ID": "Measurement ID", "Color Setting": "Color Setting", "Custom": "Custom", "Theme Color": "Theme Color", "Navbar Background Color": "Navbar Background Color", "Body Font Color": "Body Font Color", "Heading Color": "Heading Color", "Gradiant Banner Color": "Gradiant Banner Color", "Current CSS Background": "Current CSS Background", "Gradiant Footer Color": "Gradiant Footer Color", "Gradiant Overlay Background Color Opacity": "Gradiant Overlay Background Color Opacity", "Font Setting": "Font Setting", "System Font": "System Font", "Font Family": "Font Family", "Font Link": "Font Link", "Certificate Font": "Certificate Font", "Certificate font": "Certificate font", "Private Mode Settings": "Private Mode Settings", "If private mode is active then no user can enroll any course without admin approval.": "If private mode is active then no user can enroll any course without admin approval.", "Select default saas package": "Select default saas package", "Default SAAS Type Instructor": "Default SAAS Type Instructor", "Default SAAS Type Organization": "Default SAAS Type Organization", "Registration Bonus Settings": "Registration Bonus Settings", "Registration Bonus System": "Registration Bonus System", "Bonus Amount": "Bonus Amount", "Refund System Settings": "Refund System Settings", "Cashback Settings": "Cashback Settings", "Cashback Type": "Cashback Type", "Percent": "Percent", "Fixed Amount": "Fixed Amount", "Chat Settings": "<PERSON><PERSON>", "Chat System Driver": "Chat System Driver", "Pusher": "<PERSON><PERSON><PERSON>", "Ajax": "Ajax", "Pusher ID": "Pusher ID", "Pusher Key": "<PERSON>usher Key", "Pusher Secret": "Pusher Secret", "Pusher Cluster": "<PERSON><PERSON><PERSON>", "Wallet Recharge System": "Wallet Recharge System", "Wallet Recgarge System": "Wallet Recgarge System", "State Setting": "State Setting", "Add State": "Add State", "Country name": "Country name", "City Setting": "City Setting", "Add City": "Add City", "State Name": "State Name", "Home Special Feature Section": "Home Special Feature Section", "First Logo": "First Logo", "First Title": "First Title", "First Subtitle": "First Subtitle", "Second Logo": "Second Logo", "Second Title": "Second Title", "Second Subtitle": "Second Subtitle", "Third Logo": "Third Logo", "Third Title": "Third Title", "Third Subtitle": "Third Subtitle", "Category Course Title": "Category Course Title", "Category Course Subtitle": "Category Course Subtitle", "Upcoming Course Title": "Upcoming Course Title", "Upcoming Course Subtitle": "Upcoming Course Subtitle", "Application_settings": "Application_settings", "Bundle Course Title": "Bundle Course Title", "Bundle Course Subtitle": "Bundle Course Subtitle", "Top Category Title": "Top Category Title", "Top Category Subtitle": "Top Category Subtitle", "Video Preview Image": "Video Preview Image", "Customer Say Title": "Customer Say Title", "Customer Comment Section": "Customer Comment Section", "First Customer Name": "First Customer Name", "First Customer Position": "First Customer Position", "First Customer Comment title": "First Customer Comment title", "First Customer Comment Description": "First Customer Comment Description", "First Customer Rating Star (1-5)": "First Customer Rating Star (1-5)", "Second Customer Name": "Second Customer Name", "Second Customer Position": "Second Customer Position", "Second Customer Comment title": "Second Customer Comment title", "Second Customer Comment Description": "Second Customer Comment Description", "Second Customer Rating Star (1-5)": "Second Customer Rating Star (1-5)", "Third Customer Name": "Third Customer Name", "Third Customer Position": "Third Customer Position", "Third Customer Comment title": "Third Customer Comment title", "Third Customer Comment Description": "Third Customer Comment Description", "Third Customer Rating Star (1-5)": "Third Customer Rating Star (1-5)", "PNG": "PNG", "Four Logo": "Four Logo", "Four Title": "Four Title", "Four Subtitle": "Four Subtitle", "Bank Account": "Bank Account", "Add Bank": "Add Bank", "Account Name": "Account Name", "Type Bank Name": "Type Bank Name", "Type Bank Account Name": "Type Bank Account Name", "Type Bank Account Number": "Type Bank Account Number", "Inactive": "Inactive", "Instructor Footer Title": "<PERSON><PERSON><PERSON><PERSON> Title", "Instructor Footer Subtitle": "Instructor Footer Subtitle", "Translate language": "Translate language", "Translate Language": "Translate Language", "Import Keywords": "Import Keywords", "Add More": "Add More", "Value": "Value", "Import Language": "Import Language", "Note: If you import keywords, your current keywords will be deleted and replaced by the imported keywords.": "Note: If you import keywords, your current keywords will be deleted and replaced by the imported keywords.", "AR ( Arabic )": "AR ( Arabic )", "Import": "Import", "Translate": "Translate", "Revenue report": "Revenue report", "Total Enrolled in Courses": "Total Enrolled in Courses", "Bundle Report": "Bundle Report", "Consultation Report": "Consultation Report", "Total Affiliator Commission": "Total Affiliator Commission", "Total Purchase Course": "Total Purchase Course", "Courses Report List": "Courses Report List", "Request course as": "Request course as", "Publish": "Publish", "If you select as upcoming then it will be show as upcoming in frontend after approval.": "If you select as upcoming then it will be show as upcoming in frontend after approval.", "Recomended image format & size": "Recomended image format & size", "To Upload your course videos please create your section and lesson details first!": "To Upload your course videos please create your section and lesson details first!", "Section title of the coures": "Section title of the coures", "Created successful.": "Created successful.", "Other Instructors": "Other Instructors", "Revenew share (in % between 0 to 100)": "Revenew share (in % between 0 to 100)", "Submit for review": "Submit for review", "You have selected as co-instructor": "You have selected as co-instructor", "Course has been approved": "Course has been approved", "New course has been published": "New course has been published", "Mark all as read": "Mark all as read", "Quiz List": "Quiz List", "Create New Quiz": "Create New Quiz", "Enter your quiz name": "Enter your quiz name", "Multiple Choice": "Multiple Choice", "True False": "True False", "Marks Per Question": "Marks Per Question", "Enter your marks per question": "Enter your marks per question", "Enter your time duration": "Enter your time duration", "Assignment List": "Assignment List", "Create New Assignment": "Create New Assignment", "Create Assignment": "Create Assignment", "Enter your assignment topic": "Enter your assignment topic", "Assignment Marks": "Assignment Marks", "Enter your Assignment Marks": "Enter your Assignment Marks", "Assignment Details": "Assignment Details", "Enter your assignment details": "Enter your assignment details", "Accepted files": "Accepted files", "Course has been hold": "Course has been hold", "New course": "New course", "Laravel": "<PERSON><PERSON>", "Laravel is a free, open-source PHP web framework used for building web applications. It follows the Model-View-Controller (MVC) architectural pattern, which separates the logic of an application into three interconnected components: models, views, and controllers.": "Laravel is a free, open-source PHP web framework used for building web applications. It follows the Model-View-Controller (MVC) architectural pattern, which separates the logic of an application into three interconnected components: models, views, and controllers.", "Laravel is a free, open-source PHP web framework used for building web applications. It follows the Model-View-Controller (MVC) architectural pattern, which separates the logic of an applicat": "Laravel is a free, open-source PHP web framework used for building web applications. It follows the Model-View-Controller (MVC) architectural pattern, which separates the logic of an applicat", "Models: Models represent the data and business logic of the application. They interact with the database to retrieve and store information. In Laravel, models are typically associated with da": "Models: Models represent the data and business logic of the application. They interact with the database to retrieve and store information. In Laravel, models are typically associated with da", "test2": "test2", "Views: Views are responsible for presenting the data to the user. They contain the HTML templates and are used to render the content that the user sees in their browser. Laravel uses the Blad": "Views: Views are responsible for presenting the data to the user. They contain the HTML templates and are used to render the content that the user sees in their browser. <PERSON><PERSON> uses the Blad", "test3": "test3", "Controllers: Controllers handle the application's logic, acting as intermediaries between the models and views. They receive input from the user, process it (often by interacting with models)": "Controllers: Controllers handle the application's logic, acting as intermediaries between the models and views. They receive input from the user, process it (often by interacting with models)", "test4": "test4", "Routing: Laravel provides a simple and expressive way to define routes, allowing you to map URLs to specific controllers and actions.": "Routing: <PERSON><PERSON> provides a simple and expressive way to define routes, allowing you to map URLs to specific controllers and actions.", "This is your course. No need to add to cart.": "This is your course. No need to add to cart.", "Added to Cart": "Added to <PERSON><PERSON>", "Mollie payment gateway is off!": "Mollie payment gateway is off!", "Razorpay payment gateway off!": "Razorpay payment gateway off!", "Badge": "Badge", "Membership": "Membership", "Author Level": "Author Level", "Courses Count": "Courses Count", "Students Count": "Students Count", "Courses Sale Count": "Courses Sale Count", "Refresh user badge": "Refresh user badge", "Rule": "Rule", "From": "From", "To": "To", "Total Sale Amount": "Total Sale Amount", "Sales": "Sales", "Create your rules carefully. So that all the possible ": "Create your rules carefully. So that all the possible ", "Badge Image": "Badge Image", "Accepted": "Accepted", "Descripiton": "Descripiton", "Add Skill": "<PERSON><PERSON>", "Type Title": "Type Title", "Edit Skill": "<PERSON>", "Affiliate": "Affiliate", "Affiliator list": "Affiliator list", "Pending Request": "Pending Request", "Suspend Request": "Suspend Request", "Approved Request": "Approved Request", "#ID": "#ID", "Write Feedback": "Write Feedback", "Affiliate Request": "Affiliate Request", "Letter": "Letter", "Request status has been updated": "Request status has been updated", " Affiliate Manage": " Affiliate Manage", "Resource List": "Resource List", "Add Resource": "Add Resource", "Add Resources": "Add Resources", "Resource Add": "Resource Add", "Total Notice": "Total Notice", "Add Notice": "Add Notice", "View List": "View List", "Empty Refund": "Empty Refund", "Refund Accepted": "Refund Accepted", "Total Certificate": "Total Certificate", "View Certificate": "View Certificate", "Select Certificate": "Select Certificate", "Certificate Not Found": "Certificate Not Found", "Analysis": "Analysis", "Number of Courses": "Number of Courses", "Total Earning": "Total Earning", "Available balance": "Available balance", "Total Withdraw Amount": "Total Withdraw Amount", "Pending Withdraw Amount": "Pending Withdraw Amount", "Withdrawal Money": "<PERSON><PERSON><PERSON> Money", "Withdrawal": "<PERSON><PERSON><PERSON>", "Method": "Method", "Withdraw with Paypal": "Withdraw with <PERSON><PERSON>", "Withdraw with Card": "Withdraw with Card", "Empty Followers": "Empty Followers", "Please use your email address as calender id. You have to authorize with that email to active the google meet.": "Please use your email address as calender id. You have to authorize with that email to active the google meet.", "Calender ID": "Calender ID", "Authorized": "Authorized", "Gmeet Setting": "<PERSON><PERSON><PERSON>", "Withdraw History": "Withdraw History", "Transactions": "Transactions", "Empty Transactions": "Empty Transactions", "Education Management": "Education Management", "Bundles Courses Name": "Bundles Courses Name", "Subject List": "Subject List", "All Subjects": "All Subjects", "All Chapter": "All Chapter", "All Topics": "All Topics", "Empty Chapter": "Empty Chapter", "Manage Institues & Units": "Manage Institues & Units", "Institues & Units": "Institues & Units", "Search Courses title": "Search Courses title", "Empty Discussion": "Empty Discussion", "Question Management": "Question Management", "Empty Question": "Empty Question", "All Questions": "All Questions", "Empty Subjects": "Empty Subjects", "Empty Chapters": "Empty Chapters", "Chapter successfully deleted": "Chapter successfully deleted", "Empty Questions": "Empty Questions", "Most Commented Authors": "Most Commented Authors", "Forum Leaderboard": "Forum Leaderboard", "You don't have the permission": "You don't have the permission", "Coupon": "Coupon", "Creator": "Creator", "Edit Language": "Edit Language", "Note:": "Note:", "You can`t change it.": "You can`t change it.", "ISO Code List": "ISO Code List", "RTL Support": "RTL Support", "Contact Us List": "Contact Us List", "Sl": "Sl", "Issue": "Issue", "Messages": "Messages", "Contact Issue List": "Contact Issue List", "Add Contact Issue": "Add Contact Issue", "Contact Issue": "Contact Issue", "Save and another": "Save and another", "Support Ticket Question & Answer": "Support Ticket Question & Answer", "Question": "Question", "Type question": "Type question", "Answer": "Answer", "Add Department": "Add Department", "Edit Department": "Edit Department", "Add Priority": "Add Priority", "Edit Priority": "Edit Priority", "Support Ticket Related Service Field": "Support Ticket Related Service Field", "Add Related Service": "Add Related Service", "Edit Related Service": "Edit Related Service", "Empty Saas Plan": "Empty Saas Plan", "Exam Management": "Exam Management", "Add Exam": "Add <PERSON>am", "Bulk Upload": "Bulk Upload", "Enter your question": "Enter your question", "True": "True", "False": "False", "View Sample File": "View Sample File", "Upload": "Upload", "Back to Quiz": "Back to Quiz", "View Quiz": "View Quiz", "Unpublish": "Unpublish", "Create Exam": "Create Exam", "Exam Title": "Exam Title", "Type Exam Title": "Type Exam Title", "Exam Fees": "<PERSON><PERSON>", "Enter Exam Fee": "Enter <PERSON><PERSON>", "Question Types": "Question Types", "Number of Questions": "Number of Questions", "Exam Duration (Minutes)": "<PERSON><PERSON> (Minutes)", "Marks per Question": "Marks per Question", "Negative marks per Question": "Negative marks per Question", "Model": "Model", "Exam Type": "Exam Type", "Result Type": "Result Type", "Academy Types": "Academy Types", "Your Bundle Create limit has been finish.": "Your Bundle Create limit has been finish.", "Add_notice": "Add_notice", "Notice Topic": "Notice Topic", "Notice Details": "Notice Details", "Instant Result Publish": "Instant Result Publish", "Is Expire Date": "Is Expire Date", "Result Grading": "Result Grading", "Result Marks": "Result Marks", "Exam Time End": "<PERSON>am Time End", "Marks Data": "Marks Data", "Continue": "Continue", "Exam Starts At": "<PERSON><PERSON> Starts At", "Exam successfully Save": "<PERSON>am successfully Save", "Exam": "Exam", "Exam Title : ": "Exam Title : ", "Exam Configuration Title": "Exam Configuration Title", "Type Exam Configuration": "Type Exam Configuration", "Mix Difficulty": "Mix Difficulty", "Max Difficulty": "<PERSON>", "Exam Configaration Saved": "Exam <PERSON> Saved", "All Exam": "All Exam", "Subject cannot be deleted because it has questions associated with it": "Subject cannot be deleted because it has questions associated with it", "Questions added to the exam": "Questions added to the exam", "Personal Info": "Personal Info", "Type your professional title": "Type your professional title", "Social Links": "Social Links", "Skills Name": "Skills Name", "Add More Certificate": "Add More Certificate", "Add More Award": "Add More Award", "Instructor Profile": "Instructor Profile", "Option 5": "Option 5", "Select Academy Types": "Select Academy Types", "Exam Center": "Exam Center", "List of all device from which you have logged in": "List of all device from which you have logged in", "My login devices": "My login devices", "Logout from all devices": "Logout from all devices", "device": "device", "OS": "OS", "Browser": "Browser", "IP address": "IP address", "Last session": "Last session", "Enter your option": "Enter your option", "Correct Answer": "Correct Answer", "My Attended Exam": "My Attended Exam", "Your max question add limit is9": "Your max question add limit is9", "Manage Topic": "Manage Topic", "Add Topic": "Add Topic", "Empty Topics": "Empty Topics", "Topic Name": "Topic Name", "Type Topic Name": "Type Topic Name", "Edit Topic": "Edit Topic", "Topic successfully Updated": "Topic successfully Updated", "Select topic Name": "Select topic Name", "Select Topic Name": "Select Topic Name", "Chapter": "Chapter", "Exam Name": "Exam Name", "Type Exam Name": "Type Exam Name", "<i class=\"fas fa-search\"></i>": "<i class=\"fas fa-search\"></i>", "Topic successfully Created": "Topic successfully Created", "All Questionshhhhh": "All Questionshhhhh", "Searching...": "Searching...", "Subject Management": "Subject Management", "Manage Routine": "Manage Routine", "All QuestZXZXions": "All QuestZXZXions", "\"Enovate | Educate | Elevate-Empowering the Next Generation to Learn,Teach,Lead.At Enovate,we are committed to inspiring growth and innovation through education\"": "\"Enovate | Educate | Elevate-Empowering the Next Generation to <PERSON><PERSON>,<PERSON><PERSON>,Lead.At Enovate,we are committed to inspiring growth and innovation through education\"", "DOHS Mirpur Dhaka Bangladesh": "DOHS Mirpur Dhaka Bangladesh", "+8801648904171": "+8801648904171", "© 2024 Testwizz.All Rights Reserved.": "© 2024 Testwizz.All Rights Reserved.", "LEARN": "LEARN", "TEACH": "TEACH", "LEAD": "LEAD", "\"Discover your path to success.The next generation is waiting for you—start your journey with us today.\"": "\"Discover your path to success.The next generation is waiting for you—start your journey with us today.\"", "Learn for own": "Learn for own", "Learning for Personal Growth and Empowerment.Active engagement and regular practice are key to effective learning.": "Learning for Personal Growth and Empowerment.Active engagement and regular practice are key to effective learning.", "Teach to others": "Teach to others", "A good teacher inspires curiosity, encourages growth, and adapts to the needs of students to foster meaningful learning experiences.": "A good teacher inspires curiosity, encourages growth, and adapts to the needs of students to foster meaningful learning experiences.", "Lead the generation": "Lead the generation", "Leadership requires vision,adaptability,and the ability to motivate and support teams in overcoming challenges.": "Leadership requires vision,adaptability,and the ability to motivate and support teams in overcoming challenges.", "HAVE A DEEP DRIVE IN YOUR OWN AREA": "HAVE A DEEP DRIVE IN YOUR OWN AREA", "\"Find Your Instructor\"": "\"Find Your Instructor\"", "Discovering Your Instructor: Key Steps to Finding the Right Guide": "Discovering Your Instructor: Key Steps to Finding the Right Guide", "What Our Ambitious Teachers Learners and Leaders Say": "What Our Ambitious Teachers Learners and Leaders Say", "KAWSER ABEDIN RUPOK": "KAWSER ABEDIN RUPOK", "CHEMISTRY INSTRUCTOR": "CHEMISTRY INSTRUCTOR", "\"Great initiative makes great responsibility\"": "\"Great initiative makes great responsibility\"", "As it revolutionizes learning with innovative tools and platforms,it bears the responsibility to ensure accessibility, inclusivity, and quality education for all. By addressing digital divides and fostering lifelong learning, edutech has the power to shape a brighter future responsibly.": "As it revolutionizes learning with innovative tools and platforms,it bears the responsibility to ensure accessibility, inclusivity, and quality education for all. By addressing digital divides and fostering lifelong learning, edutech has the power to shape a brighter future responsibly.", "ROBIN HOSSAIN": "ROBIN HOSSAIN", "MATH INSTRUCTOR": "MATH INSTRUCTOR", "\"Outstanding Solution\"": "\"Outstanding Solution\"", "An outstanding solution for EdTech involves a platform that seamlessly integrates interactive learning tools and personalized feedback. It should offer accessibility across devices, gamification to boost engagement, and real-time analytics for educators to monitor progress.": "An outstanding solution for EdTech involves a platform that seamlessly integrates interactive learning tools and personalized feedback. It should offer accessibility across devices, gamification to boost engagement, and real-time analytics for educators to monitor progress.", "KHONDAKER MAMUN NISHAT": "KHONDAKER MAMUN NISHAT", "MARKET LEAD": "MARKET LEAD", "\"Great Initiative Appreciated Most\"": "\"Great Initiative Appreciated Most\"", "A hallmark of excellence in EdTech, this recognition highlights innovative efforts that transform learning experiences. It celebrates solutions that bridge gaps, foster accessibility, and empower learners and educators with cutting-edge technology. Such initiatives inspire progress, shaping the future of education for all.": "A hallmark of excellence in EdTech, this recognition highlights innovative efforts that transform learning experiences. It celebrates solutions that bridge gaps, foster accessibility, and empower learners and educators with cutting-edge technology. Such initiatives inspire progress, shaping the future of education for all.", "Successfully Learned": "Successfully Learned", "1500+ students": "1500+ students", "200+ students": "200+ students", "50+ students": "50+ students", "500+ students": "500+ students", "ASK YOUR QUESTION WHAT DO YOU WANT TO KNOW": "ASK YOUR QUESTION WHAT DO YOU WANT TO KNOW", "What About Testwizz?": "What About Testwizz?", "Testwizz is an advanced educational technology platform designed to enhance learning, teaching, and leadership. It offers a range of tools for both educators and students, focusing on interactive learning, personalized feedback, and efficient assessment methods. With features like customizable tests, detailed analytics, and progress tracking, Testwizz empowers educators to lead effectively, tailoring their teaching methods to individual needs. The platform fosters an engaging learning environment, helping students thrive while supporting teachers in their professional growth and leadership within the educational process.": "Testwizz is an advanced educational technology platform designed to enhance learning, teaching, and leadership. It offers a range of tools for both educators and students, focusing on interactive learning, personalized feedback, and efficient assessment methods. With features like customizable tests, detailed analytics, and progress tracking, Testwizz empowers educators to lead effectively, tailoring their teaching methods to individual needs. The platform fosters an engaging learning environment, helping students thrive while supporting teachers in their professional growth and leadership within the educational process.", "How can i open an account here as new comer?": "How can i open an account here as new comer?", "First click sign in button then click create an account give your personal email phone no first name last name make a strong password for next time sign in then click sign up button.In this way you can open a new account and sign up here.": "First click sign in button then click create an account give your personal email phone no first name last name make a strong password for next time sign in then click sign up button.In this way you can open a new account and sign up here.", "How can i sign in?": "How can i sign in?", "For user friendly system when you first time create an account here it automatically sign in and save your password. You don't need to sign in/Login every visiting time .Although when you Log out then give your registration email or phone no and put your password just click Sign in/Log in.": "For user friendly system when you first time create an account here it automatically sign in and save your password. You don't need to sign in/Login every visiting time .Although when you Log out then give your registration email or phone no and put your password just click Sign in/Log in.", "ASK FOR THAT WHAT MAKES YOU CONFUESD": "ASK FOR THAT WHAT MAKES YOU CONFUESD", "Find your course from your favourite instructor and have a deep drive for gathering knowledge.": "Find your course from your favourite instructor and have a deep drive for gathering knowledge.", "Find your favourite instructor. Make the decision take step and discovering right guideline is the key to success of your goals.": "Find your favourite instructor. Make the decision take step and discovering right guideline is the key to success of your goals.", "24/7 online support": "24/7 online support", "Please ask anything anytime what's make you confused. Our support center is always waiting for you to solve your technical problems.": "Please ask anything anytime what's make you confused. Our support center is always waiting for you to solve your technical problems.", "Join us as we foster a community where every individual can thrive,share knowledge,and become a catalyst for positive change.": "Join us as we foster a community where every individual can thrive,share knowledge,and become a catalyst for positive change.", "Educational": "Educational", "Research": "Research", "পরীক্ষার আগের দিনে করনী বিষয়াবলী!": "পরীক্ষার আগের দিনে করনী বিষয়াবলী!", "পড়ার রুটিন তৈরির জন্য ধাপগুলো অনুসরণ করুন:": "পড়ার রুটিন তৈরির জন্য ধাপগুলো অনুসরণ করুন:", "ভালো ফলাফল অর্জনের জন্য কিছু কার্যকর টিপস!": "ভালো ফলাফল অর্জনের জন্য কিছু কার্যকর টিপস!", "Scholarship": "Scholarship", "University Admission": "University Admission", "Validate Certificate": "Validate Certificate", "Certificate Varification": "Certificate Varification", "Certificate ID": "Certificate ID", "Enter certificate ID": "Enter certificate ID", "Ask for that what you need to be assisted from us": "Ask for that what you need to be assisted from us", "+8801761457984": "+8801761457984", "Thank you for connecting us.We are very happy that you are trying to reach us. For some of our technical and formal issues sometimes It might take 6 -12 hour to replay. Please wait and stay connect.": "Thank you for connecting us.We are very happy that you are trying to reach us. For some of our technical and formal issues sometimes It might take 6 -12 hour to replay. Please wait and stay connect.", "Testwizz Administration": "Testwizz Administration", "K.A Rupok": "<PERSON><PERSON><PERSON>", "Khondakar Nishat Ahmed": "<PERSON><PERSON><PERSON><PERSON>", "ABOUT TESTWIZZ": "ABOUT TESTWIZZ", "Welcome to Testwizz,where cutting-edge technology meets transformative learning experiences. At Testwizz we are on a mission to revolutionize education by harnessing the power of innovation and personalization. Our platform is designed to empower learners of all ages, educators, and institutions with intuitive tools, dynamic content, and real-time analytics, ensuring that education is more accessible, effective, and engaging than ever before. What We Offer: Smart Assessments: Tailored testing solutions with adaptive algorithms that cater to individual learning needs. Interactive Learning: Engaging modules and resources designed to make learning enjoyable and impactful. Data-Driven Insights: Advanced analytics to track progress, identify strengths, and pinpoint areas for improvement. Seamless Integration: A platform that works effortlessly with existing educational systems and tools. Why Choose Testwizz? Innovative Technology: Leveraging AI and machine learning to deliver personalized educational experiences. User-Centric Design: Built with educators and learners in mind, making it easy to navigate and use. Global Reach: Serving students and institutions worldwide, fostering a community of learners and educators. Commitment to Excellence: Continuously evolving to meet the changing needs of education in the digital age. At Testwizz, we believe that education is the foundation of a better future. By combining advanced technology with a passion for learning, we strive to unlock every individual’s full potential. Join us on this journey to transform education, one learner at a time.": "Welcome to Testwizz,where cutting-edge technology meets transformative learning experiences. At Testwizz we are on a mission to revolutionize education by harnessing the power of innovation and personalization. Our platform is designed to empower learners of all ages, educators, and institutions with intuitive tools, dynamic content, and real-time analytics, ensuring that education is more accessible, effective, and engaging than ever before. What We Offer: Smart Assessments: Tailored testing solutions with adaptive algorithms that cater to individual learning needs. Interactive Learning: Engaging modules and resources designed to make learning enjoyable and impactful. Data-Driven Insights: Advanced analytics to track progress, identify strengths, and pinpoint areas for improvement. Seamless Integration: A platform that works effortlessly with existing educational systems and tools. Why Choose Testwizz? Innovative Technology: Leveraging AI and machine learning to deliver personalized educational experiences. User-Centric Design: Built with educators and learners in mind, making it easy to navigate and use. Global Reach: Serving students and institutions worldwide, fostering a community of learners and educators. Commitment to Excellence: Continuously evolving to meet the changing needs of education in the digital age. At Testwizz, we believe that education is the foundation of a better future. By combining advanced technology with a passion for learning, we strive to unlock every individual’s full potential. Join us on this journey to transform education, one learner at a time.", "Possession of my entire soul has been started to generate this idea in May 20th at 2020 Leading the Future: Pioneering Thought and Innovation in EdTech. Innovate Educate Elevate the generation": "Possession of my entire soul has been started to generate this idea in May 20th at 2020 Leading the Future: Pioneering Thought and Innovation in EdTech. Innovate Educate Elevate the generation", "\"Our Passionate Team Members\"": "\"Our Passionate Team Members\"", "Our passionate team members are the driving force behind our success, bringing dedication, creativity, and expertise to every endeavor.": "Our passionate team members are the driving force behind our success, bringing dedication, creativity, and expertise to every endeavor.", "Mirpur Dhaka Bangladesh": "Mirpur Dhaka Bangladesh", "Manage Batch": "Manage Batch"}