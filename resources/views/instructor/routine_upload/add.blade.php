@extends('layouts.instructor')
@section('content')
    <style>
        .container {
            margin: auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        h2 {
            text-align: center;
            color: #333;
            margin-bottom: 20px;
        }
        input[type="file"] {
            width: 100%;
            padding: 15px;
            border: 2px dashed #007bff;
            border-radius: 5px;
            font-size: 16px;
            transition: border-color 0.3s;
            cursor: pointer;
        }
        input[type="file"]:focus {
            border-color: #0056b3;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s, transform 0.3s;
            width: 100%;
        }
        button:hover {
            background-color: #0056b3;
            transform: scale(1.05);
        }
        @media (max-width: 480px) {
            .container {
                padding: 20px;
            }
            h2 {
                font-size: 24px;
            }
            button {
                padding: 10px;
                font-size: 14px;
            }
        }
    </style>

    <div class="container">
        <h2>Select Your Upload File</h2>
        <form id="uploadForm" action="{{route('instructor.routine-store')}}" method="POST" enctype="multipart/form-data">
            @csrf
            <div style="margin-bottom: 20px;">
                <input type="text" name="title" id="title" placeholder="Enter Title" required 
                       style="width: 100%; padding: 15px; border: 2px solid #007bff; border-radius: 5px; font-size: 16px; transition: border-color 0.3s;" />
            </div>
            <div style="margin-bottom: 20px;">
                <select name="academic_type" id="academic_type" required 
                        style="width: 100%; padding: 15px; border: 2px solid #007bff; border-radius: 5px; font-size: 16px; transition: border-color 0.3s;">
                    <option value="" disabled selected>Select Academic Type</option>
                    @foreach($academyTypes as $academyType)
                        <option value="{{$academyType->id}}">{{$academyType->title}}</option>
                    @endforeach
                </select>
            </div>
            <div style="margin-bottom: 20px;">
                <input type="file" name="file" id="file" accept=".pdf" required />
            </div>
            <div>
                <button type="submit">Upload</button>
            </div>
        </form>
    </div>

    <script>
        document.getElementById('uploadForm').addEventListener('submit', function(event) {
            const fileInput = document.getElementById('file');
            const filePath = fileInput.value;
            const allowedExtensions = /(\.pdf)$/i;

            if (!allowedExtensions.exec(filePath)) {
                alert('Please upload a valid PDF file.');
                fileInput.value = '';
                event.preventDefault(); // Prevent form submission
            }
        });
    </script>
@endsection