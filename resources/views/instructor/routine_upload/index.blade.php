@extends('layouts.instructor')
<style>
    .dropdown.all-student-filter-dropdown{
        display: flex;
        align-items: end;
        gap: 10px;
        width: 50%;
    }
    .searching-parent{
        display: flex;
        align-items: center;
        gap: 25px;
        width: 100%;
        position: relative;
    }

    button.theme-btn.default-hover-btn.theme-button1 {
        position: absolute;
        right: 8px;
        top: 6px;
    }

    .searching-flex-parent{
        width: 100%;
    }
    .common-add-btn {
        border: 1px solid rgba(0, 0, 0, 0.07);
        padding: 11px 20px;
        border-radius: 8px;
        white-space: nowrap;
    }
</style>
@section('content')
<div class="instructor-profile-right-part">
    <div class="instructor-quiz-list-page instructor-all-student-page">
        <div class="instructor-my-courses-title d-flex justify-content-between align-items-center">
            <h6>{{__('All Routines')}}</h6>
            <div class="dropdown all-student-filter-dropdown">
                <form action="{{ route('instructor.routine-index') }}" name="get" class="searching-parent">
                    <div class=" m-0 p-0 searching-flex-parent">
                        <input type="search" name="search_name" class="form-control"
                            placeholder="{{ __('Searching...') }}" value="{{ app('request')->search_name }}">
                    </div>
                    <button type="submit" class="theme-btn default-hover-btn theme-button1"><i
                            class="fas fa-search"></i></button>
                </form>
                <a href="{{route('instructor.routine-upload')}}" class="common-add-btn" type="button">{{__('Upload Routine')}}</a>
            </div>
        </div>
        <div class="row">
            @if(count($routines) > 0)
            <div class="col-12">
                <div class="table-responsive table-responsive-xl">
                    <table id="customers-table" class="table">
                        <thead>
                            <tr>
                                <th>Title</th>
                                <th>Academic type</th>
                                <th>File</th>
                                <th>Status</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($routines as $key => $routine)
                            <tr class="removable-item">
                                <td>{{$routine->title}}</td>
                                <td>{{$routine->academyType->title}}</td>
                                <td>{{$routine->file_path}}</td>
                                <td>
                                    <button class="btn btn-link p-0" onclick="updateStatus({{$routine->id}})">
                                        {{$routine->status == 1 ? "Active" : "Deactivated" }}
                                    </button>
                                </td>
                                <td>
                                    <div class="action__buttons">
                                        <button class="btn-action ms-2 deleteItem"
                                            data-formid="delete_row_form_{{ $routine->id }}">
                                            <img src="{{ asset('admin/images/icons/trash-2.svg') }}" alt="trash">
                                        </button>
                                        <form action="{{route('instructor.routine-delete', [$routine->id])}}"
                                            method="post" id="delete_row_form_{{ $routine->id }}">
                                            {{ method_field('DELETE') }}
                                            <input type="hidden" name="_token" value="{{ csrf_token() }}">
                                        </form>
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                    <div class="mt-3">
                        {{$routines->links()}}
                    </div>
                </div>
            </div>
            @else
            <div class="empty-data">
                <img src="{{ asset('frontend/assets/img/empty-data-img.png') }}" alt="img" class="img-fluid">
                <h5 class="my-3">{{__('Empty Routines')}}</h5>
            </div>
            @endif
        </div>
    </div>
</div>
@endsection
<script>
    function updateStatus(id) {
        $.ajax({
            url: "{{ route('instructor.routine-status-change') }}",
            type: "POST",
            data: {
                id: id,
                _token: "{{ csrf_token() }}"
            },
            success: function(response) {
                if (response.status == true) {
                    toastr.success(response.message);
                    setTimeout(() => {
                        location.reload();
                    }, 1000);
                } else {
                    toastr.error(response.message);
                }
            }
        });
    }
</script>