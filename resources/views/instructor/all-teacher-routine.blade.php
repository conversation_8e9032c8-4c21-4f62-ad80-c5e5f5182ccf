<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>All Teacher Routines</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.1.1/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f0f4f1;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        h1 {
            color: #335B47;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        .search-container {
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
        }
        #searchInput {
            flex-grow: 1;
            padding: 10px;
            font-size: 1em;
            border: 2px solid #335B47;
            border-radius: 5px;
            box-sizing: border-box;
        }
        .back-btn {
            background-color: #335B47;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1em;
            transition: background-color 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .back-btn:hover {
            background-color: #2a4a3a;
        }
        .back-btn i {
            margin-right: 5px;
        }
        .routine-list {
            list-style-type: none;
            padding: 0;
        }
        .routine-item {
            background-color: white;
            border-radius: 15px;
            margin-bottom: 20px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .routine-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
        }
        .routine-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 2px solid #335B47;
            padding-bottom: 10px;
            margin-bottom: 10px;
        }
        .routine-title {
            color: #335B47;
            margin: 0;
            font-size: 1.4em;
            display: flex;
            align-items: center;
        }
        .routine-title i {
            margin-right: 10px;
            font-size: 1.2em;
        }
        .teacher-info {
            text-align: right;
        }
        .teacher-name {
            color: #335B47;
            margin: 0;
            font-weight: bold;
        }
        .upload-time {
            color: #777;
            margin: 5px 0 0 0;
            font-size: 0.9em;
        }
        .routine-details {
            display: flex;
            justify-content: space-between;
            margin-top: 10px;
        }
        .detail-item {
            display: flex;
            align-items: center;
            color: #555;
        }
        .detail-item i {
            margin-right: 5px;
            color: #335B47;
        }
        .download-btn {
            background-color: #335B47;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9em;
            transition: background-color 0.3s ease;
            display: flex;
            align-items: center;
            margin-top: 10px;
        }
        .download-btn:hover {
            background-color: #2a4a3a;
        }
        .download-btn i {
            margin-right: 5px;
        }
        .no-results {
            text-align: center;
            color: #335B47;
            font-size: 1.2em;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        {{-- <h1><i class="fas fa-chalkboard-teacher"></i> All Teacher Routines</h1>
        <form action="{{ route('student.live-exam-routines') }}" name="get" class="searching-parent">
            <div class="search-container">
                <input type="text" name="search_name" value="{{request()->search_name ?? ''}}" id="searchInput" placeholder="Search by teacher name or subject..." aria-label="Search routines">
            </div>
        </form> --}}
        <h1><i class="fas fa-chalkboard-teacher"></i> All Routines</h1>
        <div class="search-container">
            <a href="{{route('student.examCenter')}}" style="text-decoration: none;">
                <button id="backBtn" class="back-btn" aria-label="Go back">
                    <i class="fas fa-arrow-left"></i> Back
                </button>
            </a>
            <form action="{{ route('student.live-exam-routines') }}" name="get" class="searching-parent">
                <input type="text" name="search_name" value="{{request()->search_name ?? ''}}" id="searchInput" placeholder="Search by title or teacher..." aria-label="Search routines">
            </form>
        </div>
        <ul id="routineList" class="routine-list">
            @forelse($routines as $routine)
            @php
              $downloadLink = route('student.routine-download',$routine->id);  
            @endphp
                <li class="routine-item" data-teacher="John Smith" data-subject="Mathematics">
                    <div class="routine-header">
                        <h2 class="routine-title"><i class="{{App\Enums\SubjectBaseIcon::getIconByKey($routine->academic_type)}}"></i> {{$routine->title}}</h2>
                        <div class="teacher-info">
                            <p class="teacher-name"><i class="fa-regular fa-user"></i> {{ $routine->instructor->name }}</p>
                            <p class="upload-time"><i class="far fa-clock"></i> Uploaded: {{$routine->created_at}}</p>
                        </div>
                    </div>
                   
                    {{-- <div class="routine-details">
                        <span class="detail-item"><i class="fas fa-users"></i> 30 students</span>
                        <span class="detail-item"><i class="fas fa-clock"></i> 60 minutes</span>
                        <span class="detail-item"><i class="fas fa-calendar-alt"></i> Mon, Wed, Fri</span>
                    </div> --}}
                    <a href="{{$downloadLink}}" style="text-decoration: none;">
                        <button class="download-btn">
                            <i class="fas fa-download"></i> Download Routine
                        </button>
                    </a>
                </li>
            @empty
            <li class="routine-item">
                <div class="routine-header">
                    <h2 class="routine-title"><i class="fas fa-exclamation-circle"></i> No Routines Found</h2>
                </div>
            </li>
            @endforelse
        </ul>
        <div class="mt-3">
            {{$routines->links()}}
        </div>
    </div>
</body>
</html>