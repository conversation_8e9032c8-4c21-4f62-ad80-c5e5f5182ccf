
<!DOCTYPE html>
<html lang="en">
<head>
    <!--- Basic Page Needs  -->
    <meta charset="utf-8">
    <title>{{"Test | Self MCQ EXAM"}}</title>
    <meta name="description" content="">
    <meta name="author" content="">
    <meta name="keywords" content="">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <!-- Mobile Specific Meta  -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css?family=Raleway:300,400,500,700" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css?family=Work+Sans:400,500,600,700" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css?family=Ubuntu:300,300i,400,400i,500,500i,700,700i" rel="stylesheet">
    <!-- KaTeX -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.9.0/katex.min.css" rel="stylesheet">
    <!-- CSS -->
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="{{asset('question/assets/css/bootstrap.min.css')}}">
    <!-- Jquery ui CSS -->
    <link rel="stylesheet" href="{{asset('question/assets/css/jquery-ui.css')}}">
    <!-- Font Awesome CSS -->
    <link rel="stylesheet" href="{{asset('question/assets/css/font-awesome.min.css')}}">
    <!-- Animate CSS -->
    <link rel="stylesheet" href="{{asset('question/assets/css/animate.css')}}">
    <!-- Main StyleSheet CSS -->
    <link rel="stylesheet" href="{{asset('question/assets/css/style.css')}}">
    <!-- Responsive CSS -->
    <link rel="stylesheet" href="{{asset('question/assets/css/responsive.css')}}">
        <style>
            .single-question .select-answer ul li div{
                display: inline;
            }
            .qoustion-area *{
                font-size: 16px !important;
            }
            input[type='radio'].correct:checked:after {
                background-color: #08ff46 !important;
                border: 2px solid #08ff46 !important;
            }
            input[type='radio'].wrong:checked:after {
                background-color: #ff2d08 !important;
                border: 2px solid #ff2d08 !important;
            }
            span.correct-round {
                background-color: #08ff46;
                border-color: #08ff46 !important;
            }
            span.wrong-round {
                background-color: #ff2d08;
                border-color: #ff2d08 !important;
            }
            .answer-sheet ul.as-list li p input[type="radio"].correct:checked:after {
                position: absolute;
                content: '';
                height: 100%;
                width: 100%;
                background-color: #02b72f !important;
                top: 0;
                left: 0;
                border-radius: 50%;
                border-color: #02b72f !important;
            }
            .answer-sheet ul.as-list li p input[type="radio"].wrong:checked:after {
                position: absolute;
                content: '';
                height: 100%;
                width: 100%;
                background-color: #ff4322 !important;
                top: 0;
                left: 0;
                border-radius: 50%;
                border-color: #ff4322 !important;
            }
             header.header-area img.eph-bg {
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
                height: 100%;
            }
            header.header-area {
                padding: 20px 0px 12px;
            }
            .select-ans-preview ul li .sap-ca {
                background-color: transparent;
                border: none;
                padding: 0px;
                color: #15BDFF;
                position: relative;
                top: 4px;
            }
            .single-question .select-answer ul li {padding: 0px;}
            .select-ans-preview ul li {
                width: auto !important;
                margin-right: 10px !important;
            }
            .explaination-btn {
                position: relative;
            }
            .explaination-btn button {
                background-color: #224156;
                color: #fff;
                border: none;
                padding: 6px 10px;
                font-size: 11px !important;
                border-radius: 15px;
            }
            .explaination-contents {
                position: relative;
                padding: 10px;
            }
            .explaination-contents h4 {
                font-size: 13px;
                line-height: 18px;
                color: #15BDFF;
                letter-spacing: 0px;
                margin-bottom: 18px;
            }
            .explaination-contents p {
                font-size: 13px;
                color: #000;
                line-height: 23px;
            }
            #explainationModal .modal-dialog {
                top: 100px;
            }
            @media(max-width: 767px){
                .select-ans-preview ul li {
                    width: auto !important;
                    margin-right: 10px !important;
                }
                .qoustion-area * {
                    font-size: 12px !important;
                    line-height: 18px;
                }
                .select-ans-preview ul li {
                    width: 40% !important;
                }
                .select-ans-preview ul li:first-child {
                    width: 100% !important;
                }
            }
            .select-ans-preview li button.btn.btn-warning.btn-sm {
                background-color: #EB3D3D;
                color: #fff;
                border: none;
                padding: 6px 10px;
                font-size: 11px !important;
                border-radius: 15px;
            }
            button#backward {
                position: relative;
                padding: 5px 15px;
                background-color: #007bff;
                color: #fff;
                border: 1px solid #007bff;
                border-radius: 5px;
                font-size: 14px;
                line-height: 20px;
                margin-top: 0px;
                margin-left: 13px;
                display: inline-block;
                }
            .qa-watermark-bp {
                position: fixed;
                width: 100%;
                top: 45%;
                text-align: center;
                z-index: 999;
                left: 0;
                transform: rotate(-45deg);
                opacity: .25;
            }
            .qa-watermark-bp p {
                font-size: 30px !important;
                line-height: 38px;
                font-weight: bold;
            } 
            li.explanation-prev .sap-ca,li.explanation-prev p {
                line-height: 20px;
                display: block;
                color: #000 !important;
            }
            .select-ans-preview ul li.explanation-prev {
                width: 100% !important;
                background-color: #e1e1e1;
                max-width: 100% !important;
                padding: 10px 15px;
                border-radius: 4px;
                padding-bottom: 18px;
            }
            header.header-area {
            background-color: #2e7d32 !important;
            padding-top: 10px;
            padding-bottom: 30px; 
            }
            .main-content-wrapper{
                border: none;
            }
            .exam-logo h2{
                font-size: 20px;
                margin-bottom: 10px;
                text-align: center;
            }
            .back-btn{
                text-align: center !important;
            }
        </style>
</head>
@php 
    function getCorrectOption($question_answer){
        switch($question_answer['correct_option']){
            case "option_1":
                return "A";
            break;
            case "option_2":
                return "B";
            break;
            case "option_3":
                return "C";
            break;
            case "option_4":
                return "D";
            break;
            case "option_5":
                return "E";
            break;
        }
    }
@endphp
<body>
    <div id="preloader"></div>
    <!-- Header Area --> 
    <!-- Header Area --> 
    <header class="header-area">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="exam-logo">
                        <h2>Self Exam Solve Sheet</h2>
                    </div>
                    <div class="back-btn">
                           <button class="pageback-button" onClick="previous()"  id="backward">Back</button> 
                    <script>  
                        function previous() {
                            window.history.back();
                        }
                    </script>
                    </div>
                </div>
            </div>
        </div>
    </header>
    <!-- Main Content Wrapper -->
    <div class="main-content-wrapper">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-md-2"></div>
                    <div class="col-md-8">
                        <div class="qoustion-area">
                            <div class="qa-watermark-bp">
                                <p>Prosthuti</p>
                            </div>
                            <div class="question-box">
                                <ul class="questions q-list">
                                    @foreach ($question_answers as $question_answer)
                                    @php
                                        $selected_option=$question_answer['selected_option'];
                                    @endphp
                                    <li id="section_{{$loop->iteration}}">
                                        <div class="single-question">
                                            <div class="question">
                                                <span class="question-no">{{$loop->iteration}}</span>
                                                <h4>{!!$question_answer['question_text']!!}</h4>
                                                @php
                                                    // $mcq=$question->mcqQuestion;
                                                @endphp
                                            </div>
                                            <div  class="select-answer" data-answered="{{$selected_option==null?'false':'true'}}" data-qa_id="{{$question_answer['id']}}" data-on_review="false">
                                                <ul>
                                                    <li><input disabled  type="radio" id=""><div class="opt" ><span>A</span>{!!$question_answer['option_1']!!}</div></li>
                                                    <li><input disabled   type="radio" id=""><div class="opt" ><span>B</span>{!!$question_answer['option_2']!!}</div></li>
                                                    <li><input disabled   type="radio" id=""><div class="opt" ><span>C</span>{!!$question_answer['option_3']!!}</div></li>
                                                    <li><input disabled   type="radio" id=""><div class="opt" ><span>D</span>{!!$question_answer['option_4']!!}</div></li>
                                                </ul>
                                                <div class="select-ans-preview">
                                                    <ul>
                                                        <li>
                                                            <div class="sap-ca">Ans: {{ getCorrectOption($question_answer) }}</div>
                                                        </li>
                                                        <li class="explanation-prev">
                                                            <div class="sap-ca">Explanation: {!! $question_answer['solution'] !!}</div>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </li>
                                    @endforeach
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
    </div>
    <!-- /Main Content Wrapper -->
    <!-- Scripts -->
    <!-- jQuery Plugin -->
    <script src="{{asset('question/assets/js/jquery-3.2.0.min.js')}}"></script>
    <script src="{{asset('question/assets/js/jquery-ui.js')}}"></script>
    <script src="{{asset('question/assets/js/jquery.waypoints.min.js')}}"></script>
    <!-- Bootstrap JS -->
    <script src="{{asset('question/assets/js/bootstrap.min.js')}}"></script>
    
    <script>
        $(window).on('load', function() {
            $('#preloader').fadeOut('fast', function() { $(this).remove(); });
        });
        /*---------------------
        smooth scroll
        --------------------- */
        $('.smoothscroll').on('click', function(e) {
            e.preventDefault();
            var target = this.hash;

            $('html, body').stop().animate({
                'scrollTop': $(target).offset().top - 80
            }, 1200);
        });

    </script>
    
</body>

</html>
