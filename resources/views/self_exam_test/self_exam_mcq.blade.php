
<!DOCTYPE html>
<html lang="en">
<head>
    <!--- Basic Page Needs  -->
    <meta charset="utf-8">
    <title>{{"Test | ".$self_exams->self_exam_title}}</title>
    <meta name="description" content="">
    <meta name="author" content="">
    <meta name="keywords" content="">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <!-- Mobile Specific Meta  -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css?family=Raleway:300,400,500,700" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css?family=Work+Sans:400,500,600,700" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css?family=Ubuntu:300,300i,400,400i,500,500i,700,700i" rel="stylesheet">
    <!-- KaTeX -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.9.0/katex.min.css" rel="stylesheet">
    <!-- CSS -->
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="{{asset('question/assets/css/bootstrap.min.css')}}">
    <!-- Jquery ui CSS -->
    <link rel="stylesheet" href="{{asset('question/assets/css/jquery-ui.css')}}">
    <!-- Font Awesome CSS -->
    <link rel="stylesheet" href="{{asset('question/assets/css/font-awesome.min.css')}}">
    <!-- Animate CSS -->
    <link rel="stylesheet" href="{{asset('question/assets/css/animate.css')}}">
    <!-- Main StyleSheet CSS -->
    <link rel="stylesheet" href="{{asset('question/assets/css/style.css')}}">
    <!-- Responsive CSS -->
    <link rel="stylesheet" href="{{asset('question/assets/css/responsive.css')}}">

        <style>
            .single-question .select-answer ul li div{
                display: inline;
            }
            /* .katex { font-size: 5em !important; }  */
            .qoustion-area *{
                font-size: 13px !important;
            }
            .mcq-question-top-header {
                display: none;
            }
             button#backward {
                display: none;
            }
            @media only screen and (max-width: 767px) {
               header.header-area img.eph-bg {
                    position: absolute;
                    left: 0;
                    top: 0;
                    width: 100%;
                    height: 100%;
                }
                
                header.header-area {
                    padding: 20px 0px 30px;
                }
                .mcq-question-top-header {
                        padding: 0px 20px;
                        z-index: 1;
                        top: 80px;
                        display: block;
                        position: fixed;
                        width: 100%;
                        left: 0;
                }
                
                ul.mcqth-list {
                    position: relative;
                    padding: 10px 15px;
                    list-style-type: none;
                    background-color: #15bdff;
                    border-radius: 5px;
                    text-align: center;
                }
                
                ul.mcqth-list li {
                    position: relative;
                    display: inline-block;
                    width: 32%;
                    font-size: 13px;
                    letter-spacing: 0px;
                    color: #fff;
                }
                
                ul.mcqth-list li span {
                    color: #224156;
                }
                
                .main-content-wrapper { 
                    padding-top: 25px;
                }
                
                .question-box {
                    border: none;
                }
                
                ul.questions>li {
                    padding: 0px;
                }
                
                .single-question {
                    border: 1px solid #d4dade;
                    border-radius: 5px;
                    padding: 15px 5px;
                    margin-bottom: 10px;
                }
                .infos-area .info-box .exam-review button.timer-btn {
                    top: 24px;
                    right: 0px;
                    left: unset;
                    background-color: transparent !important;
                    border: none !important;
                    color: #f00 !important;
                    font-weight: 600;
                    box-shadow: none !important;
                }
                .infos-area .info-box .exam-review button.timer-btn {
                    background-color: #f00 !important;
                    color: #fff !important;
                    right: 20px !important;
                }
                .infos-area .info-box .exam-review button.end-exam-btn {
                    top: unset;
                    right: 38px;
                    bottom: 0px;
                    background-color: #15bdff;
                }
                
                span.omrSheet {
                    right: unset;
                    left: 26px;
                    bottom: 0px;
                }
                 
                span.omrSheetClose {
                    right: unset;
                    left: 20px;
                    bottom: 10px;
                    z-index: 14;
                }
                .main-content-wrapper {
                    border-color: #fff;
                }
                .infos-area.viewMobileOMR {
                    z-index: 13;
                    top: -100%;
                    position: absolute;
                   height: calc(100vh - 130px);
                }
                
                span.omrSheetClose {
                    display: none;
                }
                .infos-area.viewMobileOMR span.omrSheetClose {
                    display: block;
                }
                
                .qoustion-area {
                    height: calc(100vh - 200px);
                }
                
                .main-content-wrapper {
                    max-height: calc(100vh - 100px);
                }
                button#backward {
                    position: relative;
                    padding: 5px 15px;
                    background-color: #007bff;
                    color: #fff;
                    border: 1px solid #007bff;
                    border-radius: 5px;
                    font-size: 14px;
                    line-height: 20px;
                    margin-top: 12px;
                    margin-left: 13px;
                    display: inline-block;
                }

                .mcq-question-top-header { 
                    top: 120px;
                }
                .infos-area.viewMobileOMR span.omrSheetClose {
                    top: calc(100vh - 218px);
                    bottom: unset !important;
                    width: 110px;
                    z-index: 999;
                }

                .answer-sheet ul.as-list li p {
                    width: 20px;
                    height: 20px;
                    margin-top: 12px;
                }

                .answer-sheet ul.as-list li p span {
                    font-size: 13px;
                    line-height: 19px;
                }
                .infos-area.viewMobileOMR {
                    height: calc(100vh - 125px);
                }
                .infos-area.viewMobileOMR .answer-sheet {
                    height: calc(100% - 125px);
                    border-bottom: 1px solid #000;
                }
                body.fixed-body {
                    height: 100vh;
                    position: relative;
                    overflow: hidden;
                }
                body.fixed-body button#backward {
                    visibility: hidden;
                }
                .exam-logo h2 {
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
            }
        </style>
</head>

<body>
    @php
        function ifChecked($qa_value,$inp_value){
            if($qa_value==$inp_value){
                return "checked";
            }
            return "";
        }
        function ifDisabled($qa_value){
            if($qa_value==null){
               return "";
            }
            return "disabled";
        }
    @endphp
    <div id="preloader"></div>

    <!-- Header Area --> 
    <header class="header-area">
        {{-- <img src="{{url('/')}}/images/exam-header-bg.png" alt="" class="eph-bg"> --}}
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="back-btn">
                        <button class="pageback-button" onClick="previous()"  id="backward">Back</button> 
                    <script>  
                        function previous() {
                            window.history.back();
                        }
                    </script>
                    </div>
                    <div class="exam-logo">
                        <h2>{{$self_exams->self_exam_title}}</h2>
                    </div>
                </div>
            </div>
        </div>
    </header>
    <!-- /Header Area -->
    
    
    <!-- Main Content Wrapper -->
    <div class="main-content-wrapper">
        
        <form action="">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-md-8">
                        <div class="qoustion-area">
                            <div class="mcq-question-top-header">
                                <ul class="mcqth-list">
                                </ul>
                            </div>
                            <div class="question-box">
                                <ul class="questions q-list">
                                    @foreach ($question_answers as $key => $question_answer)
                                    @php
                                        // $selected_option = $question_answer['selected_option'];
                                        $selected_option = $selectedQuestions[$key]->selected_option ?? null ;
                                    @endphp
                                    <li id="section_{{$loop->iteration}}">
                                        <div class="single-question">
                                            <div class="question">
                                                <span class="question-no">{{$loop->iteration}}</span>
                                                <h4>{!!$question_answer['question_text']!!}</h4>
                                            </div>
                                            <div class="select-answer" data-answered="{{$selected_option==null?'false':'true'}}" data-qa_id="{{$question_answer['id']}}" data-on_review="false">
                                                <ul>
                                                    <li><input {{ifDisabled($selected_option)}} {{ifChecked($selected_option,'option_1')}} onchange="optionSelected(this)" class="q_{{$loop->iteration}}" value="option_1" type="radio" name="q_{{$loop->iteration}}" id=""><div class="opt" onclick="markOption(this)"><span>A</span>{!!$question_answer['option_1']!!}</div></li>
                                                    <li><input {{ifDisabled($selected_option)}} {{ifChecked($selected_option,'option_2')}} onchange="optionSelected(this)" class="q_{{$loop->iteration}}" value="option_2" type="radio" name="q_{{$loop->iteration}}" id=""><div class="opt" onclick="markOption(this)"><span>B</span>{!!$question_answer['option_2']!!}</div></li>
                                                    <li><input {{ifDisabled($selected_option)}} {{ifChecked($selected_option,'option_3')}} onchange="optionSelected(this)" class="q_{{$loop->iteration}}" value="option_3" type="radio" name="q_{{$loop->iteration}}" id=""><div class="opt" onclick="markOption(this)"><span>C</span>{!!$question_answer['option_3']!!}</div></li>
                                                    <li><input {{ifDisabled($selected_option)}} {{ifChecked($selected_option,'option_4')}} onchange="optionSelected(this)" class="q_{{$loop->iteration}}" value="option_4" type="radio" name="q_{{$loop->iteration}}" id=""><div class="opt" onclick="markOption(this)"><span>D</span>{!!$question_answer['option_4']!!}</div></li>
                                                </ul>
                                            </div>
                                        </div>
                                    </li>
                                    @endforeach
                                </ul>
                                
                                <span class="omrSheet">OMR SHEET</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="infos-area">
                            <span class="omrSheetClose">Back</span>
                            <div class="info-box">
                                <div class="info-head">
                                    <ul>
                                        <li>Candidate Name: {{auth()->user()->name}}</li>  
                                        <li>
                                            <p style="color:red" >Sometimes, when we're unable to generate questions as per your expectations, we adjust our approach and provide you with a question based on our AI logic.</p>
                                        </li>  

                                    </ul>
                                </div>
                                <div class="exam-review">
                                    <ul>
                                        <li>
                                            <p>Exam Type - <span>Self Exam</span></p>
                                            <p>Question Type - <span>MCQ</span></p>
                                            <p>Exam Name - <span>{{$self_exams->self_exam_title}}</span></p>
                                            <p>Marks: <span> {{ $self_exams->marks_per_question * $self_exams->number_of_question }}</span> </p></span> </p>

                                                <button type="button" class="btn btn-success timer-btn" style="background-color: #008000; color: #fff; border-color: #008000;">
                                                    Time: 
                                                    {{-- <span id="time_remain" data-remaintime="{{$exam->total_duration}}">{{$exam->total_duration}}:00</span> --}}
                                                    <span  class="tanha" id="time_remain" data-remaintime="{{$remaining_minutes}}">00:00</span> 
                                                </button>
                                        </li>
                                        <li>
                                            <div class="aer">
                                                <ul>
                                                    <li>
                                                        <span>Answered =</span> 
                                                        <span class="black-round"></span>
                                                        <span class="q-count" id="answered_count">0</span>
                                                    </li>
                                                    <li>
                                                        <span>On Review =</span> 
                                                        <span class="blue-round"></span>
                                                        <span class="q-count" id="on_review_count">0</span>
                                                    </li>
                                                    <li>
                                                        <span>Not Answered =</span> 
                                                        <span class="white-round"></span>
                                                        <span class="q-count" id="not_answered_count">0</span>
                                                    </li>
                                                </ul>
                                            </div>
                                                <button type="button" onclick='document.getElementById("mcq_generate_result").submit()' class="btn btn-success end-exam-btn">Finish Exam</button>
                                        </li>
                                    </ul>
                                </div>
                                <div class="answer-sheet" data-subject_id="{{$question_answer['subject_id']}}">
                                    <ul class="as-list">
                                        @foreach ($question_answers as $key => $question_answer)
                                        @php
                                            // $selected_option=$question_answer['selected_option'];
                                            $selected_option = $selectedQuestions[$key]->selected_option ?? null ;
                                        @endphp
                                        <li id="answer_{{$question_answer['id']}}" >
                                            <a href="#section_{{$loop->iteration}}"><span class="asl-count">{{$loop->iteration}}</span></a>
                                            <p><input onclick="javascript: return false;" {{ifChecked($selected_option,'option_1')}} value="option_1" readonly type="radio" name="a_{{$loop->iteration}}" id=""><span>A</span></p>
                                            <p><input onclick="javascript: return false;" {{ifChecked($selected_option,'option_2')}} value="option_2" readonly type="radio" name="a_{{$loop->iteration}}" id=""><span>B</span></p>
                                            <p><input onclick="javascript: return false;" {{ifChecked($selected_option,'option_3')}} value="option_3" readonly type="radio" name="a_{{$loop->iteration}}" id=""><span>C</span></p>
                                            <p><input onclick="javascript: return false;" {{ifChecked($selected_option,'option_4')}} value="option_4" readonly type="radio" name="a_{{$loop->iteration}}" id=""><span>D</span></p>
                                        </li>
                                        @endforeach
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
        <form id="mcq_generate_result" action="{{route('selfMcqGenerateResult')}}" method="post">
            {{-- @csrf --}}
            <input type="hidden" name="id" value="{{$enc_id}}">
        </form>
    </div>
    <!-- /Main Content Wrapper -->

    <!-- Scripts -->
    <!-- jQuery Plugin -->
    <script src="{{asset('question/assets/js/jquery-3.2.0.min.js')}}"></script>
    <script src="{{asset('question/assets/js/jquery-ui.js')}}"></script>
    <script src="{{asset('question/assets/js/jquery.waypoints.min.js')}}"></script>
    <!-- Bootstrap JS -->
    <script src="{{asset('question/assets/js/bootstrap.min.js')}}"></script>
    <!-- Main Script -->
    <script src="{{asset('question/assets/js/theme.js')}}"></script>
    <script>
        var csrf=$('meta[name="csrf-token"]').attr('content');
        const single_answer_url=`{{route('selfMcqSingleAnswer')}}`;
        function isAnswered(parent){
            var inps=parent.find('input');
            for(var inp of inps){
                if(inp.checked==true){
                    return true;
                }
            }
            return false;
        }
        function markOption(obj){
            var obj=$(obj);
            var parent=obj.closest('.select-answer');
            if(isAnswered(parent)){
                return;
            }
            var opts=parent.find('.opt');
            for(var opt of opts){
                $(opt).removeAttr('style');
            }
            var qa_id=parent.data('qa_id');
            var current_option=$(obj.siblings()[0]).val();
            console.log(current_option);
            var ansOptions = $("#answer_"+qa_id).find('input');
            for(var ansOption of ansOptions){
                if(ansOption.value==current_option){
                    $($(ansOption).siblings()[0]).css({'border':'1px solid green','color':'green'});
                }
                else{
                    $($(ansOption).siblings()[0]).removeAttr('style');
                }
            }
            // for(var ansOption of ansOptions){
            //     if(ansOption.value==selected_option){
            //         $(ansOption).removeAttr('style');
            //     }
            // }
            // console.log(opts);
            obj.css('color','green');
            parent.data('on_review',true);
            updateOverallStatus();
        }
        function optionSelected(obj){
            var option=$(obj);
            var selected_option=option.val();
            var parent=option.closest('.select-answer');
            var qa_id=parent.data('qa_id');
            var subject_id=parent.data('subject_id');
            var test_id =  {{ $id }} ;
            var exam_id = {{$self_exams->id}};

            $.post(single_answer_url, {
                '_token':csrf,
                'qa_id':qa_id,
                'id' : {{$id}},
                'option':selected_option,
                'exam_id' : exam_id,
                'subject_id' : subject_id,
            },
            function(data,status){
                if(status=='success'){
                    parent.data('on_review',false);
                    parent.data('answered',true);
                    var opts=parent.find('.opt');
                    for(var opt of opts){
                        $(opt).removeAttr('style');
                    }
                    var ansOptions = $("#answer_"+qa_id).find('input');
                    for(var ansOption of ansOptions){
                        $($(ansOption).siblings()[0]).removeAttr('style');
                        if(ansOption.value==selected_option){
                            ansOption.checked=true;
                        }
                    }
                    var inps=parent.find('input');
                    for(var inp of inps){
                        $(inp).prop( "disabled", true );
                    }
                    updateOverallStatus();
                }else{
                    var inps=parent.find('input');
                    for(var inp of inps){
                        inp.checked=false;
                    }
                }
            });
        }

        function updateOverallStatus(){
            var answered_count=0;
            var on_review_count=0;
            var answers=$('.select-answer');
            for(var answer of answers){
                answer=$(answer);
                if(answer.data('answered')==true){
                    answered_count++;
                }
                if(answer.data('on_review')==true){
                    on_review_count++;
                }
            }
            $("#answered_count").text(answered_count)
            $("#answered_count_2").text(answered_count)
            $("#not_answered_count").text({{$self_exams->number_of_question}} - answered_count)
            $("#not_answered_count_2").text({{$self_exams->number_of_question}} - answered_count)
            if({{$self_exams->number_of_question}} - answered_count<1){
                document.getElementById("mcq_generate_result").submit();
            }
            $("#on_review_count").text(on_review_count);
            $("#on_review_count_2").text(on_review_count);
        }
        
        updateOverallStatus();
        
        $(".omrSheet").on('click', function(){
            $(".infos-area").addClass("viewMobileOMR");
            $("body").addClass('fixed-body');
        });
        $(".omrSheetClose").on('click', function(){
            $(".infos-area").removeClass("viewMobileOMR");
            $("body").removeClass('fixed-body')
        });
        @if(session('disableBackButton'))
        history.pushState(null, null, document.URL);
            window.addEventListener('popstate', function () {
            history.pushState(null, null, document.URL);
        });
        document.addEventListener('DOMContentLoaded', function() {
            window.history.pushState(null, null, window.location.href);
            window.onpopstate = function(event) {
                window.history.go(1);
            };
        });
        @endif
    </script>
</body>

</html>
