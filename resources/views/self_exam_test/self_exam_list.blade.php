@php
    use Carbon\Carbon;
@endphp
<div class="group-exam-archive">
    <x-app-layout>
    <x-slot name="header">
         <style>
             header.header-area img.eph-bg {
                    position: absolute;
                    left: 0;
                    top: 0;
                    width: 100%;
                    height: 100%;
                }
                
                header.header-area {
                    padding: 15px 0px 15px;
                    position: relative;
                    background: #2e7d32;
                }
                header.bg-white.shadow .max-w-7xl.mx-auto {
                    padding: 0px !important;
                    margin-left: 0px !important;
                    margin-right: 0px !important;
                }
                
                header.header-area .exam-logo h2 {
                    font-size: 25px;
                    color: #fff;
                    font-weight: 700;
                    margin: 0;
                    text-align: center;
                }
                
         </style>
        <header class="header-area">
            <div class="container">
                <div class="row">
                    <div class="col-md-12">
                        <div class="exam-logo">
                            <h2>Self Exam List</h2>
                        </div>
                    </div>
                </div>
            </div>
        </header>
    </x-slot>
    @push('css')
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-beta.1/dist/css/select2.min.css" rel="stylesheet" />
    @endpush
    @push('scripts')
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-beta.1/dist/js/select2.min.js"></script>
    @endpush
    <style>
        @media(max-width: 767px){
            .formbl {
                overflow-x: scroll !important;
            } 
            
            .group-exam-archive {}

            .group-exam-archive header.bg-white>div {
                padding: 0px;
            }
            
            .this-page-title {
                position: absolute;
                top: -45px;
                left: 46px;
            }
            
            .group-exam-archive-box {
                position: relative;
                padding: 5px 0px !important;
            }
            
            .group-exam-archive-box>.bg-white {
                border-radius: 10px;
            }
            
            table.group-exam-archive-table {}
            
            table.group-exam-archive-table tr {}
            
            table.group-exam-archive-table tr th {}
            
            table.group-exam-archive-table tr th:nth-child(3),table.group-exam-archive-table tr td:nth-child(3) {
                display: none;
            }
            
            table.group-exam-archive-table tr td:nth-child(2) {}
            
            table.group-exam-archive-table tr td:first-child {
                padding: 5px 8px;
            }
            
            table.group-exam-archive-table tr td {
                vertical-align: middle;
            }
            
            table.group-exam-archive-table tr td button {
                margin: 2px 0px;
            }
            table.group-exam-archive-table tr td {
                font-size: 12px;
            }
            
            table.group-exam-archive-table tr td button {
                font-size: 10px;
                padding: 1px 5px 0px;
                line-height: 16px;
                display: inline-block;
            }
            
            table.group-exam-archive-table tr td:nth-child(2) {
                max-width: 100px;
            }
            table.group-exam-archive-table {
                margin: 0px auto;
                background-color: #b3e5fc;
            }
            
            .group-exam-archive-box {
                margin: 15px auto;
            }
            
            table.group-exam-archive-table tr th,table.group-exam-archive-table tr td {
                border-bottom: 5px solid #FFF;
            }
            
            .min-h-screen.bg-gray-100 {
                background-color: #fff;
            }
        }
    </style>
    <div class="py-12 group-exam-archive-box">
        <div class="bg-white overflow-hidden shadow-xl formbl">
            {{-- <x-jet-welcome /> --}}
            <table class="table group-exam-archive-table">
                <thead>
                    <tr>
                        <th>SL</th>
                        <th>Exam Title</th>
                        <th>Time</th>
                        <th>Subject</th>
                        <th>Chapter</th>
                        <th>Institute</th>
                        <th>Unit</th>
                        <th>Year</th>
                        <th>Status</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody>
                  
                    @foreach ($self_exam as $key => $exam)
                        <tr>
                            <td>{{ ++$key}}</td>
                            <td>{!!$exam->self_exam_title!!}</td>
                            <td>{{ Carbon::parse($exam->created_at)->toDayDateTimeString()}}</td>
                            <td>{{ $exam->subject_title }}</td>
                            <td>{{ $exam->chapter_title }}</td>
                            <td>{{ $exam->institute_name }}</td>
                            <td>{{ $exam->unit_title }}</td>
                            <td>{{ $exam->year_id }}</td>
                            <td>{{ $exam->is_finished == 0 ? "Running" : "Finished"}} </td>
                            <td>
                                @if($exam->is_finished == 1)
                                <a href="{{route('self_test_mcq_preview',['id' => $exam->self_exam_tests_id])}}">
                                    <button class="btn btn-outline-success">Result</button>
                                </a>
                                @elseif($exam->is_finished == 0)
                                <a href="{{route('selfExamRunningExamJoin',['id' => $exam->self_exam_tests_id])}}">
                                    <button class="btn btn-outline-info">Start</button>
                                </a>
                                @endif
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
            <div style="padding:20px">
                {!!$self_exam->links()!!}
            </div>
        </div>
    </div>


    <!-- Modal -->
<div class="modal fade" id="examStartWarningModal" data-backdrop="static" data-keyboard="false" tabindex="-1" aria-labelledby="examStartWarningModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="examStartWarningModalLabel">Warning</h5>
          <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          By starting the exam your account might be charged by BDT <span id="warning_price"></span> or 1 Exam unit.
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
          <button onclick="confirmStartExam()" type="button" class="btn btn-primary">Understood</button>
        </div>
      </div>
    </div>
  </div>

    <script>
        var exam_id;
        function showWarning(id,price){
            $("#warning_price").text(price);
            exam_id=id;
        }
        function confirmStartExam(){
            document.location=`{{url('/student/start_group_exam_archive_test')}}/`+exam_id;
        }
        function confirmDelete(obj){
            if(confirm('Do you really want to detete this?')){
                $(obj).closest('form').submit();
            }
        }
    </script>
</x-app-layout>
</div>