@php
    use Illuminate\Support\Facades\Crypt;
@endphp
<x-app-layout>
    <x-slot name="header"> 
        <div class="leaderbord-wrapper">
            <style type = text/css>
                .subject-card {
                  width: 135px;
                  height: 125px;
                  display: inline-block;
                  margin: 20px;
                  font-size: 20px;
                  text-align: center;
                  border-radius: 6px;
                }
                .subject-card:hover {
                  background-color: #f6f6f6;
                }
                .subject-card:active {
                  background-color: #e7e7e7;
                }
                
                h1 {
                  max-width: 750px;
                  margin: 3rem auto;
                }
              
              </style>  
            <div class="text-center">
                 <h1>Self Exam </h1>
                 <span class="text-center"> Self Exam Basic Result</span>
                    <div style="margin-top:10px; margin-bottom:20px">
                        <div  class="subject-card card card-floating card-block"><h3>English</h3></div>
                        <div  class="subject-card card card-floating card-block"><h3>Math</h3></div>
                        <div  class="subject-card card card-floating card-block"><h3>Reading</h3></div>
                        <div class="subject-card card card-floating card-block"><h3>Science</h3></div>
                    </div>
            </div>

            <div class="mlw-head"><h4>Solution Sheet</h4> 
              <span class="ml-current-answer">
                <a href="{{route('self_test_mcq_preview',['id'=>$last_test->id])}}">Correct Ans</a
              ></span>
            </div>

        </div>
</x-app-layout>

<script>
  @if(session('disableBackButton'))
      history.pushState(null, null, document.URL);
          window.addEventListener('popstate', function () {
          history.pushState(null, null, document.URL);
      });

      document.addEventListener('DOMContentLoaded', function() {
          window.history.pushState(null, null, window.location.href);
          window.onpopstate = function(event) {
              window.history.go(1);
          };
      });
  @endif
</script>