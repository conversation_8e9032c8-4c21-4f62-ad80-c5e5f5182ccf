
<!DOCTYPE html>
<html lang="en">

<head>
    <!--- Basic Page Needs  -->
    <meta charset="utf-8">
    <title>{{"Self Test Exam | ".$self_exam->self_exam_title}}</title>
    <meta name="description" content="">
    <meta name="author" content="">
    <meta name="keywords" content="">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <!-- Mobile Specific Meta  -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css?family=Raleway:300,400,500,700" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css?family=Work+Sans:400,500,600,700" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css?family=Ubuntu:300,300i,400,400i,500,500i,700,700i" rel="stylesheet">
    <!-- KaTeX -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/KaTeX/0.9.0/katex.min.css" rel="stylesheet">
    <!-- CSS -->
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="{{asset('question/assets/css/bootstrap.min.css')}}">
    <!-- Jquery ui CSS -->
    <link rel="stylesheet" href="{{asset('question/assets/css/jquery-ui.css')}}">
    <!-- Font Awesome CSS -->
    <link rel="stylesheet" href="{{asset('question/assets/css/font-awesome.min.css')}}">
    <!-- Animate CSS -->
    <link rel="stylesheet" href="{{asset('question/assets/css/animate.css')}}">
    <!-- Main StyleSheet CSS -->
    <link rel="stylesheet" href="{{asset('question/assets/css/style.css')}}">
    <!-- Responsive CSS -->
    <link rel="stylesheet" href="{{asset('question/assets/css/responsive.css')}}">
        <style>
            .single-question .select-answer ul li div{
                display: inline;
            }
            .qoustion-area *{
                font-size: 16px !important;
            }
            input[type='radio'].correct:checked:after {
                background-color: #08ff46 !important;
                border: 2px solid #08ff46 !important;
            }
            input[type='radio'].wrong:checked:after {
                background-color: #ff2d08 !important;
                border: 2px solid #ff2d08 !important;
            }
            span.correct-round {
                background-color: #08ff46;
                border-color: #08ff46 !important;
            }
            span.wrong-round {
                background-color: #ff2d08;
                border-color: #ff2d08 !important;
            }
            .answer-sheet ul.as-list li p input[type="radio"].correct:checked:after {
                    position: absolute;
                content: '';
                height: 100%;
                width: 100%;
                background-color: #02b72f !important;
                top: 0;
                left: 0;
                border-radius: 50%;
                border-color: #02b72f !important;
            }
            .answer-sheet ul.as-list li p input[type="radio"].wrong:checked:after {
                position: absolute;
                content: '';
                height: 100%;
                width: 100%;
                background-color: #ff4322 !important;
                top: 0;
                left: 0;
                border-radius: 50%;
                border-color: #ff4322 !important;
            }
             header.header-area img.eph-bg {
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
                height: 100%;
            }
            header.header-area {
                padding: 20px 0px 12px;
                background: #33691e !important;
            }
            .select-ans-preview ul li .sap-ca {
                background-color: transparent;
                border: none;
                padding: 0px;
                color: #15BDFF;
                position: relative;
                top: 4px;
            }
            .single-question .select-answer ul li {padding: 0px;}
            .select-ans-preview ul li {
                width: auto !important;
                margin-right: 10px !important;
            }
            .explaination-btn {
                position: relative;
            }
            .explaination-btn button {
                background-color: #224156;
                color: #fff;
                border: none;
                padding: 6px 10px;
                font-size: 11px !important;
                border-radius: 15px;
            }
            .explaination-contents {
                position: relative;
                padding: 10px;
            }
            
            .explaination-contents h4 {
                font-size: 13px;
                line-height: 18px;
                color: #15BDFF;
                letter-spacing: 0px;
                margin-bottom: 18px;
            }
            
            .explaination-contents p {
                font-size: 13px;
                color: #000;
                line-height: 23px;
            }
            
            div#reportQuestionModal {}
            
            div#reportQuestionModal .modal-dialog {}
            
            #explainationModal {}
            
            #explainationModal .modal-dialog {
                top: 100px;
            }

            .main-content-wrapper{
                border: none;
            }
            .exam-logo h2{
                font-size: 20px;
                margin-bottom: 10px;
                text-align: center;
            }
            
            
            @media(max-width: 767px){
                .select-ans-preview ul li {
                    width: auto !important;
                    margin-right: 10px !important;
                }
                 .qoustion-area * {
                    font-size: 12px !important;
                    line-height: 18px;
                }
                .select-ans-preview ul li {
                    width: 40% !important;
                }

                .select-ans-preview ul li:first-child {
                    width: 100% !important;
                }
            }
            
            .select-ans-preview li button.btn.btn-warning.btn-sm {
                background-color: #EB3D3D;
                color: #fff;
                border: none;
                padding: 6px 10px;
                font-size: 11px !important;
                border-radius: 15px;
            }
            button#backward {
                    position: relative;
                    padding: 5px 15px;
                    background-color: #007bff;
                    color: #fff;
                    border: 1px solid #007bff;
                    border-radius: 5px;
                    font-size: 14px;
                    line-height: 20px;
                    margin-top: 0px;
                    margin-left: 13px;
                    display: inline-block;
                    margin-bottom: 15px;
                }
        </style>
</head>

<body>
    @php
        function ifChecked($qa,$inp_value){

            if($qa['correct_option'] == $qa['selected_option']){
                $cor = true ;
            }else{
                $cor = false;
            }
            $qa_value = $qa['selected_option'];
            $ret="";
            if($qa_value!=null){
                if($qa_value==$inp_value){
                    $ret= "checked";
                    if($cor){
                        $ret .= ' class="correct"';
                    }else{
                        $ret .= ' class="wrong"';
                    }
                }
            }
            return $ret;
        }

        function getCorrectOption($question_answer){
            switch($question_answer['correct_option']){
                case "option_1":
                    return "A";
                break;
                case "option_2":
                    return "B";
                break;
                case "option_3":
                    return "C";
                break;
                case "option_4":
                    return "D";
                break;
                case "option_5":
                    return "E";
                break;
            }
        }

    @endphp
    <div id="preloader"></div>
    <!-- Header Area --> 
    <!-- Header Area --> 
    <header class="header-area">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <div class="exam-logo">
                        <h2>{{$self_exam->self_exam_title}}</h2>
                    </div>
                    <div class="back-btn" style="text-align: center;">
                            <button class="pageback-button" onClick="previous()"  id="backward">Back</button>
                            <a href="{{ route('student.selfExamSolveSheet',['id'=> $self_exam->id]) }}">
                                <button class="pageback-button"  id="backward">Solve Sheet</button> 
                            </a>
                            <a href="{{ route('selfExamdownloadSolveSheetFromUser',['id'=>$self_exam->id]) }}">
                                <button class="pageback-button"  id="backward">Download</button> 
                            </a> 
                    <script>  
                        function previous() {
                            window.history.back();
                        }
                    </script>
                    </div>
                </div>
            </div>
        </div>
    </header>
    <!-- /Header Area -->
    <!-- /Header Area -->
    <!-- Main Content Wrapper -->
    <div class="main-content-wrapper">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-md-8">
                        @if (session('status'))
                            <div class="alert alert-success">
                                {{ session('status') }}
                            </div>
                        @endif
                        <div class="qoustion-area">
                            <div class="question-box">
                                <ul class="questions q-list">
                                    @foreach ($question_answers as $question_answer)
                                    @php
                                        $question=$question_answer['question_text'];
                                        $selected_option = $question_answer['selected_option'];
                                    @endphp
                                    <li id="section_{{$loop->iteration}}">
                                        <div class="single-question">
                                            <div class="question">
                                                <span class="question-no">{{$loop->iteration}}</span>
                                                <h4>{!! $question !!}</h4>
                                               
                                            </div>
                                            <div  class="select-answer" data-answered="{{$selected_option==null?'false':'true'}}" data-qa_id="{{$question_answer['id']}}" data-on_review="false">
                                                <ul>
                                                    <li><input disabled {!!ifChecked($question_answer,'option_1')!!}  type="radio" id=""><div class="opt" ><span>A</span>{!!$question_answer['option_1']!!}</div></li>
                                                    <li><input disabled {!!ifChecked($question_answer,'option_2')!!}  type="radio" id=""><div class="opt" ><span>B</span>{!!$question_answer['option_2']!!}</div></li>
                                                    <li><input disabled {!!ifChecked($question_answer,'option_3')!!}  type="radio" id=""><div class="opt" ><span>C</span>{!!$question_answer['option_3']!!}</div></li>
                                                    <li><input disabled {!!ifChecked($question_answer,'option_4')!!}  type="radio" id=""><div class="opt" ><span>D</span>{!!$question_answer['option_4']!!}</div></li>
                                                </ul>
                                                <div class="select-ans-preview">
                                                    <ul>
                                                        <li>
                                                            <div class="sap-ca">Ans: {{getCorrectOption($question_answer)}}</div>
                                                        </li> 
                                                        <li>
                                                            <div class="explaination-btn">
                                                                <button   data-toggle="modal" data-target="#explainationModal">Explaination</button>
                                                            </div>
                                                        </li>
                                                        <li>
                                                            {{-- <div style="margin-top: 20px"><button onclick="$('#report_form_question_id').val({{$question_answer->question_id}})" data-toggle="modal" data-target="#reportQuestionModal" class="btn btn-warning btn-sm"><i class="fa fa-exclamation-triangle"></i> Report</button></div> --}}
                                                        </li>
                                                    </ul>
                                                </div>
                                                
                                            </div>
                                        </div>
                                    </li>
                                    @endforeach
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="infos-area">
                            <div class="info-box">
                                <div class="info-head">
                                    <ul>
                                        <li>Name : {{ auth()->user()->name }}</li>
                                        <li>Taken Time : {{ gmdate("H:i:s", $self_exam->time_taken_in_second) }}</li>
                                    </ul>
                                </div>
                                <div class="exam-review">
                                    <ul>
                                        <li>
                                            <p>Exam Name - <span> {{"Self Test Exam | ".$self_exam->self_exam_title}} </span></p>
                                            <p>Exam Type - <span> Self Exam - MCQ </span></p>
                                            <p>Exam Duration - <span> {{ $self_exam->total_duration }} </span></p>
                                            <p>Earned Marks - <span> {{ $self_exam->earned_marks}} </span></p>
                                        </li>
                                        <li>
                                            <div class="aer">
                                                <ul>
                                                    <li>
                                                        <span>Correct Answer</span> 
                                                        <span class="correct-round"></span>
                                                        <span class="q-count" id="answered_count">{{$self_exam->number_of_correct_answers}}</span>
                                                    </li>
                                                    <li>
                                                        <span>Wrong Answer</span> 
                                                        <span class="wrong-round"></span>
                                                        <span class="q-count" id="on_review_count">{{$self_exam->number_of_wrong_answers}}</span>
                                                    </li>
                                                    <li>
                                                        <span>Not Answered</span> 
                                                        <span class="white-round"></span>
                                                        <span class="q-count" id="not_answered_count">{{$self_exam->number_of_not_answered}}</span>
                                                    </li>
                                                </ul>
                                            </div>
                                        </li>
                                    </ul>
                                </div>
                                <div class="answer-sheet">
                                    <ul class="as-list">
                                        @foreach ($question_answers as $question_answer)
                                        @php
                                            $selected_option=$question_answer['selected_option'];
                                        @endphp
                                        <li id="answer_{{$question_answer['id']}}">
                                            <a href="#section_{{$loop->iteration}}"><span class="asl-count">{{$loop->iteration}}</span></a>
                                            <p><input onclick="javascript: return false;" {!!ifChecked($question_answer,'option_1')!!} readonly type="radio"><span>A</span></p>
                                            <p><input onclick="javascript: return false;" {!!ifChecked($question_answer,'option_2')!!} readonly type="radio"><span>B</span></p>
                                            <p><input onclick="javascript: return false;" {!!ifChecked($question_answer,'option_3')!!} readonly type="radio"><span>C</span></p>
                                            <p><input onclick="javascript: return false;" {!!ifChecked($question_answer,'option_4')!!} readonly type="radio"><span>D</span></p>
                                        </li>
                                        @endforeach
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
    </div>
    <!-- /Main Content Wrapper -->


    <!-- Modal -->
    <div class="modal fade" id="reportQuestionModal" tabindex="-1" aria-labelledby="reportQuestionModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
        <form action="{{route('student.question_report')}}" method="post" id="report_form">
        @csrf
        <div class="modal-content">
            <div class="modal-header">
            <h5 class="modal-title" id="reportQuestionModalLabel">Report This Question</h5>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <input type="hidden" name="question_id" id="report_form_question_id" required>
                    <label>Please select what issue you are having</label>
                    <select name="question_issue_type_id" class="form-control" required>
                        <option value="">Please Select an issue</option>
                        @foreach (App\Models\QuestionIssueType::all() as $issue_type)
                        <option value="{{$issue_type->id}}">{{$issue_type->title}}</option>
                        @endforeach
                    </select>
                </div>
                <div class="form-group">
                    <label>Comment</label>
                    <textarea name="comment" class="form-control" cols="30" rows="10"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button class="btn btn-danger">Report</button>
            </div>
        </div>
        </form>
        </div>
    </div>
    
    
     <div class="modal fade" id="explainationModal" tabindex="-1" aria-labelledby="explainationModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg"> 
            <div class="modal-content"> 
                <div class="modal-body">
                     <div class="explaination-contents">
                         <h4>ব্যাখ্যা সংযোজনের কাজ চলছে</h4>
                         
                     </div>
                </div> 
            </div> 
        </div>
    </div>

    <!-- Scripts -->
    <!-- jQuery Plugin -->
    <script src="{{asset('question/assets/js/jquery-3.2.0.min.js')}}"></script>
    <script src="{{asset('question/assets/js/jquery-ui.js')}}"></script>
    <script src="{{asset('question/assets/js/jquery.waypoints.min.js')}}"></script>
    <!-- Bootstrap JS -->
    <script src="{{asset('question/assets/js/bootstrap.min.js')}}"></script>
    
    <script>
        $(window).on('load', function() {
            $('#preloader').fadeOut('fast', function() { $(this).remove(); });
        });
        /*---------------------
        smooth scroll
        --------------------- */
        $('.smoothscroll').on('click', function(e) {
            e.preventDefault();
            var target = this.hash;

            $('html, body').stop().animate({
                'scrollTop': $(target).offset().top - 80
            }, 1200);
        });
    </script>
    
</body>

</html>
