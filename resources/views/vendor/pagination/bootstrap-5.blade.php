@if ($paginator->hasPages())
    <nav class="pagination-div" style="display: flex; justify-content: center; align-items: center; font-family: Arial, sans-serif; padding: 20px;">
        {{-- Previous Page Link --}}
        @if ($paginator->onFirstPage())
            <span style="padding: 8px 12px; border: 1px solid #ccc; border-radius: 4px; margin: 0 5px; color: #ccc;">&laquo; Previous</span>
        @else
            <a href="{{ $paginator->previousPageUrl() }}" style="text-decoration: none; color: #333; padding: 8px 12px; border: 1px solid #ccc; border-radius: 4px; margin: 0 5px;">&laquo; Previous</a>
        @endif

        {{-- Pagination Elements for Desktop --}}
        <div  style="display: flex; @media (max-width: 640px) { display: none; }">
            @foreach ($elements as $element)
                {{-- "Three Dots" Separator --}}
                @if (is_string($element))
                    <span style="padding: 8px 12px; margin: 0 5px;">{{ $element }}</span>
                @endif

                {{-- Array Of Links --}}
                @if (is_array($element))
                    @foreach ($element as $page => $url)
                        @if ($page == $paginator->currentPage())
                            <span style="padding: 8px 12px; margin: 0 5px; background-color: #335B47; color: #fff; border-radius: 4px;">{{ $page }}</span>
                        @else
                            <a href="{{ $url }}" style="text-decoration: none; color: #333; padding: 8px 12px; border: 1px solid #ccc; border-radius: 4px; margin: 0 5px;">{{ $page }}</a>
                        @endif
                    @endforeach
                @endif
            @endforeach
        </div>

        {{-- Next Page Link --}}
        @if ($paginator->hasMorePages())
            <a href="{{ $paginator->nextPageUrl() }}" style="text-decoration: none; color: #333; padding: 8px 12px; border: 1px solid #ccc; border-radius: 4px; margin: 0 5px;">Next &raquo;</a>
        @else
            <span style="padding: 8px 12px; border: 1px solid #ccc; border-radius: 4px; margin: 0 5px; color: #ccc;">Next &raquo;</span>
        @endif
    </nav>
@endif

<style>
    @media (max-width: 640px) {
        nav > div {
            display: none !important;
        }
    }
</style>