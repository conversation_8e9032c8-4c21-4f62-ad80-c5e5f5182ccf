@extends('frontend.layouts.app')
@php
    $userRelation = getUserRoleRelation($user);
@endphp
@push('style')
    <style>
        .exam-center-parent {
            padding-top: 0px !important;
        }
    </style>
     <style>
        /* Additional styles for modal visibility */
        .about-modal {
            display: none; /* Initially hidden */
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            align-items: center;
            justify-content: center;
        }

        .about-modal .about-modal-content {
            background: white;
            width: 95%;
            max-width: 800px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            max-height: 90vh;
            overflow-y: auto;
        }

        .tab-button {
            padding: 12px 0;
            border: none;
            background: none;
            font-size: 14px;
            font-weight: 500;
            color: #6b7280; /* Default color for inactive tabs */
            cursor: pointer;
        }
        .tab-button.active {
            color: #000; /* Active tab color */
            border-bottom: 2px solid #000; /* Active tab underline */
        }
    </style>
@endpush
@section('content')
    <div class="instructor-profile-page">

        {{-- Header Background --}}
        <div class="header-background">
            <img src="{{getImageFile($user->cover_image ?? '')}}" alt="Header Background">
        </div>
        {{-- {{getImageFile($user->image)}} --}}
        <div class="profile-section">

            {{-- Profile Header --}}
            <div class="profile-header">
                <div class="profile-image">
                    <img src="{{ getImageFile($user->image ?? '') }}" alt="{{ $user->name ?? '' }}">
                    <span class="online-badge">Online</span>
                </div>
                <div class="profile-info">
                    <h1>{{ $user->name ?? ''}}</h1>
                    <h2>{{ $user->instructor ? $user->instructor->professional_title : '' }}</h2>
                    <div class="action-buttons">
                        <button class="btn" id="openModal">
                            <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                            <span class="btn-text">About</span>
                        </button>
                        <button class="btn">
                            <svg width="16" height="16" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"
                                fill="currentColor">
                                <path
                                    d="M14.5998 8.00033H21C22.1046 8.00033 23 8.89576 23 10.0003V12.1047C23 12.3659 22.9488 12.6246 22.8494 12.8662L19.755 20.3811C19.6007 20.7558 19.2355 21.0003 18.8303 21.0003H2C1.44772 21.0003 1 20.5526 1 20.0003V10.0003C1 9.44804 1.44772 9.00033 2 9.00033H5.48184C5.80677 9.00033 6.11143 8.84246 6.29881 8.57701L11.7522 0.851355C11.8947 0.649486 12.1633 0.581978 12.3843 0.692483L14.1984 1.59951C15.25 2.12534 15.7931 3.31292 15.5031 4.45235L14.5998 8.00033ZM7 10.5878V19.0003H18.1606L21 12.1047V10.0003H14.5998C13.2951 10.0003 12.3398 8.77128 12.6616 7.50691L13.5649 3.95894C13.6229 3.73105 13.5143 3.49353 13.3039 3.38837L12.6428 3.0578L7.93275 9.73038C7.68285 10.0844 7.36341 10.3746 7 10.5878ZM5 11.0003H3V19.0003H5V11.0003Z">
                                </path>
                            </svg>
                            <span class="btn-text">Follow</span>
                        </button>
                        <button class="btn">
                            <svg width="16" height="16" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"
                                fill="currentColor">
                                <path
                                    d="M12.0006 18.26L4.94715 22.2082L6.52248 14.2799L0.587891 8.7918L8.61493 7.84006L12.0006 0.5L15.3862 7.84006L23.4132 8.7918L17.4787 14.2799L19.054 22.2082L12.0006 18.26ZM12.0006 15.968L16.2473 18.3451L15.2988 13.5717L18.8719 10.2674L14.039 9.69434L12.0006 5.27502L9.96214 9.69434L5.12921 10.2674L8.70231 13.5717L7.75383 18.3451L12.0006 15.968Z">
                                </path>
                            </svg>
                            <span class="btn-text">Review</span>
                        </button>
                        <button class="btn" data-url="{{url()->current()}}" id="shareLink">
                            <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z">
                                </path>
                            </svg>
                            <span class="btn-text">Share</span>
                        </button>
                        <button class="btn">
                            <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z">
                                </path>
                            </svg>
                            <span class="btn-text">Message</span>
                        </button>
                    </div>
                </div>
            </div>

            {{-- Stats Area --}}
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">4.9</div>
                    <div class="rating-stars">★★★★★</div>
                    <div class="stat-label">Average Rating</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">15,000+</div>
                    <div class="stat-label">Students</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">25</div>
                    <div class="stat-label">Courses</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">98%</div>
                    <div class="stat-label">Completion Rate</div>
                </div>
            </div>

            {{-- Instructor Profile Tab --}}
            <div class="instructor-profile-tab">
                <ul class="nav nav-tabs" id="myTab" role="tablist">
                    {{-- <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="about" data-bs-toggle="tab" data-bs-target="#active"
                            type="button" role="tab" aria-controls="active" aria-selected="true">About</button>
                    </li> --}}

                    <li class="nav-item active" role="presentation">
                        <button class="nav-link active" id="examCenter-sec" data-bs-toggle="tab" data-bs-target="#link2"
                            type="button" role="tab" aria-controls="link2" aria-selected="false">Exam
                            Center</button>
                    </li>

                    {{-- <li class="nav-item" role="presentation">
                        <button class="nav-link" id="courses" data-bs-toggle="tab" data-bs-target="#link1" type="button"
                            role="tab" aria-controls="link1" aria-selected="false">Courses</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="reviews" data-bs-toggle="tab" data-bs-target="#link3"
                            type="button" role="tab" aria-controls="link2" aria-selected="false">Reviews</button>
                    </li> --}}
                </ul>
                <div class="tab-content" id="myTabContent">
                    <div class="tab-pane fade show active" id="link2" role="tabpanel" aria-labelledby="examCenter-sec">
                        @include('frontend.student.exam_center.content.exam_center_content')
                    </div>
                    {{-- <div class="tab-pane fade show active" id="active" role="tabpanel" aria-labelledby="about">
                        <div class="content-section">
                            <h3 class="bio-title">Bio</h3>
                            <p class="bio-text">
                                Experienced Chemistry instructor with over 10 years of teaching experience. Specializing
                                in organic
                                chemistry and helping students achieve their academic goals.
                            </p>
                            <div class="contact-info">
                                <div class="contact-item">
                                    <svg width="16" height="16" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z">
                                        </path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    </svg>
                                    New York, USA
                                </div>
                                <div class="contact-item">
                                    <svg width="16" height="16" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z">
                                        </path>
                                    </svg>
                                    <EMAIL>
                                </div>
                                <div class="contact-item">
                                    <svg width="16" height="16" fill="none" stroke="currentColor"
                                        viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z">
                                        </path>
                                    </svg>
                                    Member since 2020
                                </div>
                            </div>
                        </div>
                    </div> --}}
                    {{-- <div class="tab-pane fade" id="link1" role="tabpanel" aria-labelledby="courses">
                        <p class="mt-3">world</p>
                    </div> --}}
                    {{-- <div class="tab-pane fade" id="link2" role="tabpanel" aria-labelledby="reviews">
                        <p class="mt-3">This is the content for Link 2 tab. You can add more details here as needed.
                        </p>
                    </div> --}}
                </div>

            </div>
            <!-- About Modal Container -->
            <div id="modal" class="about-modal">
                <!-- Modal Content -->
                <div class="about-modal-content">
                    <!-- Modal Header -->
                    <div
                        style="display: flex; justify-content: space-between; align-items: center; padding: 20px 24px; border-bottom: 1px solid #e5e7eb;">
                        <h2 style="margin: 0; font-size: 24px; font-weight: 600;">About {{ $user->name ?? ''}}</h2>
                        <button id="closeModal"
                            style="background: none; border: none; font-size: 24px; cursor: pointer; padding: 0; color: #6b7280;">×</button>
                    </div>
            
                    <!-- Tabs Navigation -->
                    <div style="padding: 0 24px; border-bottom: 1px solid #e5e7eb;">
                        <div style="display: flex; gap: 32px; margin-bottom: -1px;">
                            <button class="tab-button active" onclick="switchTab('overview')">Overview</button>
                            <button class="tab-button" onclick="switchTab('expertise')">Expertise</button>
                            <button class="tab-button" onclick="switchTab('education')">Education</button>
                            <button class="tab-button" onclick="switchTab('awards')">Awards</button>
                        </div>
                    </div>
            
                    <!-- Tab Content -->
                    <div style="padding: 24px;">
                        <!-- Overview Tab (Active by default) -->
                        <div id="overview" style="display: block;">
                            <div style="margin-bottom: 24px;">
                                <h3 style="font-size: 20px; font-weight: 600; margin: 0 0 16px;">Biography</h3>
                                <p style="margin: 0; color: #6b7280; line-height: 1.5;">
                                   {{ $user->instructor ? $user->instructor->about_me : '' }}
                                </p>
                            </div>
            
                            <!-- Info Grid -->
                            <div
                                style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 24px; margin-bottom: 24px;">
                                {{-- <div style="display: flex; align-items: center; gap: 8px;">
                                    <span style="color: #6b7280;">🕒</span>
                                    <span>Teaching since 2015</span>
                                </div>
                                <div style="display: flex; align-items: center; gap: 8px;">
                                    <span style="color: #6b7280;">📚</span>
                                    <span>25 Active Courses</span>
                                </div> --}}
                                <div style="display: flex; align-items: center; gap: 8px;">
                                    <span style="color: #6b7280;">📍</span>
                                    <span>{{ $user->instructor ? $user->instructor->address : '' }}</span>
                                </div>
                                <div style="display: flex; align-items: center; gap: 8px;">
                                    <span style="color: #6b7280;">🌐</span>
                                    <span>{{ $user->instructor ? $user->instructor->professional_title : '' }}</span>
                                </div>
                            </div>
            
                            <!-- Contact Information -->
                            <div>
                                <h3 style="font-size: 20px; font-weight: 600; margin: 0 0 16px;">Contact Information</h3>
                                <div style="display: flex; flex-direction: column; gap: 12px;">
                                    <div style="display: flex; align-items: center; gap: 8px;">
                                        <span style="color: #6b7280;">✉️</span>
                                        <span>{{ $user->email ?? '' }}</span>
                                    </div>
                                    <div style="display: flex; align-items: center; gap: 8px;">
                                        <span style="color: #6b7280;">📞</span>
                                        <span>{{ $user->instructor ? $user->instructor->phone_number : '' }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
            
                        <!-- Expertise Tab (Hidden by default) -->
                        <div id="expertise" style="display: none;">
                            <h3 style="font-size: 20px; font-weight: 600; margin: 0 0 16px;">Expertise</h3>
                            <p style="margin: 0; color: #6b7280; line-height: 1.5;">
                                Currently, there is no information available for Expertise. Please check back later
                            </p>
                        </div>
            
                        <!-- Education Tab (Hidden by default) -->
                        <div id="education" style="display: none;">
                            <h3 style="font-size: 20px; font-weight: 600; margin: 0 0 16px;">Education</h3>
                            <p style="margin: 0; color: #6b7280; line-height: 1.5;">
                                Education details are not available at the moment. Please check back later.
                            </p>
                        </div>
            
                        <!-- Awards Tab (Hidden by default) -->
                        <div id="awards" style="display: none;">
                            <h3 style="font-size: 20px; font-weight: 600; margin: 0 0 16px;">Awards</h3>
                            <p style="margin: 0; color: #6b7280; line-height: 1.5;">
                                No awards information is available right now. Please check back later.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            <!-- About Modal Container -->
        </div>
    </div>

    <input type="hidden" value="3" class="course_paginate_number">
    <input type="hidden" class="instructorCoursePaginateRoute"
        value="{{ route('instructorCoursePaginate', $user->id) }}">
    <input type="hidden" class="organizationInstructorsPaginateRoute"
        value="{{ route('organizationInstructorPaginate', $user->id) }}">

    @include('frontend.home.partial.consultation-booking-schedule-modal')
@endsection

@push('script')
    <script>
        const followRoute = "{{ route('follow') }}";
        const unfollowRoute = "{{ route('unfollow') }}";
        const userInstructorId = "{{ $user->id }}";
    </script>
    <script src="{{ asset('frontend/assets/js/course/addToCart.js') }}"></script>
    <script src="{{ asset('frontend/assets/js/course/addToWishlist.js') }}"></script>
    <script src="{{ asset('frontend/assets/js/custom/instructor-course-paginate.js') }}"></script>
    <script src="{{ asset('frontend/assets/js/custom/booking.js') }}"></script>
    <script src="{{ asset('frontend/assets/js/custom/follow.js') }}"></script>

   <script>
        // Function to switch tabs
        function switchTab(tabId) {
            // Hide all tabs
            document.querySelectorAll('[id^="overview"], [id^="expertise"], [id^="education"], [id^="awards"]')
                .forEach(tab => tab.style.display = 'none');
            
            // Show selected tab
            document.getElementById(tabId).style.display = 'block';
            
            // Update tab button styles
            const buttons = document.querySelectorAll('.tab-button');
            buttons.forEach(button => {
                if (button.textContent === tabId.charAt(0).toUpperCase() + tabId.slice(1)) {
                    button.classList.add('active'); // Add active class
                } else {
                    button.classList.remove('active'); // Remove active class
                }
            });
        }

        // Function to close the modal
        document.getElementById('closeModal').onclick = function() {
            document.getElementById('modal').style.display = 'none';
        };

        // Function to open the modal
        document.getElementById('openModal').onclick = function() {
            document.getElementById('modal').style.display = 'flex';
        };


        // Share link button click event
        document.getElementById('shareLink').onclick = function() {
            const url = this.getAttribute('data-url');
            copyToClipboard(url);
        };


        function copyToClipboard(url) {
            const linkToCopy = url;
            if (navigator.clipboard && navigator.clipboard.writeText) {
                // Use the modern clipboard API if supported
                navigator.clipboard.writeText(linkToCopy)
                    .then(() => {
                        toastr.success('Link copied to clipboard!');
                    })
                    .catch(err => {
                        toastr.error('Failed to copy link to clipboard!');
                    });
            } else {
                // Fallback for older browsers
                const tempTextarea = $('<textarea>'); // Create a temporary textarea element
                $('body').append(tempTextarea); // Append it to the body
                tempTextarea.val(linkToCopy).select(); // Set its value and select the text
                try {
                    document.execCommand('copy'); // Copy the text
                    toastr.success('Link copied to clipboard!');
                } catch (err) {
                    toastr.error('Failed to copy link to clipboard!');
                }
                tempTextarea.remove(); // Remove the temporary element
            }
        }
    </script>
    
@endpush
