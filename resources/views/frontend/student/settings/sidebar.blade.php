<div class="col-lg-3 p-0">
    <div class="student-profile-left-part">
        <h6>{{ @Auth::user()->name }}</h6>
        <ul>
         
            @if (Auth::user()->role == USER_ROLE_STUDENT)
            <li><a class="dropdown-item {{active_if_full_match('student/my-learning')}}" href="{{ route('student.my-learning') }}">
                {{__('My Learning')}}
               </a>
            </li>
            <li>
                <a class="dropdown-item {{active_if_full_match('student/my-consultation')}}" href="{{ route('student.my-consultation') }}">
                     {{__('My Consultation') }}
                </a>
            </li>
            <li>
                <a class="dropdown-item {{active_if_full_match('/student/chat')}}" href="{{ route('student.chat.index') }}">
                    {{__('Chat') }}
                </a>
            </li>
            <li>
                <a class="dropdown-item {{active_if_full_match('student/my-consultation')}}" href="{{ route('student.my-consultation') }}">
                     {{__('My Exam Module') }}
                </a>
            </li>
            <li>
                <a class="dropdown-item {{active_if_full_match('student/wishlist')}}" href="{{ route('student.wishlist') }}">
                    {{__('Wishlist')}}</a>
            </li>
            @endif
            <li><a href="{{ route('student.profile') }}" class="font-medium font-15 {{active_if_full_match('student/profile')}}">{{__('Profile')}}</a></li>
            {{-- <li><a href="{{ route('student.my-attended-exam') }}" class="font-medium font-15 {{active_if_full_match('student/my-attended-exam')}}">{{__('My Attended Exam')}}</a></li> --}}
            <li><a href="{{ route('student.address') }}" class="font-medium font-15 {{active_if_full_match('student/address')}}">{{__('Address & Location')}}</a></li>
            <li><a href="{{ route('student.change-password') }}" class="font-medium font-15 {{active_if_full_match('student/change-password')}}">{{__('Change Password')}}</a></li>
        </ul>
    </div>
</div>
