<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Exam Sheet - Inverse School</title>

    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet"
        integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
    <link href="{{ asset('frontend/assets/css/group-exam/examCenter.css') }}" rel="stylesheet">
    <link href="{{ asset('frontend/assets/css/group-exam/custom.css') }}" rel="stylesheet">
    <link href="{{ asset('frontend/assets/css/group-exam/responsive.css') }}" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="{{ asset('frontend/assets/katex/katex.min.css') }}">
    <link rel="stylesheet" href="{{ asset('frontend/assets/katex/fonts/katex_Main-Regular.woff2') }}">
</head>

<body>
    <style>
        .katex-display {
            display: block;
            margin: 0;
            text-align: unset;
        }
        /* Mobile Responsive Styles */
        @media (max-width: 768px) {
            .container {
                width: 100%;
                max-width: 100%;
                /* Ensure container fits within its parent */
                padding: 10px;
                /* Add padding for better spacing */
                box-sizing: border-box;
                /* Include padding and borders in width */
                overflow-x: hidden;
                /* Prevent horizontal scroll */
            }

            /* General KaTeX Styles */
            .katex .mord {
                max-width: 100%;
                /* Prevent stretching */
                display: inline-block;
                /* Ensure proper rendering */
            }

            .katex .mroot {
                display: inline-block;
                /* Proper root display */
            }

            .katex .katex-root {
                width: auto;
                /* Prevent excessive width */
                overflow: hidden;
                /* Hide unwanted parts */
            }

            /* Math Equation Styles */
            .math-equation {
                display: block;
                /* Ensure block-level formatting */
                white-space: normal;
                /* Prevent content stretching */
                text-align: left;
                /* Align multiline equations properly */
                overflow-x: auto;
                /* Allow scrolling for long expressions */
                font-size: 16px;
                /* Default font size */
                line-height: 1.5;
                /* Improve readability */
            }

            /* KaTeX Block Equations */
            .katex-display {
                margin-bottom: 8px !important;
                /* Add space below equations */
                text-align: center !important;
                /* Center-align block equations */
                max-width: 100% !important;
                /* Prevent overflow */
                overflow-x: auto !important;
                /* Handle long equations with scrolling */
            }

            /* KaTeX Container for Responsiveness */
            .katex-container {
                max-width: 100%;
                word-wrap: break-word;
                /* Handle long lines */
                overflow-wrap: break-word;
                /* Break words as needed */
                box-sizing: border-box;
                /* Include padding in dimensions */
            }

            /* Question Text Styles */
            .question-text {
                font-size: 16px;
                /* Default font size */
                line-height: 1.5;
                /* Improve readability */
                word-break: break-word;
                /* Break long words to fit */
                overflow-wrap: break-word;
                /* Support breaking in all cases */
                margin-bottom: 10px;
                /* Add spacing below the text */
                text-align: left;
                /* Align text properly */
            }
        }
        /* Mobile Responsive Styles */
        @media (max-width: 768px) {
            .math-equation {
                font-size: 14px;
                /* Reduce size for smaller screens */
            }

            .katex-display {
                font-size: 14px;
                /* Adjust equation size */
            }

            .question-text {
                font-size: 14px;
                /* Adjust text size for smaller screens */
            }

            .container {
                padding: 8px;
                /* Adjust padding for mobile */
            }
        }
         /* Handle Long Inline Math Equations */
        .katex .katex-html {
            max-width: 100%;
            /* Restrict inline equation width */
            white-space: nowrap;
            /* Prevent wrapping of inline equations */
            overflow-x: auto;
            /* Allow scrolling for long equations */
        }

        /* Improved Spacing Between Text and Equations */
        .katex-container+.question-text {
            margin-top: 8px;
        }

        /* Scrollbar Customization (Optional) */
        .math-equation::-webkit-scrollbar {
            height: 6px;
            /* Adjust scrollbar height */
        }

        .math-equation::-webkit-scrollbar-thumb {
            background: #ddd;
            /* Customize scrollbar thumb color */
            border-radius: 3px;
            /* Rounded scrollbar thumb */
        }

        .math-equation::-webkit-scrollbar-thumb:hover {
            background: #bbb;
            /* Darker color on hover */
        }
    </style>
    <div class="container exam-mcq-and-omr">
        {{--  Header  --}}
        <header>
            <div class="row justify-content-between align-items-end">
                <div class="col-md-5 col-sm-5">
                    <div class="header">
                        <div class="title d-flex">
                            <div class="exam-icon">
                                <i class="fas fa-book"></i>
                                {!! $exam->title ?? '' !!}
                            </div>
                        </div>
                        <div class="progress-item">
                            <i class="fas fa-clock left-time" style="color: #d8560a;"></i>
                            <span id="time-left">{{ $remaining_time['time'] . ':00' ?? 0 }}</span>
                        </div>
                    </div>
                </div>
                <div class="col-md-2 col-sm-2">
                    <div class="header-middle">
                        <i class="fas fa-tasks" style="color: #4CAF50;"></i>
                        <span id="selected-count" style="color:#45a049;font-weight: 900;">0</span> | <span
                            id="remaining-count"
                            style="color:#d8560a;font-weight: 900;">{{ $exam->number_of_question ?? 0 }}</span>
                    </div>
                </div>
                <div class="col-md-5 col-sm-5">
                    <div class="header-right">
                        <div class="exam-info">
                            <i class="fas fa-clock" style="color: green;"></i>
                            {{ $exam->total_duration ?? 0 }} min |
                            <i class="far fa-question-circle" style="color: #2196F3;"></i>MCQ
                        </div>
                        <div class="progress-container">
                            <div class="button-container">
                                <button class="button omr-button trigger" data-bs-toggle="offcanvas"
                                    href="#offcanvasExample" role="button"
                                    aria-controls="offcanvasExample">OMR</button>
                                <button class="button finish-button" onclick="submitExam()">Finish Exam</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </header>
        {{-- Questions Section --}}
        <div class="row">
            <div class="col-lg-8 col-md-12">
                <div class="mcq-questions-area">
                    @forelse ($questions as $key => $question)
                        @php
                            $keyId = $key + 1;
                            $selectedOption = $questions_answer[$question->question_id] ?? null;
                            $options = [
                                'option_1' => ['label' => 'A', 'text' => $question->option_1 ?? ''],
                                'option_2' => ['label' => 'B', 'text' => $question->option_2 ?? ''],
                                'option_3' => ['label' => 'C', 'text' => $question->option_3 ?? ''],
                                'option_4' => ['label' => 'D', 'text' => $question->option_4 ?? ''],
                            ];
                            if ($question->academy_type == 13) {
                                $options['option_5'] = ['label' => 'E', 'text' => $question->option_5 ?? ''];
                            }
                        @endphp
                        <div class="mcq-question">
                            {{-- <div style="display: flex; gap: 5px;">
                                <p class="question-wrapper-title">{{ $keyId }}. {!! $question->question_text !!}</p>
                            </div> --}}
                            <div class="question-text"
                                style="display: flex; align-items: flex-start; gap: 8px; font-size: 1rem; font-weight: 500; color: #111827;">
                                <span class="" style="min-width: 24px; text-align: center; font-weight: bold; color: #333;">{{$keyId }}.</span>
                                <div class="math-equation katex-display" style="flex: 1;">
                                    {!! $question->question_text !!}
                                </div>
                            </div>
                            <div class="mcq-options" id="mcq-content-{{ $keyId }}">
                                @foreach ($options as $optionKey => $option)
                                    <label class="mcq-option">
                                        <input type="radio"
                                            onclick="selectOption({{ $keyId }},{{ $loop->index + 1 }},{{ $question->question_id }})"
                                            name="{{ $question->question_id }}[{{ $optionKey }}]"
                                            data-keyid="{{ $keyId }}"
                                            data-option="{{ str_replace('option_', '', $optionKey) }}"
                                            data-question-id="{{ 0 }}"
                                            data-letter={{ $option['label'] }}
                                            {{ $selectedOption === $optionKey ? 'checked' : '' }} class="option-input live-exam-input">
                                        {!! $option['text'] !!}
                                    </label>
                                @endforeach
                            </div>
                        </div>
                    @empty
                        <p>No questions found</p>
                    @endforelse
                </div>
            </div>
            <div class="col-lg-4 col-md-12 answer-sheet-col">
                <div class="answer-sheet-wrapper" id="omr-question">
                    @for ($i = 1; $i <= count($questions); $i++)
                        <div class="question">
                            <div class="question-number">{{ $i }}</div>
                            <div class="options">
                                <div class="option {{ $i }}-1">A</div>
                                <div class="option {{ $i }}-2">B</div>
                                <div class="option {{ $i }}-3">C</div>
                                <div class="option {{ $i }}-4">D</div>
                                @if (isset($questions[0]) && $questions[0]->academy_type == 13)
                                    <div class="option {{ $i }}-5">E</div>
                                @endif
                            </div>
                        </div>
                    @endfor
                </div>
            </div>
        </div>
        {{--  Modal  --}}
        <div class="offcanvas offcanvas-bottom" tabindex="-1" id="offcanvasExample"
            aria-labelledby="offcanvasExampleLabel">
            <div class="offcanvas-header">
                <h5 class="offcanvas-title" id="offcanvasExampleLabel">OMR Sheet</h5>
                <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
            </div>
            <div class="offcanvas-body">
                <div class="dropdown mt-3 omr">
                    <div class="answer-sheet-wrapper" id="omr-question">
                        @for ($i = 1; $i <= count($questions); $i++)
                            <div class="question">
                                <div class="question-number">{{ $i }}</div>
                                <div class="options">
                                    <div class="option {{ $i }}-1">A</div>
                                    <div class="option {{ $i }}-2">B</div>
                                    <div class="option {{ $i }}-3">C</div>
                                    <div class="option {{ $i }}-4">D</div>
                                    @if (isset($questions[0]) && $questions[0]->academy_type == 13)
                                        <div class="option {{ $i }}-5">E</div>
                                    @endif
                                </div>
                            </div>
                        @endfor
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{--  McQ Js Code Here  --}}
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"
        integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous">
    </script>
    <!-- Add KaTeX CSS in your <head> -->

    <!-- Add KaTeX JS before closing </body> tag -->
    <script src="{{ asset('frontend/assets/katex/katex.min.js') }}"></script>
    <script src="{{ asset('frontend/assets/katex/contrib/auto-render.min.js') }}"></script>

    <script>
        document.addEventListener("DOMContentLoaded", function() {
            renderMathInElement(document.body);
        });
    </script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const checkedOptions = document.querySelectorAll('.mcq-option input:checked');
            // Loop through each checked option and do something with it
            checkedOptions.forEach(input => {
                const keyId = input.getAttribute('data-keyid');
                const option = input.getAttribute('data-option');
                const questionId = Number(input.getAttribute('data-question-id'));
                selectOption(keyId, option, questionId);
            });
        });

        // Countdown timer logic
        let timeLeft = {{ $remaining_time['time'] ?? 0 }} * 60; // Time in seconds (minutes)
        // let timeLeft = 1 * 60; // Time in seconds (minutes)

        const totalQuestions = {{ $exam->number_of_question ?? 0 }};
        let selectedCount = 0;

        // Update timer every second
        const timer = setInterval(function() {
            if (timeLeft > 0) {
                timeLeft--;
                updateTimerDisplay();
            } else {
                clearInterval(timer);
                submitExam();
            }
        }, 1000);
        // Display the initial time when the page loads
        updateTimerDisplay();

        function updateTimerDisplay() {
            let minutes = Math.floor(timeLeft / 60);
            let seconds = timeLeft % 60;

            minutes = minutes < 10 ? '0' + minutes : minutes;
            seconds = seconds < 10 ? '0' + seconds : seconds;

            document.getElementById('time-left').textContent = minutes + ":" + seconds;
        }

        // Update question counter (Selected and Remaining)
        function updateQuestionCounter() {
            selectedCount = $('input[type="radio"]:checked').length;
            const remainingCount = totalQuestions - selectedCount;

            $('#selected-count').text(selectedCount);
            $('#remaining-count').text(remainingCount);

            if (selectedCount === totalQuestions) {
                submitExam();
            }
        }

        function selectOption(indexId, optionId, questionId) {
            var $mcqContent = $('#mcq-content-' + indexId);

            $('.' + indexId + '-' + optionId).addClass('mcq-answer-omr-selected');
            // Disable all radio buttons after selection
            $mcqContent.find('input[type="radio"]').prop('disabled', true);
            // Add a class to mark the question as answered
            $mcqContent.addClass('answered');

            if (questionId !== 0) {
                // Save the selected option
                selectedMcqOption(questionId, 'option_' + optionId);
            }
            // Update the counts of answered and unanswered questions
            updateQuestionCounter()
        };

        function submitExam() {
            $.ajax({
                url: "{{ $examSubmitUrl }}",
                type: "POST",
                data: {
                    testId: "{{ $testId }}",
                    _token: "{{ csrf_token() }}"
                },
                success: function(response) {
                    if (response.status === 'success') {
                        // Redirect to the new route
                        window.location.href = response.redirect;
                    } else {
                        // Handle the error message
                        alert(response.message);
                    }
                },
                error: function(error) {
                    console.log(error);
                }
            });
        }

        function selectedMcqOption(question_id, option) {
            $.ajax({
                url: "{{ route('student.mcq-question-select-option') }}",
                type: "POST",
                data: {
                    question_id: question_id,
                    option: option,
                    testId: "{{ $testId }}",
                    _token: "{{ csrf_token() }}"
                },
                success: function(response) {
                    console.log(response);
                },
                error: function(error) {
                    console.log(error);
                }
            });
        }
    </script>
    {{--  McQ Js Code Here  --}}
    <script>
        var href = window.location.href;
        var parts = href.split("/");
        var partsLength = parts.length;
        if (href.includes("student") && href.includes("start-mcq-exam") && partsLength === 6) {
            document.body.classList.add("exam-page");
        }
    </script>
</body>

</html>
