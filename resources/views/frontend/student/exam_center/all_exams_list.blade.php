
@forelse ($exams as $exam)
    <div class="row">
        <div class="col-12">
            <div class="bup-card">
                <div class="{{examColorBadge($filter)}}">{{ ucfirst($filter)}}</div>
               
                <h5 class="bup-card-title">{{$exam->title ?? ''}}</h5>
                <div style="font-size: 15px;font-weight: 900;color: #335B47;">
                    <span><i class="fas fa-chalkboard-teacher"></i> Teacher:  {{$exam->instructor->name ?? ''}}</span>
                </div>
                <div>
                    <p class="bup-card-subtitle">
                        @if($exam->model == 'live') 
                            <i class="fas fa-users"></i>
                        @else
                            <i class="far fa-user"></i>
                        @endif
                            {{ucfirst($exam->model) ?? ''}} 
                    </p>
                </div>
                <div class="bup-card-info">
                    <span><img src="{{ asset('admin/images/examCenter/time.svg') }}" alt="icon"> Time: {{$exam->total_duration ?? 0}} min</span>
                    <span><img src="{{ asset('admin/images/examCenter/message-question.svg') }}" alt="icon"> Question: {{$exam->number_of_question ?? 0}}</span>
                    <span><img src="{{ asset('admin/images/examCenter/tick-circle.svg') }}" alt="icon">  Marks: {{($exam->number_of_question ?? 0 * $exam->marks_per_question ?? 0)}}</span>
                    <span><img src="{{ asset('admin/images/examCenter/calendar-2.svg') }}" alt="icon">  Date: {{$exam->starts_at ?? ''}}</span>
                </div>
                @if($exam->model == 'live')
                    @if ($exam->test_attempted_id && $exam->is_test_finished ==1)
                        <a href="{{route('student.result-sheet',[
                            'examType'=> 'live',
                            'testId'=>$exam->test_attempted_id,
                            ])}}" class="start-exam bup-start-btn" style="background-color: #f39c12; color: white; padding: 5px 10px; font-size: 14px;">
                            <i class="fa fa-check" aria-hidden="true"></i>
                            You Already Attend This Exam
                        </a>
                    @else
                      @if(auth()->check() && auth()->user()->role == 3 )  <a onclick="startExam({{$exam->id}})" class="start-exam bup-start-btn">Start Exam</a>@endif
                    @endif
                @else
                    <a onclick="startSelfExam({{$exam->id}})" class="start-exam bup-start-btn">Start Exam</a>
                @endif
            </div>
        </div>
    </div>                    
@empty
    <div
        style="background-color: white; border-radius: 8px; box-shadow: 0 1px 3px rgba(0,0,0,0.1); padding: 40px 20px; margin-bottom: 20px; text-align: center;">
        <div style="max-width: 300px; margin: 0 auto;">
            <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="#9ca3af" stroke-width="2"
                stroke-linecap="round" stroke-linejoin="round" style="margin: 0 auto 20px;">
                <path d="M8 21h8" />
                <path d="M12 3v18" />
                <path d="M17 8l-1.5-1.5" />
                <path d="M17 16l-1.5 1.5" />
                <path d="M7 8l1.5-1.5" />
                <path d="M7 16l1.5 1.5" />
            </svg>
            <h3 style="font-size: 18px; color: #1f2937; margin: 0 0 10px;">No {{ ucfirst($filter)}} Exams</h3>
            <p style="font-size: 14px; color: #6b7280; margin: 0;">
                There are currently no {{ ucfirst($filter)}} exams. Check back later.
            </p>
        </div>
    </div>
@endforelse
<div class="pagination-wrapper">
    {{ $exams->links() }}
</div>
{{-- <script>
    $(document).on('click', '.pagination a', function(event) {
        event.preventDefault();
        var page = $(this).attr('href').split('page=')[1];
        var filter = '{{ request()->get('filter') }}';
        var currentModel = '{{ request()->get('model') }}';
        var searchQuery = '{{ request()->get('search') }}';
        var selectedDate = '{{ request()->get('date') }}';
        console.log(page, filter, currentModel, searchQuery, selectedDate);
        fetchFilteredExams(page, filter, currentModel, searchQuery, selectedDate);
    });
</script> --}}