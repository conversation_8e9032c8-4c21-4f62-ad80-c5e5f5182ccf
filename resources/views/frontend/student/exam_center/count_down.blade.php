<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Exam Countdown</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins&display=swap');

        * {
            box-sizing: border-box;
        }

        body {
            background: url(https://mir-s3-cdn-cf.behance.net/project_modules/disp/e29ccc95910113.5face8c671539.gif) no-repeat left center,
                url(https://mir-s3-cdn-cf.behance.net/project_modules/disp/0c0c5895910113.5fb0c66b4bc1d.gif) no-repeat right center;
            background-color: #000;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
            font-family: 'Poppins';
            color: #fff;
            font-size: 10px;
        }

        h1 {
            text-align: center;
            margin-top: 14rem;
            font-size: 3rem;
        }

        .cd_timer {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            text-align: center;
        }

        .cd_container .time {
            font-weight: bold;
            padding: 1rem 2rem;
            font-size: 3rem;
            border: 4px solid #01dfcc;
            border-radius: 1rem;
            margin: 0 1rem;
        }

        .cd_container span {
            font-size: 1rem;
            font-weight: bold;
            color: #ccc;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>The Exam Will be Started After</h1>
        <div class="cd_timer">
            <div class="cd_container">
                <div class="time day">0</div>
                <span class="time_text">Days</span>
            </div>
            <div class="cd_container">
                <div class="time hour">0</div>
                <span class="time_text">Hours</span>
            </div>
            <div class="cd_container">
                <div class="time minute">0</div>
                <span class="time_text">Minutes</span>
            </div>
            <div class="cd_container">
                <div class="time second">0</div>
                <span class="time_text">Seconds</span>
            </div>
        </div>
        <div class="cd_container" style="margin-top: 5%; text-align: center;">
            <a style="text-decoration: none;" href="{{route('student.examCenter')}}"><span class="time_text">Go Exams
                    List</span></a>
        </div>
    </div>

    <script>
        // Use the `timeLeftInSeconds` from PHP to initialize the countdown
        let timeLeftInSeconds = <?= $timeLeftInSeconds ?>; // Change to 'let' so it can be updated

        const daysEl = document.querySelector('.day');
        const hourEl = document.querySelector('.hour');
        const minuteEl = document.querySelector('.minute');
        const secondEl = document.querySelector('.second');

        function timeCountDown() {
            // Get the remaining time in seconds
            let totalSeconds = timeLeftInSeconds;

            // Calculate days, hours, minutes, and seconds
            const days = Math.floor(totalSeconds / 3600 / 24);
            const hours = Math.floor(totalSeconds / 3600) % 24;
            const minutes = Math.floor(totalSeconds / 60) % 60;
            const seconds = totalSeconds % 60;

            // Update the countdown elements
            daysEl.innerHTML = formatTime(days);
            hourEl.innerHTML = formatTime(hours);
            minuteEl.innerHTML = formatTime(minutes);
            secondEl.innerHTML = formatTime(seconds);

            // Decrease timeLeftInSeconds every second, and stop countdown when time reaches zero
            if (timeLeftInSeconds > 0) {
                timeLeftInSeconds--;
            } else {
                clearInterval(countdownInterval);
            }
        }

        function formatTime(time) {
            return time < 10 ? `0${time}` : time;
        }

        // Initialize countdown
        timeCountDown();
        const countdownInterval = setInterval(timeCountDown, 1000);
    </script>
</body>

</html>