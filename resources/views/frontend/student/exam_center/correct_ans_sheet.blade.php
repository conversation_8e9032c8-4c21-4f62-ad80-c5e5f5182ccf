<link href="{{ asset('frontend/assets/css/group-exam/custom.css') }}" rel="stylesheet">
<link href="{{ asset('frontend/assets/css/group-exam/responsive.css') }}" rel="stylesheet">

<body
    class="correct-ans-body"
    style="margin: 0; padding: 20px; background-color: #f8f9fa; font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;">
    <div class="container my-3">
        <!-- Exam <PERSON>er -->
        <div
            style="background: white; border-radius: 12px; padding: 15px; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); margin-bottom: 10px;">
            <!-- Header Top -->
            <div
                style="display: flex; justify-content: space-between; align-items: flex-start;  flex-wrap: wrap; gap: 16px;">
                <div>
                    <h5>{!! $exam->title ?? '' !!}</h5>
                    {{-- <p style="color: #6b7280; font-size: 0.875rem; margin: 0;">Mathematics Department - Fall 2024</p> --}}
                </div>
                {{-- <div style="background: #f3f4f6; padding: 12px 16px; border-radius: 9999px; display: flex; align-items: center; gap: 8px; font-size: 0.875rem; color: #111827;">
                    ⏰ {{$exam->total_duration ?? 0}} Minutes
                </div> --}}
            </div>

            <!-- Info Grid -->
            {{-- <div style="display: flex; flex-wrap: wrap; gap: 32px; margin-bottom: 32px;">
                <div style="display: flex; align-items: center; gap: 8px; font-size: 0.875rem;">
                    📝 <span style="color: #6b7280;">Question Type:</span>
                    <span style="font-weight: 500; color: #111827;">MCQ</span>
                </div>
                <div style="display: flex; align-items: center; gap: 8px; font-size: 0.875rem;">
                    👤 <span style="color: #6b7280;">Student ID:</span>
                    <span style="font-weight: 500; color: #111827;">STU123456</span>
                </div>
                <div style="display: flex; align-items: center; gap: 8px; font-size: 0.875rem;">
                    🔢 <span style="color: #6b7280;">Total Questions:</span>
                    <span style="font-weight: 500; color: #111827;">{!! $exam->number_of_question ?? '' !!}</span>
                </div>
            </div> --}}
        </div>
        <!-- Question Section -->
        <div class="question-section" style="overflow-y: scroll;max-height: 540px;">
            @forelse ($question_answers as $key => $question_answer)
                @php
                    $keyId = $key + 1;
                    $question = $question_answer->question;
                    $mcq = $question->mcqQuestion;
                    $selected_option = $question_answer->selected_option;
                    $options = [
                        'option_1' => ['label' => 'A', 'text' => $mcq->option_1 ?? ''],
                        'option_2' => ['label' => 'B', 'text' => $mcq->option_2 ?? ''],
                        'option_3' => ['label' => 'C', 'text' => $mcq->option_3 ?? ''],
                        'option_4' => ['label' => 'D', 'text' => $mcq->option_4 ?? ''],
                    ];
                    if ($question->academy_type == 13) {
                        $options['option_5'] = ['label' => 'E', 'text' => $question->option_5 ?? ''];
                    }
                @endphp
                <div class="question-single"
                    style="background: white; border-radius: 12px; padding: 32px; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); margin-bottom: 10px;">
                    <div class="question-text"
                        style="display: flex; align-items: flex-start; gap: 6px; font-size: 1rem; font-weight: 500; color: #111827;">
                        <span class="" style="min-width: 24px; text-align: center; font-weight: bold; color: #333;">{{$keyId}}.</span>
                        <div class="math-equation katex-display" style="flex: 1;">
                            {!! $question->question_text !!}
                        </div>
                    </div>

                    <!-- Options List -->
                    <div class="correct-ans-page-options" style="margin-bottom: 24px;">
                        @foreach ($options as $optionKey => $option)
                        @php
                            $isMetch = $selected_option === $optionKey;
                            $correct_option = $mcq->correct_option == $optionKey;
                            $notAnswered = $selected_option === '';
                            $emoji = '';
                            $color = 'border: 1px solid #e5e7eb;background-color: white;';
                            if ($isMetch) {
                                $color = 'border: 1px solid #fecaca;background-color: #fef2f2;';
                                $emoji = '🥴';
                            }
                            if ($correct_option) {
                                $color = 'border: 1px solid #86efac;background-color: #f0fdf4;';
                            }
                            if ($isMetch && $correct_option) {
                                $color = 'border: 1px solid #86efac;background-color: #f0fdf4;';
                                $emoji = '🥳';
                            }
                            $color = $selected_option === '' && $correct_option ? 'border: 1px solid #e5e7eb;background-color: #d4edda;' : $color;

                    
                        @endphp
                        <label style="display: block; cursor: pointer; position: relative;">
                            <div class="{{ $correct_option ? 'correct-option' : '' }}"
                                style="display: flex; align-items: center; padding: 16px; border-radius: 8px; {{ $color }}">
                        
                                <input {{ $isMetch ? 'checked' : '' }} type="hidden" id="{{ $keyId }}-{{ $optionKey }}"
                                    style="margin-right: 12px;" disabled>
                                <span style="display: flex; align-items:center; gap: 8px;">
                                    <p class="option-label">{{ $option['label'] }}</p> {!! $option['text'] !!}
                                </span>
                        
                                <!-- Emoji -->
                                <p style="position: absolute; top: 4px; right: 8px;">{{$emoji}}</p>
                            </div>
                            <!-- Unanswered info message -->
                           
                        </label>
                        @endforeach
                        @if ($notAnswered)
                        <p
                            style="margin-top: 8px; color: #856404; background-color: #fff3cd; padding: 8px; border: 1px solid #ffeeba; border-radius: 4px;">
                            😬 This question is unanswered.
                        </p>
                        @endif
                    </div>
                    <!-- Answer Text -->
                    <div class="ans-btns">
                        <div class="the-ans" style="font-weight: 500; color: #111827;">Answer:
                            {{ getQuestionOption($mcq->correct_option) }} </div>
                        <!-- Action Buttons -->
                        <div style="display: flex; flex-wrap: wrap; gap: 12px;">
                            <button
                                style="display: inline-flex; align-items: center; gap: 8px; padding: 8px 16px; border: 1px solid #2563eb; border-radius: 6px; font-size: 0.875rem; font-weight: 500; color: #2563eb; background: white; cursor: pointer;"
                                onclick="showingExplanation(`{{ $question->solution ?? '' }}`)">
                                📖 Explanation
                            </button>
                            <button
                                style="display: inline-flex; align-items: center; gap: 8px; padding: 8px 16px; border: 1px solid #dc2626; border-radius: 6px; font-size: 0.875rem; font-weight: 500; color: #dc2626; background: white; cursor: pointer;"
                                onclick="showingReport({{ $question->id }})">
                                ⚠️ Report
                            </button>
                        </div>
                    </div>
                </div>
            @empty
        </div>
        @endforelse
    </div>
</body>
<style>
    ul.nav.result-sheet-tab-parent.nav-pills.mb-3 {
        margin-right: 150px;
    }

    @media (max-width: 768px) {
        ul.nav.result-sheet-tab-parent.nav-pills.mb-3 {
            margin-right: 0px !important;
        }
    }
</style>
