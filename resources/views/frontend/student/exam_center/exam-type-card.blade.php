<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet"
    integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
<link href="{{ asset('frontend/assets/css/group-exam/custom.css') }}" rel="stylesheet">
<link href="{{ asset('frontend/assets/css/group-exam/responsive.css') }}" rel="stylesheet">

<div class="row live-exam-parent-wrapper gy-4">
    <div class="col-md-4 exam-type-tab-web">
        <a href="#" onclick="getExamDetails('previous')">
            <div class="exam-type-card" onclick="handleExamButtonClick(event, this)">
                <h4>Previous Exam</h4>
                <div class="start-exam-btn-parent">
                    <button class="start-exam-btn">Enter Exam <i class="bi bi-arrow-right"></i></button>
                    <div class="arrow-icon-parent">
                        <i class="fa-solid fa-arrow-up"></i>
                    </div>
                </div>
            </div>
        </a>
    </div>
    <div class="col-md-4 exam-type-tab-web">
        @if ($hasLiveExam)
            <div class="{{ examColorBadge('live') }}">Live</div>
        @endif
        <a href="#" onclick="getExamDetails('live')">
            <div class="exam-type-card" onclick="handleExamButtonClick(event, this)">
                <h4>Live Exam</h4>
                <div class="start-exam-btn-parent">
                    <button class="start-exam-btn">Enter Exam <i class="bi bi-arrow-right"></i></button>
                    <div class="arrow-icon-parent">
                        <i class="fa-solid fa-arrow-up"></i>
                    </div>
                </div>
            </div>
        </a>
    </div>
    <div class="col-md-4 exam-type-tab-web">
        <a href="#" onclick="getExamDetails('upcoming')">
            <div class="exam-type-card" onclick="handleExamButtonClick(event, this)">
                <h4>Upcoming Exam</h4>
                <div class="start-exam-btn-parent">
                    <button class="start-exam-btn">Enter Exam <i class="bi bi-arrow-right"></i></button>
                    <div class="arrow-icon-parent">
                        <i class="fa-solid fa-arrow-up"></i>
                    </div>
                </div>
            </div>
        </a>
    </div>
</div>

<script>
    function handleExamButtonClick(event, button) {
        event.preventDefault();
        const startExamButtons = document.querySelectorAll('.exam-type-card');
        startExamButtons.forEach(btn => btn.classList.remove('active-exam'));
        button.classList.add('active-exam');
    }
</script>
