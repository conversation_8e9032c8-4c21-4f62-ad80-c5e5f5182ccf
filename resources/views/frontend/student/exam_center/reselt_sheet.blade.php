@extends('frontend.layouts.app')
@push('style')
    <link href="{{ asset('frontend/assets/css/group-exam/examCenter.css') }}" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <link href="{{ asset('frontend/assets/css/group-exam/responsive.css') }}" rel="stylesheet">
    <link rel="stylesheet" href="{{ asset('frontend/assets/katex/katex.min.css') }}">
    <link rel="stylesheet" href="{{ asset('frontend/assets/katex/fonts/katex_Main-Regular.woff2') }}">
    <style>
        .table-div {
            -ms-overflow-style: none;
            /* IE and Edge */
            scrollbar-width: none;
            /* Firefox */
        }

        .table-div::-webkit-scrollbar {
            display: none;
            /* Chrome, Safari, and Opera */
        }

        .relative {
            position: relative;
            max-width: 1320px;
            margin: 0 auto;
        }

        h1.result-sheet-title {
            position: absolute;
            top: 2px;
            left: 75px;
        }

        /* Mobile responsive CSS */
        @media only screen and (max-width: 600px) {
            .table {
                width: 100%;
                display: block;
            }
        }

        .loader-swal {
            height: 200px !important;
            width: 280px !important;
        }

        .loader-swal .swal2-image {
            max-width: 100% !important;
            margin: -0.75em auto !important;
        }

        @media only screen and (max-width: 600px) {
            ul.nav.result-sheet-tab-parent.nav-pills.mb-3 {
                margin-left: 59px !important;
            }
        }

        .katex-display {
            display: block;
            margin: 0;
            text-align: unset;
        }

        /* Mobile Responsive Styles */
        @media (max-width: 768px) {
            .container {
                width: 100%;
                max-width: 100%;
                /* Ensure container fits within its parent */
                padding: 10px;
                /* Add padding for better spacing */
                box-sizing: border-box;
                /* Include padding and borders in width */
                overflow-x: hidden;
                /* Prevent horizontal scroll */
            }

            /* General KaTeX Styles */
            .katex .mord {
                max-width: 100%;
                /* Prevent stretching */
                display: inline-block;
                /* Ensure proper rendering */
            }

            .katex .mroot {
                display: inline-block;
                /* Proper root display */
            }

            .katex .katex-root {
                width: auto;
                /* Prevent excessive width */
                overflow: hidden;
                /* Hide unwanted parts */
            }

            /* Math Equation Styles */
            .math-equation {
                display: block;
                /* Ensure block-level formatting */
                white-space: normal;
                /* Prevent content stretching */
                text-align: left;
                /* Align multiline equations properly */
                overflow-x: auto;
                /* Allow scrolling for long expressions */
                font-size: 16px;
                /* Default font size */
                line-height: 1.5;
                /* Improve readability */
            }

            /* KaTeX Block Equations */
            .katex-display {
                margin-bottom: 8px !important;
                /* Add space below equations */
                text-align: center !important;
                /* Center-align block equations */
                max-width: 100% !important;
                /* Prevent overflow */
                overflow-x: auto !important;
                /* Handle long equations with scrolling */
            }

            /* KaTeX Container for Responsiveness */
            .katex-container {
                max-width: 100%;
                word-wrap: break-word;
                /* Handle long lines */
                overflow-wrap: break-word;
                /* Break words as needed */
                box-sizing: border-box;
                /* Include padding in dimensions */
            }

            /* Question Text Styles */
            .question-text {
                font-size: 16px;
                /* Default font size */
                line-height: 1.5;
                /* Improve readability */
                word-break: break-word;
                /* Break long words to fit */
                overflow-wrap: break-word;
                /* Support breaking in all cases */
                margin-bottom: 10px;
                /* Add spacing below the text */
                text-align: left;
                /* Align text properly */
            }
        }

        /* Mobile Responsive Styles */
        @media (max-width: 768px) {
            .math-equation {
                font-size: 14px;
                /* Reduce size for smaller screens */
            }

            .katex-display {
                font-size: 14px;
                /* Adjust equation size */
            }

            .question-text {
                font-size: 14px;
                /* Adjust text size for smaller screens */
            }

            .container {
                padding: 8px;
                /* Adjust padding for mobile */
            }
        }

        @media only screen and (max-width: 425px) {
            #result-info-section .my-3 {
                margin-top: 0px !important;
            }
        }

        /* Handle Long Inline Math Equations */
        .katex .katex-html {
            max-width: 100%;
            /* Restrict inline equation width */
            white-space: nowrap;
            /* Prevent wrapping of inline equations */
            overflow-x: auto;
            /* Allow scrolling for long equations */
        }

        /* Improved Spacing Between Text and Equations */
        .katex-container+.question-text {
            margin-top: 8px;
        }

        /* Scrollbar Customization (Optional) */
        .math-equation::-webkit-scrollbar {
            height: 6px;
            /* Adjust scrollbar height */
        }

        .math-equation::-webkit-scrollbar-thumb {
            background: #ddd;
            /* Customize scrollbar thumb color */
            border-radius: 3px;
            /* Rounded scrollbar thumb */
        }

        .math-equation::-webkit-scrollbar-thumb:hover {
            background: #bbb;
            /* Darker color on hover */
        }
        .merit-position-wrapper{
           background: lightslategrey; 
        }
    </style>
@endpush
@section('content')
    @php
        $total_marks = $myexam->number_of_question * $myexam->marks_per_question;
        $isStudentPass = is_student_pass($myexam->id, $last_test->earned_marks, $total_marks);
        $gradesRate = get_percent_data(
            $last_test->exam_id,
            $last_test->id,
            $last_test->earned_marks,
            $last_test->positive_marks,
            $last_test->negative_marks,
            $total_marks,
        );
        $gradingSystemNumber = grading_system_number($last_test->earned_marks, $total_marks);
        $getRanking = $last_test->getRanking();
    @endphp
    <div class="parent-result">
        <div class="relative">
            <div class="container">
                <div class="title-flex-parent">
                    <div class="back-button-parent">
                        <a href="{{ route('student.examCenter') }}" class="back-btn">
                            <button class="back-button"
                                style="display: flex; justify-content: center; align-items: center; background-color: #335B47; color: white; border-radius: 0.375rem; transition: background-color 0.2s;">
                                <i class="fa-solid fa-arrow-left"></i>
                            </button>
                        </a>
                    </div>
                    <ul class="nav result-sheet-tab-parent nav-pills" id="pills-tab" role="tablist">
                        <li class="nav-item result-sheet-items" role="presentation">
                            <button
                                class="nav-link result-sheet-button {{ request()->get('active') == null ? 'active' : '' }}"
                                onclick="getExamCorrectAnswer('resultsheet')" id="pills-home-tab" data-bs-toggle="pill"
                                data-bs-target="#pills-home" type="button" role="tab" aria-controls="pills-home"
                                aria-selected="true">Result Sheet</button>
                        </li>
                        <li class="nav-item result-sheet-items" role="presentation">
                            <button
                                class="nav-link result-sheet-button {{ request()->get('active') == 'meritlist' ? 'active' : '' }}"
                                onclick="getExamCorrectAnswer('meritlist')" id="pills-profile-tab" data-bs-toggle="pill"
                                data-bs-target="#pills-profile" type="button" role="tab" aria-controls="pills-profile"
                                aria-selected="false">Merit List</button>
                        </li>
                        <li class="nav-item result-sheet-items" role="presentation">
                            <button
                                class="nav-link result-sheet-button {{ request()->get('active') == 'correctanswer' ? 'active' : '' }}"
                                onclick="getExamCorrectAnswer('correctanswer')" id="pills-contact-tab" data-bs-toggle="pill"
                                data-bs-target="#pills-contact" type="button" role="tab" aria-controls="pills-contact"
                                aria-selected="false">Correct Answer</button>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="tab-content" id="pills-tabContent">
                <div id="result-info-section">
                    <div class="container">
                        <h1 class="result-sheet-title">Result Analysis</h1>
                        <div class="exam-completed-card">
                            <h2 class="text-center mb-3">Exam Completed</h2>
                            <div class="exam-score-circle-result">
                                <span>{{ $last_test->earned_marks }}</span>
                                <div class="line-exam"></div>
                                <span>{{ $total_marks }}</span>
                                <span>Marks</span>
                            </div>
                            <div class="party-popper party-popper-left">
                                <img src="{{ asset('admin/images/examCenter/confetti2.svg') }}" alt="icon">
                            </div>
                            <div class="party-popper party-popper-right">
                                <img src="{{ asset('admin/images/examCenter/confetti.svg') }}" alt="icon">
                            </div>
                        </div>
                        <div class="row marked-row">
                            <div class="col-6 col-md-3 mb-3">
                                <div class="stats-card total-questions text-center">
                                    <h6>Total Ques. : {{ $myexam->number_of_question }}</h6>
                                </div>
                            </div>
                            <div class="col-6 col-md-3 mb-3 text-center">
                                <div class="stats-card not-answered">
                                    <h6>Not Ans. : {!! $last_test->number_of_not_answered !!}</h6>
                                </div>
                            </div>
                            <div class="col-6 col-md-3 mb-3 text-center">
                                <div class="stats-card correct-answer">
                                    <h6>Correct Ans. : {!! $last_test->number_of_correct_answers !!}</h6>
                                </div>
                            </div>
                            <div class="col-6 col-md-3 mb-3 text-center">
                                <div class="stats-card wrong-answer">
                                    <h6>Wrong Ans. : {!! $last_test->number_of_wrong_answers !!}</h6>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="container exam-result-container">
                        <div class="exam-result-wrapper">
                            <h2 class="exam-title-bengali">{!! $myexam->title !!}</h2>

                            <div class="row g-3">
                                <div class="col-md-6 col-6">
                                    <div class="result-card">
                                        <div class="result-label">Positive Mark</div>
                                        <p class="result-value">{!! $last_test->positive_marks !!}</p>
                                    </div>
                                </div>

                                <div class="col-md-6 col-6">
                                    <div class="result-card">
                                        <div class="result-label">Negative Mark</div>
                                        <p class="result-value">{!! $last_test->negative_marks !!}</p>
                                    </div>
                                </div>

                                <div class="col-md-6 col-6">
                                    <div class="result-card">
                                        <div class="result-label">Total Time</div>
                                        <p class="result-value">{!! $myexam->total_duration !!}</p>
                                    </div>
                                </div>

                                <div class="col-md-6 col-6">
                                    <div class="result-card">
                                        <div class="result-label">Taken Time</div>
                                        <p class="result-value">{!! gmdate('H:i:s', $last_test->time_taken_in_second) !!}</p>
                                    </div>
                                </div>

                                <div class="col-md-6 col-6">
                                    <div class="result-card">
                                        <div class="result-label">Model</div>
                                        <p class="result-value">{{ ucfirst($myexam->model) ?? '' }}</p>
                                    </div>
                                </div>

                                <div class="col-md-6 col-6">
                                    <div class="result-card">
                                        <div class="result-label">Merit Position</div>
                                        <p class="result-value merit">{!! $getRanking['rank'] !!}
                                            <span class="merit-fraction">/{!! $getRanking['total'] !!}</span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                            @if (count($subejct_base_marks) > 0)
                                <h2 class="score-title" style="text-align: center;">Subject Base Marks Sheet</h2>
                                <div class="table-div"
                                    style="overflow-x: auto; -ms-overflow-style: none; scrollbar-width: none;">
                                    <table class="table" style="width: 100%; border-collapse: collapse; margin: 20px 0;">
                                        <thead>
                                            <tr style="background-color: #f2f2f2; text-align: left;">
                                                <th style="padding: 12px; border: 1px solid #ddd;">Subject Name</th>
                                                <th style="padding: 12px; border: 1px solid #ddd;">Positive</th>
                                                <th style="padding: 12px; border: 1px solid #ddd;">Negative</th>
                                                <th style="padding: 12px; border: 1px solid #ddd;">Earned</th>
                                                <th style="padding: 12px; border: 1px solid #ddd;">Correct</th>
                                                <th style="padding: 12px; border: 1px solid #ddd;">Wrong</th>
                                                <th style="padding: 12px; border: 1px solid #ddd;">Not</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach ($subejct_base_marks as $single_subject_marks)
                                                @php
                                                    $sub_base_per = subject_base_percentge_marks_update(
                                                        $single_subject_marks,
                                                        $myexam,
                                                    );
                                                @endphp
                                                <tr>
                                                    <td style="padding: 12px; border: 1px solid #ddd;">
                                                        {{ get_subject_name($single_subject_marks->subject_id) }}</td>
                                                    <td style="padding: 12px; border: 1px solid #ddd;">
                                                        {{ $single_subject_marks->positive_marks }} (Percentage:
                                                        {{ $sub_base_per['positive_percentge'] }})</td>
                                                    <td style="padding: 12px; border: 1px solid #ddd;">
                                                        {{ $single_subject_marks->negative_marks }} (Percentage:
                                                        {{ $sub_base_per['negative_marks'] }})</td>
                                                    <td style="padding: 12px; border: 1px solid #ddd;">
                                                        {{ $single_subject_marks->earned_marks }} (Percentage:
                                                        {{ $sub_base_per['earned_marks'] }})</td>
                                                    <td style="padding: 12px; border: 1px solid #ddd;">
                                                        {{ $single_subject_marks->correct_answer }}</td>
                                                    <td style="padding: 12px; border: 1px solid #ddd;">
                                                        {{ $single_subject_marks->wrong_answer }}</td>
                                                    <td style="padding: 12px; border: 1px solid #ddd;">
                                                        {{ $single_subject_marks->not_answer }}</td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @endif
                            <div class="score-report-wrapper">
                                <h2 class="score-title">Score Report</h2>

                                <div class="row g-3">
                                    <div class="col-md-6 col-6">
                                        <div class="score-card">
                                            <div class="score-label">Status</div>
                                            <p class="score-value fail">{{ $isStudentPass['status'] ?? '' }}</p>
                                        </div>
                                    </div>

                                    <div class="col-md-6 col-6">
                                        <div class="score-card">
                                            <div class="score-label">Exam Passing Score</div>
                                            <p class="score-value">{{ $isStudentPass['passingScore'] ?? '' }}</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="grades-section">
                                    <div class="grades-title">Grades rate:</div>
                                    <div class="grades-grid">
                                        <div class="grade-item">
                                            <div class="grade-label">Mark</div>
                                            <p class="grade-value">{{ $gradesRate['earned_marks'] }}</p>
                                        </div>
                                        <div class="grade-item">
                                            <div class="grade-label">Negative Mark</div>
                                            <p class="grade-value">{{ $gradesRate['negative_marks'] }}</p>
                                        </div>
                                        <div class="grade-item">
                                            <div class="grade-label">Positive Mark</div>
                                            <p class="grade-value">{{ $gradesRate['positive_percentge'] }}</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="row g-3">
                                    <div class="col-md-6 col-6">
                                        <div class="score-card">
                                            <div class="score-label">Alpha Grade</div>
                                            <p class="score-value">{{ $gradingSystemNumber['letter_grade'] }}</p>
                                        </div>
                                    </div>

                                    <div class="col-md-6 col-6">
                                        <div class="score-card">
                                            <div class="score-label">GPA Score</div>
                                            <p class="score-value">{{ $gradingSystemNumber['grade_poing'] }}</p>
                                        </div>
                                    </div>

                                    <div class="col-md-6 col-6">
                                        <div class="score-card">
                                            <div class="score-label">Remarks</div>
                                            <p class="score-value fail">{{ $gradingSystemNumber['remarks'] }}</p>
                                        </div>
                                    </div>

                                    <div class="col-md-6 col-6">
                                        <div class="score-card">
                                            <div class="score-label">Highest Marks</div>
                                            <p class="score-value success">
                                                {{ $getLeadersHigest ? $getLeadersHigest->earned_marks : '' }}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('script')
    <!-- Add KaTeX JS before closing </body> tag -->
    <script src="{{ asset('frontend/assets/katex/katex.min.js') }}"></script>
    <script src="{{ asset('frontend/assets/katex/contrib/auto-render.min.js') }}"></script>

    <script>
        document.addEventListener("DOMContentLoaded", function() {
            renderMathInElement(document.body);
        });
    </script>
    <script>
        var loader = @json(Cache::get('loader-gif'));
        $(document).on('click', '.pagination-div a', function(event) {
            event.preventDefault();
            const params = getUrlParams($(this).attr('href'));
            fetchExamMeritlist(params.page, params.active);
        });

        function getUrlParams(url) {
            const urlParams = new URLSearchParams(url.split('?')[1]);
            return {
                page: urlParams.get('page'),
                active: urlParams.get('active')
            };
        }

        $(document).ready(function() {
            var page = "{{ request()->get('page') }}";
            var active = "{{ request()->get('active') }}";
            if (active == 'correctanswer') {
                fetchExamCorrectAnswer(active);
            }
            if (active == 'meritlist') {
                fetchExamMeritlist(page, active);
            }
        });

        function getExamCorrectAnswer(active) {
            if (active == 'resultsheet') {
                let testId = "{{ $last_test->id }}";
                let newUrl = `${window.location.pathname}`;
                window.history.pushState({
                    path: newUrl
                }, '', newUrl);
                location.reload();
            }

            if (active == 'meritlist') {
                fetchExamMeritlist(1, active);
            }

            if (active == 'correctanswer') {
                fetchExamCorrectAnswer(active);
                document.body.classList.add('correctanswer-body');
            }
        }

        function fetchExamCorrectAnswer(active) {
            let testId = "{{ $last_test->id }}";
            let examType = "{{ $myexam->model }}";
            var url = "{{ route('student.correct-ans-sheet', [':examType', ':testId']) }}"
                .replace(':examType', examType)
                .replace(':testId', testId) + '?active=' + active;
            $.ajax({
                url: url,
                type: 'GET',
                beforeSend: function() {
                    sowingLoader();
                },
                success: function(data) {
                    Swal.close();
                    $('#result-info-section').html(data);
                    let newUrl = `${window.location.pathname}?testId=${testId}&active=${active}`;
                    window.history.pushState({
                        path: newUrl
                    }, '', newUrl);
                },
                error: function(xhr) {
                    // $('#loading').hide();
                    console.error('An error occurred:', xhr.responseText);
                }
            });
        }

        function fetchExamMeritlist(page, active) {

            let testId = "{{ $last_test->id }}";
            let examType = "{{ $myexam->model }}";
            var url = "{{ route('student.merit-list', [':examType', ':testId']) }}"
                .replace(':examType', examType)
                .replace(':testId', testId) + '?active=' + active + '&page=' + page;

            $.ajax({
                url: url,
                type: 'GET',
                beforeSend: function() {
                    sowingLoader();
                },
                success: function(data) {
                    Swal.close();
                    $('#result-info-section').html(data);
                    let newUrl = `${window.location.pathname}?active=${active}&page=${page}`;
                    window.history.pushState({
                        path: newUrl
                    }, '', newUrl);
                },
                error: function(xhr) {
                    // $('#loading').hide();
                    console.error('An error occurred:', xhr.responseText);
                }
            });

        }

        function showingExplanation(explanation) {
            let explanationText = explanation ? explanation : 'No explanation found.';
            Swal.fire({
                title: ' 📖 Explanation',
                html: explanationText,
                showCloseButton: true,
                showConfirmButton: false,
            });
        }

        async function showingReport(questionId) {
            const {
                value: text
            } = await Swal.fire({
                title: "Report This Question", // Title of the dialog
                html: `
            <textarea id="report-textarea" placeholder="Submit Your Comments..." aria-label="Submit Your Comments" style="width: 100%; height: 100px; padding: 10px; border: 2px solid #5e3fd7; border-radius: 4px;"></textarea>
        `,
                showCancelButton: true,
                confirmButtonText: 'Report', // Change OK button text to "Report"
                confirmButtonColor: '#28a745', // Set the confirm button color to green
                preConfirm: () => {
                    const textareaValue = document.getElementById('report-textarea').value;
                    if (!textareaValue) {
                        Swal.showValidationMessage('Please enter a comment');
                    }
                    return textareaValue; // Return the value of the textarea
                }
            });

            if (text) {
                $.ajax({
                    url: "{{ route('student.question-report') }}",
                    type: 'POST',
                    data: {
                        question_id: questionId,
                        comment: text,
                        _token: "{{ csrf_token() }}",
                    },
                    success: function(data) {
                        Swal.fire({
                            title: 'Report Submitted',
                            text: 'Your report has been submitted successfully.',
                            icon: 'success',
                            showCloseButton: true,
                            showConfirmButton: false,
                        });
                    },
                    error: function(xhr) {
                        console.error('An error occurred:', xhr.responseText);
                    }
                });
            }
        }

        function sowingLoader() {
            if (loader) {
                Swal.fire({
                    title: "Please wait...",
                    imageUrl: loader,
                    showConfirmButton: false,
                    allowOutsideClick: false,
                    customClass: {
                        popup: 'loader-swal' // Add custom class here
                    }
                });
            }
        }
    </script>

    <script>
        const url = window.location.href;
        if (url.includes('correctanswer')) {
            document.body.classList.add('correctanswer-body');
        }
    </script>
@endpush
