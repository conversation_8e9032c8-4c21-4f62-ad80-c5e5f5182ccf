@push('style')
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet"
    integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
<link href="{{ asset('frontend/assets/css/group-exam/examCenter.css') }}" rel="stylesheet">
<style>
    .grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
        margin-bottom: 15px;
        max-width: 1271px;
    }

    .grid h2 {
        font-weight: 600;
        font-size: 24px;
    }

    .grid p {
        font-weight: 600;
        font-size: 16px !important;
        margin-left: 40px !important;
    }

    .exam-feature-card:hover {
        background-color: #d4e157 !important;
    }

    @media (min-width: 768px) {
        .grid {
            grid-template-columns: repeat(3, 1fr);
        }

        ul.nav.nav-pills.exam-tab-parent {
            display: none;
        }
    }

    @media only screen and (min-width: 300px) and (max-width: 479px) {
        .grid p {
            margin-left: 0px !important;
            text-align: center;
        }

        .grid h2 {
            font-size: 15px !important;
        }

        .grid p {
            font-size: 14px !important;
        }

        .live-exam-parent-wrapper {
            display: none;
        }

        .exam-feature-card {
            padding: 6px !important;
        }

    }

    .exam-feature-card a {
        background-color: #1b5e20;
        /* Default color for Live Exam */
        color: white;
        border: none;
        padding: 10px 20px;
        /* Increased padding for larger buttons */
        border-radius: 4px;
        cursor: pointer;
        font-size: 0.875rem;
        text-decoration: none;
        /* Remove underline */
        transition: all 0.3s ease;
        /* Smooth transition for all changes */
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        /* Add shadow for depth */
    }

    .exam-feature-card a:hover {
        background-color: #2e7d32;
        /* Change background color on hover */
        transform: scale(1.05);
        /* Slightly enlarge the button */
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        /* Deeper shadow on hover */
    }

    .exam-feature-card a:active {
        background-color: #1b3e20;
        /* Darker shade when clicked */
        opacity: 0.9;
        /* Slightly reduce opacity */
        transform: scale(0.95);
        /* Slightly shrink the button */
    }

    /* Specific styles for the second button */
    .exam-feature-card a.self-exam {
        background-color: #0d47a1;
        /* Color for Self Exam */
    }

    .exam-feature-card a.self-exam:hover {
        background-color: #1565c0;
        /* Change background color on hover */
    }

    .exam-feature-card a.self-exam:active {
        background-color: #0a2e6d;
        /* Darker shade when clicked */
        opacity: 0.9;
        /* Slightly reduce opacity */
    }

    .loader-swal {
        height: 200px !important;
        width: 280px !important;
    }

    .loader-swal .swal2-image {
        max-width: 100% !important;
        margin: -0.75em auto !important;
    }

    .gap {
        gap: 12px;
    }

    .back-and-search-wrapper {
        flex-grow: 1;
        margin-top: 8px;
        margin-bottom: 3px;
    }

    .search-form {
        border: 1px solid #ddd;
        border-radius: 10px;
    }

    .search-form input,
    .form-control.date:focus,
    .search-form input:focus,
    .search-form button {
        border: 0;
        outline: none;
        background: transparent;
    }

    .search-form button:hover {
        background-color: #335B47;
        border-color: #335B47;
    }

    .form-control.date {
        width: 45px;
        background-color: transparent;
        border: 0;
        border-radius: 7px;
        padding: 0;
        font-size: 40px;
        color: #ddd;
    }

    .form-control.date::placeholder {
        color: #ddd !important;
    }

    .search-area {
        margin-left: auto;
    }

    .icon-calendar-mobile {
        display: none !important;
    }

    .date-picker {
        position: relative;
    }

    .search-form button {
        background-color: #335B47;
        border-color: #335B47;
        color: #fff;
    }

    .search-form input {
        padding: 0 .75rem;
        height: 40px;
    }

    .search-form button {
        padding-top: 0;
        padding-bottom: 0;
    }

    .search-area {
        margin-right: -7px;
    }
</style>
@endpush
@php
    $id = request()->route()->parameters()['user']->id ?? '';
    $instructorid = request()->get('instructor_id') ? request()->get('instructor_id') : $id;
@endphp
    <div class="container exam-center-container">
        <div class="exam-center-parent">
            <div
                class="exam-center-header d-flex flex-column flex-md-row justify-content-between align-items-center align-items-md-center gap-3">
                <div class="back-and-search-wrapper d-flex justify-content-between align-items-center gap-3"
                    style="display: none !important;" id="filter-section">
                    <a href="{{ Route::currentRouteName() == "student.examCenter" ? route('student.examCenter') : url()->current() }}" class="back-btn" style="display: none !important;"
                        id="back-button">
                        <button
                            style="display: flex; justify-content: center; align-items: center; background-color: #335B47; color: white; border-radius: 0.375rem; transition: background-color 0.2s; padding: 9px 14px;">
                            <i class="fa-solid fa-arrow-left"></i>
                        </button>
                    </a>
              
                    {{-- Search bar --}}
                    <div class="search-area d-flex justify-content-start align-items-center gap-2">
                              <div class="row">
                        <div class="col-md-12" id="batch-select"></div>
                    </div>
                        <form class="d-flex search-form" role="search">
                            <input class="form-control search me-2" type="search" placeholder="Search"
                                aria-label="Search" value="{{request()->search ?? ''}}">
                            <button class="btn btn-outline-success" type="submit" id="exam-search"><i
                                    class="fa-solid fa-magnifying-glass"></i></button>
                        </form>
                        <div class="date-picker">
                            <input type="date" class="form-control date" id="date" value="{{request()->date ?? ''}}">
                            <i class="far fa-calendar icon-calendar-mobile"></i>
                        </div>
                    </div>
                </div>
              
                
                {{-- For mobile --}}
                <ul class="nav nav-pills exam-tab-parent exam-center-page-tab exam-type-tab-mobile" id="pills-tab"
                    role="tablist">
                    <li class="nav-item" role="presentation">
                        <button
                            class="exam-active-btn nav-link {{ request()->get('filter') == 'previous' ? 'active' : '' }}"
                            id="pills-profile-tab" data-bs-toggle="pill" data-bs-target="#pills-profile" type="button"
                            role="tab" aria-controls="pills-profile" aria-selected="false"
                            onclick="getExamDetails('previous')">Previous Exam</button>
                    </li>
                    <li class="nav-item blinking-item" role="presentation">

                        <button
                            class="exam-active-btn nav-link has-blinking-dot {{ request()->get('filter') == 'live' ? 'active' : '' }}"
                            id="pills-home-tab" data-bs-toggle="pill" data-bs-target="#pills-home" type="button"
                            role="tab" aria-controls="pills-home" aria-selected="true"
                            onclick="getExamDetails('live')"><span class="blinking-dot"></span>Live Exam</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button
                            class="exam-active-btn nav-link {{ request()->get('filter') == 'upcoming' ? 'active' : '' }}"
                            id="pills-contact-tab" data-bs-toggle="pill" data-bs-target="#pills-contact" type="button"
                            role="tab" aria-controls="pills-contact" aria-selected="false"
                            onclick="getExamDetails('upcoming')">Upcoming Exam</button>
                    </li>
                </ul>
            </div>

            <div class="tab-content" id="pills-tabContent">
                <div class="container">
                    {{-- For web --}}
                    @include('frontend.student.exam_center.exam-type-card')
                    <div id="exam-data-section">
                        <div class="row">
                            <!-- Self Exam Card -->
                            <div class="col-md-6">
                                <div class="next-self-exam-card">
                                    <div class="row justify-content-between align-items-center">
                                        <div class="col-md-8 col-8">
                                            <h2 class="rapid-exam-title">Self Exam</h2>
                                            <p class="rapid-exam-subtitle">Practice exam</p>
                                            <a href="#" onclick="getExamDetails('live','self')"
                                                class="exam-btn-primary">
                                                Start Practice
                                                <img src="{{ asset('admin/images/examCenter/arrow-right-next.svg') }}"
                                                    alt="Study illustration" class="">
                                            </a>
                                        </div>
                                        <div class="col-md-4 col-4">
                                            <img src="{{ asset('admin/images/examCenter/next-one-exam.svg') }}"
                                                alt="Rapid fire illustration" class="exam-illustration">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Rapid Fire Test Card -->
                            <div class="col-md-6">
                                <div class="next-self-exam-card">
                                    <div class="row justify-content-between align-items-center">
                                        <div class="col-md-8 col-8">
                                            <h2 class="rapid-exam-title">Rapid Fire Test</h2>
                                            <p class="rapid-exam-subtitle">Practice exam</p>
                                            <div class="exam-btns d-flex gap-2">
                                                <a href="#" class="exam-btn-primary">
                                                    Play Rapid Fire
                                                    <img src="{{ asset('admin/images/examCenter/arrow-right-next.svg') }}"
                                                        alt="Study illustration" class="">
                                                </a>
                                                <a href="#" class="exam-btn-secondary">View All</a>
                                            </div>
                                        </div>
                                        <div class="col-md-4 col-4">
                                            <img src="{{ asset('admin/images/examCenter/exam-document-next.svg') }}"
                                                alt="Study illustration" class="exam-illustration">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row justify-content-between align-items-center g-3 exam-feature-card-row">
                            <div class="col-lg-4 col-md-4 col-6">
                                <div class="exam-feature-card"
                                    style="background-color: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); padding: 20px;">
                                    <div class="gap" style="display: flex; align-items: center; margin-bottom: 10px;">
                                        <div class="feature-icon"
                                            style=" width: 40px; height: 40px; border-radius: 50%; display: flex; justify-content: center; align-items: center; ">
                                            <img class="xm-center-icon"
                                                src="{{ asset('frontend/assets/img/exam-center/my-result.png') }}"
                                                alt="xam-center-icon">
                                        </div>
                                        <h2 class="large-title" style="margin: 0; font-size: 1.25rem; color: #333;">My
                                            Result</h2>
                                    </div>
                                    <div class="xm-btns" style="display: flex; gap: 10px;">
                                        <a href="{{ route('student.result-sheet', 'live') }}">Live Exam</a>
                                        <a href="{{ route('student.result-sheet', 'self') }}" class="self-exam">Self
                                            Exam</a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-4 col-6">
                                <div class="exam-feature-card"
                                    style="background-color: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); padding: 20px;">
                                    <div class="gap" style="display: flex; align-items: center; margin-bottom: 10px;">
                                        <div class="feature-icon"
                                            style="width: 40px; height: 40px; border-radius: 50%; display: flex; justify-content: center; align-items: center; ">
                                            <img class="xm-center-icon"
                                                src="{{ asset('frontend/assets/img/exam-center/exam-history.png') }}"
                                                alt="xam-center-icon">
                                        </div>
                                        <h2 class="large-title" style="margin: 0; font-size: 1.25rem; color: #333;">
                                            Exam History</h2>
                                    </div>
                                    <div class="xm-btns" style="display: flex; gap: 10px;">
                                        <a href="{{ route('student.my-attended-exam', 'live') }}">Live Exam</a>
                                        <a href="{{ route('student.my-attended-exam', 'self') }}" class="self-exam">Self
                                            Exam</a>
                                    </div>
                                </div>
                            </div>
                            <a class="col-lg-4 col-md-4 col-6" href="{{ route('student.live-exam-routines') }}">
                                <div class="exam-feature-card no-btn"
                                    style="background-color: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); padding: 20px;">
                                    <div class="exam-feature-title gap"
                                        style="display: flex; align-items: center; margin-bottom: 10px;">
                                        <div class="feature-icon"
                                            style="width: 40px; height: 40px; border-radius: 50%; display: flex; justify-content: center; align-items: center; ">
                                            <img class="xm-center-icon"
                                                src="{{ asset('frontend/assets/img/exam-center/routine.png') }}"
                                                alt="xam-center-icon">
                                        </div>
                                        <h2 class="large-title" style="margin: 0; font-size: 1.25rem; color: #333;">
                                            Routine</h2>
                                    </div>
                                    <p style="margin: 0 0 15px 0; font-size: 0.875rem; color: #2e7d32;">Live Exam</p>
                                </div>
                            </a>
                            <div class="col-lg-4 col-md-4 col-6">
                                <div class="exam-feature-card no-btn"
                                    style="background-color: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); padding: 20px;">
                                    <div class="exam-feature-title gap"
                                        style="display: flex; align-items: center; margin-bottom: 10px;">
                                        <div class="feature-icon"
                                            style="width: 40px; height: 40px; border-radius: 50%; display: flex; justify-content: center; align-items: center; ">
                                            <img class="xm-center-icon"
                                                src="{{ asset('frontend/assets/img/exam-center/exam-faq.png') }}"
                                                alt="xam-center-icon">
                                        </div>
                                        <h2 class="large-title" style="margin: 0; font-size: 1.25rem; color: #333;">
                                            Exam FAQ</h2>
                                    </div>
                                    <p style="margin: 0 0 15px 0; font-size: 0.875rem; color: #2e7d32;">Frequently Ask
                                    </p>
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-4 col-6">
                                <div class="exam-feature-card no-btn"
                                    style="background-color: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); padding: 20px;">
                                    <div class="exam-feature-title gap"
                                        style="display: flex; align-items: center; margin-bottom: 10px;">
                                        <div class="feature-icon"
                                            style="width: 40px; height: 40px; border-radius: 50%; display: flex; justify-content: center; align-items: center; ">
                                            <img class="xm-center-icon"
                                                src="{{ asset('frontend/assets/img/exam-center/leader-board.png') }}"
                                                alt="xam-center-icon">
                                        </div>
                                        <h2 class="large-title" style="margin: 0; font-size: 1.25rem; color: #333;">
                                            Leader Board </h2>
                                    </div>
                                    <p style="margin: 0 0 15px 0; font-size: 0.875rem; color: #2e7d32;">My Position</p>
                                </div>
                            </div>
                            <a class="col-lg-4 col-md-4 col-6" href="{{ route('forum.index') }}">
                                <div class="exam-feature-card no-btn"
                                    style="background-color: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); padding: 20px;">
                                    <div class="exam-feature-title gap"
                                        style="display: flex; align-items: center; margin-bottom: 10px;">
                                        <div class="feature-icon"
                                            style="width: 40px; height: 40px; border-radius: 50%; display: flex; justify-content: center; align-items: center; ">
                                            <img class="xm-center-icon"
                                                src="{{ asset('frontend/assets/img/exam-center/qna.png') }}"
                                                alt="xam-center-icon">
                                        </div>
                                        <h2 class="large-title" style="margin: 0; font-size: 1.25rem; color: #333;">
                                            QnA</h2>
                                    </div>
                                    <p style="margin: 0 0 15px 0; font-size: 0.875rem; color: #2e7d32;">Douts QnA</p>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@push('script')
<script>
        var instructor_id = '{{ $instructorid }}';
        $(document).ready(function() {
            let page = '{{ request()->get('page') }}';
            let filter = '{{ request()->get('filter') }}';
            let model = '{{ request()->get('model') }}';
            let e_id = '{{ request()->get('e_id') }}';
            let batch_id = '{{ request()->get('batch_id') }}';
            if (filter || page) {
                const searchQuery = $('.search.me-2').val().trim();
                const selectedDate = $('#date').val();
                fetchFilteredExams(page, filter, model, searchQuery, selectedDate, e_id,batch_id);
            }
            // Handle date filter change
            $('#date').on('change', function() {
                handleFilters();
            });
            // Prevent form submission and handle search
            $('.search-form').on('submit', function(e) {
                e.preventDefault();
                handleFilters();
            });
        });

         $(document).ready(async function() {
            try {
                const response = await $.ajax({
                    url: '{{ route('common.exam.getBatchList') }}',
                    type: 'GET',
                    dataType: 'json'
                });

                if (response.success) {
                    $('#batch-select').empty().html(response.data);
                } else {
                    console.error('Failed to fetch batch list:', response.message);
                }
            } catch (error) {
                console.error('Error fetching batch list:', error);
            }

            $('#selected-batch').on('change', function() {
                handleFilters();
            });
        });

        $(document).on('click', '.pagination-div a', function(event) {
            event.preventDefault();
            const params = getUrlParams($(this).attr('href'));
            const searchQuery = $('.search.me-2').val().trim();
            const selectedDate = $('#date').val();
            const selectedBatch = $('#selected-batch').val();
            fetchFilteredExams(params.page, params.filter, params.model, searchQuery, selectedDate, params.e_id,selectedBatch);
        });

        // Handle all filters
        function handleFilters() {
            const searchQuery = $('.search.me-2').val().trim();
            const selectedDate = $('#date').val();
            const selectedBatch = $('#selected-batch').val();
            
            // Get current URL parameters
            const urlParams = new URLSearchParams(window.location.search);
            const currentPage = urlParams.get('page') || 1;
            const currentFilter = urlParams.get('filter') || 'live';
            const currentModel = urlParams.get('model') || 'live';

            fetchFilteredExams(currentPage, currentFilter, currentModel, searchQuery, selectedDate,'',selectedBatch);
        }

        // Your existing getUrlParams function remains the same
        function getUrlParams(url) {
            const urlParams = new URLSearchParams(url.split('?')[1]);
            return {
                page: urlParams.get('page'),
                filter: urlParams.get('filter'),
                model: urlParams.get('model'),
            };
        }


        function fetchFilteredExams(page, filter, model, search = '', date = '', e_id = '',batch_id = '') {
            let url = `{{ route('student.all-exams-list', ['page' => '']) }}${page}&filter=${filter}&model=${model}`;

            if (e_id) {
                url += `&e_id=${e_id}`; // Only add e_id if it's not empty
            }

            if (search) {
                url += `&search=${encodeURIComponent(search)}`;
            }
            
            if (date) {
                url += `&date=${date}`;
            }

            if(instructor_id){
                url += `&instructor_id=${instructor_id}`;
            }

            if(batch_id){
                url += `&batch_id=${batch_id}`;
            }

            let loader = @json(Cache::get('loader-gif'));
            
            if (loader) {
                $.ajax({
                    url: url,
                    type: 'GET',
                    beforeSend: function() {
                        Swal.fire({
                            title: "Please wait...",
                            imageUrl: loader,
                            showConfirmButton: false,
                            allowOutsideClick: false,
                            customClass: {
                                popup: 'loader-swal'
                            }
                        });
                    },
                    success: function(data) {
                        Swal.close();
                        $('#exam-data-section').html(data);
                        // Update the URL in the browser
                        let newUrl = `${window.location.pathname}?page=${page}&filter=${filter}&model=${model}`;
                        if (e_id) {
                            newUrl += `&e_id=${e_id}`; // Only add e_id if it's not empty
                        }
                        if (search) {
                         newUrl += `&search=${encodeURIComponent(search)}`;
                        }
                        
                        if (date) {
                         newUrl += `&date=${date}`;
                        }

                        if(batch_id){
                            newUrl += `&batch_id=${batch_id}`;
                        }

                        window.history.pushState({path: newUrl}, '', newUrl);
                        $('#back-button').removeAttr('style');
                        $('#filter-section').removeAttr('style');
                    },
                    error: function(xhr) {
                        Swal.close();
                       const response = JSON.parse(xhr.responseText);
                       if(response.message == 'Unauthenticated.'){
                            Swal.fire({
                                title: 'Hay! You are '+response.message,
                                text: 'Please Login First',
                                icon: 'info'
                            });
                        }else{
                            Swal.fire({
                                title: 'Error!',
                                text: response.message,
                                icon: 'error'
                            });
                        }
                    }
                });
            }
        }

        function getExamDetails(filter, model = 'live') {
            const searchQuery = $('.search.me-2').val().trim();
            const selectedDate = $('#date').val();
            const selectedBatch = $('#selected-batch').val();
            const updateModel = (new URL(location.href)).searchParams.get('model') ?? model;
            
            fetchFilteredExams(1, filter, updateModel, searchQuery, selectedDate,'',selectedBatch);
        }

        function examInstructions() {
            return `
            <div style="text-align: left; padding: 10px; line-height: 1.6; font-size: 1rem;overflow-y: scroll;max-height:420px">
                <ol style="list-style-type: none; padding-left: 20px; color: #333;">
                    <li style="margin-bottom: 8px;">R1-এই পরীক্ষাটি তোমার ব্যাচের সকল পরীক্ষার্থীর জন্য অভিজ্ঞ শিক্ষক মন্ডলী দ্বারা বাছাইকৃত মানসম্মত প্রশ্নপত্র দ্বারা তৈরি করা হয়েছে।</li>
                    <li style="margin-bottom: 8px;">R2-এই পরীক্ষাটিতে তুমি একবারের বেশি অংশগ্রহণ করতে পারবে না।</li>
                    <li style="margin-bottom: 8px;">R3-প্রতিটি প্রশ্নের জন্যে নির্ধারিত মার্ক বন্টন আছে এবং ভুল উত্তরের জন্য নেগেটিভ মার্কিং আছে।</li>
                    <li style="margin-bottom: 8px;">R4-এক্সাম শুরু করে এক্সাম পেইজ থেকে বের হলে,ট্যাব পরিবর্তন করলে কিংবা স্ক্রিন মিনিমাইজ করলে এক্সাম থেকে ডিসকোয়ালিফাই হয়ে যাবে।এক্সামটি আর দিতে পারবে না।</li>
                    <li style="margin-bottom: 8px;">R5-অপশন একবার সিলেক্ট করলে আর পরিবর্তন করতে পারবে না।</li>
                    <li style="margin-bottom: 8px;">R6-Online ORM Check করে তুমি কত গুলো প্রশ্নের উত্তর করেছো এবং করোনি সহজেই দেখতে পারবে।</li>
                    <li style="margin-bottom: 8px;">R7-নির্ধারিত সময়ের পূর্বে পরীক্ষাটি শেষ হলে Submit/ Finis Exam Button এ ক্লিক করতে হবে।</li>
                    <li style="margin-bottom: 8px;">R8-এক্সাম শুরুর সময় থেকে নির্ধারিত সময়ের মধ্যে এক্সাম শেষ করতে হবে অন্যথায় সময় শেষ হলে উত্তর পত্র অটোমেটিক ভাবে সাবমিট হয়ে যাবে।</li>
                    <li style="margin-bottom: 8px;">R9-লাইভ এক্সাম গুলোর অধিকাংশ পরীক্ষার ফলাফল নিদিষ্ট সময়  Ex-১২ ঘন্টা বা ১ দিন পরে ফলাফল প্রকাশিত হবে।ফলাফল প্রকাশের বিষয়টি কোর্স টিউটর এবং ছাত্র-ছাত্রীদের সম্মিলিত মতামতের ভিত্তিতে সিদ্ধান্ত নিতে পারে।তবে মডেল টেস্ট পরীক্ষার ক্ষেত্রে অবশ্যই ফলাফল লাইভ এক্সাম হাওয়ার শেষ হবার পরে প্রকাশিত হবে।ফলাফল প্রকাশিত হলে নোটিফিকেশন,এসএমএস,স্টুডেন্ট গ্রুপ গুলোতে জানিয়ে দেওয়া হবে।</li>
                    <li style="margin-bottom: 8px;color:red;">N.B- এক্সামের জন্যে নির্ধারিত সময়ের পরেও ২৫ সেকেন্ড সময় বেশি দেওয়া হবে সাবমিট করার জন্যে।</li>
                </ol>
            </div>
        `;
        }

        function startExam(examId) {
            Swal.fire({
                title: 'Exam Instructions',
                html: examInstructions(),
                icon: 'info',
                showCancelButton: true,
                confirmButtonText: 'Proceed to Exam',
                cancelButtonText: 'Close',
                allowOutsideClick: false,
                allowEscapeKey: false,
                preConfirm: () => {
                    window.location.href = `{{ route('student.get-exam', '') }}/${examId}`;
                }
            });
        }

        function startSelfExam(examId) {
            Swal.fire({
                title: 'Exam Instructions',
                html: examInstructions(),
                icon: 'info',
                showCancelButton: true,
                confirmButtonText: 'Proceed to Exam',
                cancelButtonText: 'Close',
                allowOutsideClick: false,
                allowEscapeKey: false,
                preConfirm: () => {
                    window.location.href = `{{ route('student.get-self-exam', '') }}/${examId}`;
                }
            });
        }
</script>
@endpush