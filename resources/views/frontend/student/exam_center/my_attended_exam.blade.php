@extends('frontend.layouts.app')
@push('style')
<link href="{{ asset('frontend/assets/css/group-exam/examCenter.css') }}" rel="stylesheet">
@endpush
@section('content')
<!-- mY Attended Exam Start Here -->
<div class="exam-dashboard">
        <h1 class="exam-title">My Attended Exam</h1>
        @foreach ($tests as $test)
            @php
                $exam=$test->exam;
                $getRanking = $test->getRanking();
            @endphp
            <!-- Single Exam Card -->
            <div class="attended-exam-card">
                <div class="wrapper-attend-next-exam">
                    <div class="exam-header">
                        <div class="exam-score-circle">
                            <div class="exam-score">{{$test->earned_marks}}</div>
                            <div class="line-dive"></div>
                            <div class="exam-total"> {{ ($exam && $exam->number_of_question ? $exam->number_of_question * $exam->marks_per_question : 0) }}<br> Marks</div>
                        </div>
                        <div>
                            <div class="exam-name">{!!$exam->title ?? '' !!}</div>
                            <div class="exam-position">Merit Position:
                                <strong>{!! $getRanking['rank'] !!}/</strong>{{$getRanking['total']}}
                            </div>
                        </div>
                    </div>
                    <a href="{{route('student.result-sheet', ['examType' => $examType, 'testId' => $test->id])}}" class="exam-details-btn"> <img src="{{ asset('admin/images/examCenter/eye.svg') }}" alt="icon"> View Details</a>    
                </div>
                <div class="exam-stats">
                    <div class="exam-stat-box exam-total-box">
                    <div class="exam-stat-label">Total Question</div>
                        <div class="exam-stat-value">{{$exam->number_of_question ?? 0}}</div>
                    </div>
                    <div class="exam-stat-box exam-not-answered-box">
                    <div class="exam-stat-label">Not Answered</div>
                        <div class="exam-stat-value">{{$test->number_of_not_answered ?? 0}}</div>
                    </div>
                    <div class="exam-stat-box exam-correct-box">
                    <div class="exam-stat-label">Correct Answer</div>
                        <div class="exam-stat-value">{{$test->number_of_correct_answers ?? 0}}</div>
                    </div>
                    <div class="exam-stat-box exam-wrong-box">
                    <div class="exam-stat-label">Wrong Answer</div>
                        <div class="exam-stat-value">{{$test->number_of_wrong_answers}}</div>
                    </div>
                </div>
            </div>
        @endforeach
    </div>
<!-- mY Attended Exam End Here -->
@endsection
@push('script')
@endpush