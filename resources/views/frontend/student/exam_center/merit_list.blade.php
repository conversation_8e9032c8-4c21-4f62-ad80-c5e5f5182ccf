@php
    $request = request();
    if ($request->page != null) {
        $page = $request->page;
    } else {
        $page = 1;
    }
    $page_value = $page * 20 - 20;
@endphp
<div class="container my-3">
    <div class="user-list-header" style="display: flex; ">
        <span class="header-name">Name</span>
        <span class="header-marks">Marks</span>
        <span class="header-position">Position</span>
    </div>
    @forelse($meritLists as $meritList)
        <div class="user-list-item {{$meritList->user_id == auth()->user()->id ? 'merit-position-wrapper' : ''}}">
            {{-- <div class="user-avatar">
                <div class="user-default-avatar">
                    {{ $meritList->user->name[0] ?? '' }}
                </div>
            </div> --}}
            <p class="user-name">{{ $meritList->user->name ?? '' }}</p>
            <span class="user-score">{{ $meritList->earned_marks ?? '' }}</span>
            <span class="user-rank">{{ $loop->iteration + $page_value }} th</span>
        </div>
    @empty
        <div class="no-users-found">
            <p>No users found in the merit list.</p>
        </div>
    @endforelse
    <div class="pagination-wrapper">
        {{ $meritLists->links() }}
    </div>
</div>
