@extends('frontend.layouts.app')
@push('style')
<link href="{{ asset('frontend/assets/css/group-exam/examCenter.css') }}" rel="stylesheet">
@endpush
@section('content')

<div class="container merit-list-parent">
        <div class="merit-flex-parent">
            <h1 class="merit-exam-header-title">My Purchased Courses</h1>
            <ul class="nav marit-tab-parent nav-pills mb-3" id="pills-tab" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link merit-exam-active active" id="pills-home-tab" data-bs-toggle="pill" data-bs-target="#pills-home" type="button" role="tab" aria-controls="pills-home" aria-selected="true">Enrolled Course</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link merit-exam-active" id="pills-profile-tab" data-bs-toggle="pill" data-bs-target="#pills-profile" type="button" role="tab" aria-controls="pills-profile" aria-selected="false">Packages</button>
                </li>
            </ul>
        </div>
        <div class="tab-content" id="pills-tabContent">
            <div class="tab-pane fade show active" id="pills-home" role="tabpanel" aria-labelledby="pills-home-tab">
                <div class="deatails-paretn">
                    <div class=" py-4">
                        <div class="details-exam-card">
                            <div class="preview-section">
                                <!-- Method 1: HTML5 Video -->
                                <div class="video-container" id="videoContainer">
                                    <img src="{{ asset('admin/images/examCenter/overlay-image.png') }}" alt="Video Thumbnail" class="video-thumbnail" id="videoThumbnail""> 
                                    <div class="video-overlay" id="videoOverlay">
                                        <div class="play-button"></div>
                                    </div>
                                    <video class="video-player" id="videoPlayer" controls>
                                        <source src="YOUR_VIDEO_URL.mp4" type="video/mp4">
                                        Your browser does not support the video tag.
                                    </video>
                                </div>

                            </div>
                            
                            <div class="exam-details">
                                <div class="d-flex justify-content-between align-items-start w-100 bup-merit-exam-parent">
                                    <div>
                                        <h2 class="exam-title-merit">BUP Exam batch</h2>
                                        <p class="exam-subtitle">FBS Math-10</p>
                                    </div>
                                    <div class="text-end pricing-paretn">
                                        <div class="price-tag">৳1000.00/</div>
                                        <span class="duration">8 months</span>
                                    </div>
                                </div>

                                <div class="stats-container">
                                    <div class="row g-2">
                                        <div class="col stat-item">
                                            Total Class: 22
                                        </div>
                                        <div class="col stat-item">
                                            Total Exam: 22
                                        </div>
                                        <div class="col stat-item">
                                            Solve Class: 20
                                        </div>
                                        <div class="col stat-item">
                                            Solve Sheet: 25
                                        </div>
                                        <div class="col stat-item">
                                            Joined Student: 100
                                        </div>
                                    </div>
                                </div>

                                <div class="action-buttons">
                                    <button class="enroll-btn">Enroll Now</button>
                                    <button class="facebook-btn">Join Our Facebook Group</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="tab-pane fade" id="pills-profile" role="tabpanel" aria-labelledby="pills-profile-tab">
                <div class="deatails-paretn">
                    <div class=" py-4">
                        <div class="details-exam-card">
                            <div class="preview-section">
                                <!-- Method 1: HTML5 Video -->
                                <div class="video-container" id="videoContainer">
                                    <img src="{{ asset('admin/images/examCenter/overlay-image.png') }}" alt="Video Thumbnail" class="video-thumbnail" id="videoThumbnail""> 
                                    <div class="video-overlay" id="videoOverlay">
                                        <div class="play-button"></div>
                                    </div>
                                    <video class="video-player" id="videoPlayer" controls>
                                        <source src="YOUR_VIDEO_URL.mp4" type="video/mp4">
                                        Your browser does not support the video tag.
                                    </video>
                                </div>

                            </div>
                            
                            <div class="exam-details">
                                <div class="d-flex justify-content-between align-items-start w-100 bup-merit-exam-parent">
                                    <div>
                                        <h2 class="exam-title-merit">BUP Exam batch</h2>
                                        <p class="exam-subtitle">FBS Math-10</p>
                                    </div>
                                    <div class="text-end pricing-paretn">
                                        <div class="price-tag">৳1000.00/</div>
                                        <span class="duration">8 months</span>
                                    </div>
                                </div>

                                <div class="stats-container">
                                    <div class="row g-2">
                                        <div class="col stat-item">
                                            Total Class: 22
                                        </div>
                                        <div class="col stat-item">
                                            Total Exam: 22
                                        </div>
                                        <div class="col stat-item">
                                            Solve Class: 20
                                        </div>
                                        <div class="col stat-item">
                                            Solve Sheet: 25
                                        </div>
                                        <div class="col stat-item">
                                            Joined Student: 100
                                        </div>
                                    </div>
                                </div>

                                <div class="action-buttons">
                                    <button class="enroll-btn">Enroll Now</button>
                                    <button class="facebook-btn">Join Our Facebook Group</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <script>
        // Method 1: HTML5 Video Player Script
        const videoContainer = document.getElementById('videoContainer');
        const videoThumbnail = document.getElementById('videoThumbnail');
        const videoOverlay = document.getElementById('videoOverlay');
        const videoPlayer = document.getElementById('videoPlayer');

        videoContainer.addEventListener('click', () => {
            videoThumbnail.style.display = 'none';
            videoOverlay.style.display = 'none';
            videoPlayer.style.display = 'block';
            videoPlayer.play();
        });

    </script>

@endsection
@push('script')
@endpush