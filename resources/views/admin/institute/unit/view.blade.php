@extends('layouts.admin')
@section('content')
    <!-- Page content area start -->
    <div class="page-content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="breadcrumb__content">
                        <div class="breadcrumb__content__left">
                            <div class="breadcrumb__title">
                                <h2>{{ __('Manage Units') }}</h2>
                            </div>
                        </div>
                        <div class="breadcrumb__content__right">
                            <nav aria-label="breadcrumb">
                                <ul class="breadcrumb">
                                    <li class="breadcrumb-item"><a href="{{route('admin.dashboard')}}">{{__('Dashboard')}}</a></li>
                                    <li class="breadcrumb-item active" aria-current="page">{{ __('Manage Units') }}</li>
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div class="customers__area bg-style mb-30">
                        <div class="item-title d-flex justify-content-between">
                            <h2>{{ __('Manage Units') }}</h2>
                            <button class="btn btn-success btn-sm" type="button" data-bs-toggle="modal" data-bs-target="#add-todo-modal">
                                <i class="fa fa-plus"></i> {{ __('Create Unit') }}
                            </button>
                        </div>
                        <div class="customers__table">
                            <table id="customers-table" class="row-border data-table-filter table-style">
                                <thead>
                                <tr>
                                    <th>SL</th>
                                    <th>Title</th>
                                    <th>Action</th>
                                </tr>
                                </thead>
                                <tbody>
                                @foreach($units as $key => $unit)
                                    <tr class="removable-item">
                                        <td> {{ ++$key}}</td>
                                        <td>{{ $unit->title}}</td>
                                        <td>
                                            <div class="action__buttons">
                                                <button class="edit-button btn btn-outline-primary" data-id="{{ $unit->id }}">Edit</button>
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                                </tbody>
                            </table>
                            <div class="mt-3">
                                {{$units->links()}}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <!-- Add Modal section start -->
<div class="modal fade" id="add-todo-modal" aria-hidden="true" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">

            <div class="modal-header border-0">
                <h5>{{ __('Create Unit') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>

            <form action="{{ route('admin.unit.store') }}" method="post">
                @csrf
                <div class="modal-body">
                    <div class="input__group mb-25">
                        <label for="name">{{ __('Name') }}</label>
                        <input type="hidden" name="institute_id" value="{{$id}}">
                        <input type="text" name="title" id="title" placeholder="{{ __('Type name') }}" value="" required>
                        @if ($errors->has('title'))
                            <span class="text-danger"><i class="fas fa-exclamation-triangle"></i> {{ $errors->first('title') }}</span>
                        @endif
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-purple">{{ __('Save') }}</button>
                </div>
            </form>
        </div>
    </div>
</div>  

    <!-- resources/views/institutes/modal.blade.php -->

<div class="modal fade" id="instituteModal" tabindex="-1" role="dialog" aria-labelledby="instituteModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="instituteModalLabel">Unit Details</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="instituteDetails">
                    <!-- Institute details will be displayed here -->
                    <form action="{{ route('admin.unit.update') }}" method="post">
                        @csrf
                        <input type="hidden" name="unit_id" id="unit_id">
                        <div class="modal-body">
                            <div class="input__group mb-25">
                                <label for="name">{{ __('Name') }}</label>
                                <input type="text" name="title" id="title_edit" placeholder="{{ __('Type name') }}" value="" required>
                                @if ($errors->has('title'))
                                    <span class="text-danger"><i class="fas fa-exclamation-triangle"></i> {{ $errors->first('title') }}</span>
                                @endif
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="submit" class="btn btn-purple">{{ __('Save') }}</button>
                        </div>
                    </form>

                </div>
            </div>
        </div>
    </div>
</div>


    </div>
    
    <!-- Page content area end -->
@endsection

@push('style')
    <link rel="stylesheet" href="{{asset('admin/css/jquery.dataTables.min.css')}}">
@endpush

@push('script')
    <script src="{{asset('admin/js/jquery.dataTables.min.js')}}"></script>
    <script src="{{asset('admin/js/custom/data-table-page.js')}}"></script>
    <SCript>
        $('.edit-button').click(function () {
            var unitId = $(this).data('id');
            $.ajax({
                url: '/admin/units/unit-edit/' + unitId,
                method: 'GET',
                success: function (data) {
                    // $('#instituteDetails').html(
                        $('#title_edit').val(data.title);
                        $('#unit_id').val(data.id);
                    // );
                    $('#instituteModal').modal('show');
                },
                error: function () {
                    console.log('Error fetching unit data.');
                }
            });
        });
    </SCript>
@endpush
