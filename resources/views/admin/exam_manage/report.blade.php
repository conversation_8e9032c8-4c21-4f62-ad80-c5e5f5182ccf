@extends('layouts.instructor')

@section('content')

<div class="instructor-profile-right-part">
    <div class="instructor-quiz-list-page instructor-all-student-page">

        <div class="instructor-my-courses-title d-flex justify-content-between align-items-center">
            <h6>{{__('Exam Merit List')}}</h6>
        </div>

        <div class="row">
            @if(count($reports) > 0)
            <div class="col-12">
                <div class="table-responsive table-responsive-xl">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>SL</th>
                                <th>Name</th>
                                <th>Earned Marks</th>
                                <th>Time</th>
                            </tr>
                            </thead>
                            <tbody>
                            @foreach($reports as $key => $single)
                                <tr class="removable-item">
                                    <td> {{ ++$key}}</td>
                                    <td> {{ $single->user->name ?? "N/A"}}</td>
                                    <td> {{ $single->earned_marks }} </td>
                                    <td> {{ $single->time_taken_in_second }} </td>
                                </tr>
                            @endforeach
                            </tbody>
                        </table>
                </div>
            </div>
            <div class="mt-3">
                {{$reports->links()}}
            </div>
            @else
                <!-- If there is no data Show Empty Design Start -->
                <div class="empty-data">
                    <img src="{{ asset('frontend/assets/img/empty-data-img.png') }}" alt="img" class="img-fluid">
                    <h5 class="my-3">{{__('Exam Merit List')}}</h5>
                </div>
                <!-- If there is no data Show Empty Design End -->
            @endif
        </div>


    </div>
</div>


@endsection

@push('style')
<link rel="stylesheet" href="{{asset('frontend/assets/css/custom/img-view.css')}}">
@endpush

@push('script')
<script src="{{asset('frontend/assets/js/custom/img-view.js')}}"></script>
@endpush

@push('script')
    <script src="{{asset('admin/js/jquery.dataTables.min.js')}}"></script>
    <script src="{{asset('admin/js/custom/data-table-page.js')}}"></script>
@endpush