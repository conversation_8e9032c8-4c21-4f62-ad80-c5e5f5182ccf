@extends('layouts.instructor')

@section('content')
<style>
    .dropdown.all-student-filter-dropdown{
        display: flex;
        align-items: end;
        gap: 10px;
        width: 50%;
    }
    .searching-parent{
        display: flex;
        align-items: center;
        gap: 25px;
        width: 100%;
        position: relative;
    }

    button.theme-btn.default-hover-btn.theme-button1 {
        position: absolute;
        right: 8px;
        top: 6px;
    }

    .searching-flex-parent{
        width: 100%;
    }

    .all-exam-wrapper{
        width: 60% !important;
    }


    .custom-dropdown-container {
        display: inline-block;
        position: relative;
    }

.custom-select-dropdown {
    width: 130px;
    padding: 13px 10px;
    font-size: 16px;
    font-weight: 500;
    color: #040453;
    background-color: white; 
    border: none;
    border-radius: 8px; 
    appearance: none; 
    outline: none;
    cursor: pointer;
    text-align: center;
    /* box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); */
    /* transition: background-color 0.3s ease, box-shadow 0.3s ease;*/
    border: 1px solid rgba(0, 0, 0, 0.07); 
}

.custom-select-dropdown option{
    width: 400px;
}

.wrapper-filter-icon i {
    position: absolute;
    top: 18px;
    right: 10px;
}
    .common-add-btn {
        border: 1px solid rgba(0, 0, 0, 0.07);
        padding: 11px 20px;
        border-radius: 8px;
        white-space: nowrap;
    }

    .exam-custon-btn-wrapper{
        padding: 10px 13px !important;
    }


</style>

<div class="instructor-profile-right-part">
    <div class="instructor-quiz-list-page instructor-all-student-page">

        <div class="instructor-my-courses-title d-flex justify-content-between align-items-center">
            <h6>{{__('All Exam')}}</h6>
            <div class="dropdown all-student-filter-dropdown all-exam-wrapper">
                 <form action="{{ route('common.exam.index') }}" name="get" class="searching-parent">
                    <div class=" m-0 p-0 searching-flex-parent">
                        <input type="search" name="search_name" class="form-control" placeholder="{{ __('Searching...') }}" value="{{ app('request')->search_name }}">
                    </div>
                    <button type="submit" class="theme-btn default-hover-btn theme-button1"><i class="fas fa-search"></i></button>
                </form>
                <div class="custom-dropdown-container">
                    <select class="custom-select-dropdown" id="filter-exam-manage">
                        <option >Filter</option>
                        <option value="all">All</option>
                        <option value="live">Live</option>
                        <option value="previous">Previous</option>
                        <option value="upcoming">Upcoming</option>
                    </select>
                    <div class="wrapper-filter-icon">
                        <i class="fas fa-filter "></i>
                    </div>
                </div>
                <a href="{{route('common.exam.add')}}" class="common-add-btn exam-custon-btn-wrapper" type="button">{{__('Add Exam')}}</a>
            </div>

        </div>

        <div class="row">
            @if(count($exams) > 0)
            <div class="col-12">
                <div class="table-responsive table-responsive-xl">
                    <table class="table">
                        <thead>
                            <tr>
                                {{-- <th>SL</th> --}}
                                <th>Title</th>
                                <th>Type</th>
                                <th>Model</th>
                                <th>Approved</th>
                                <th>Completed</th>
                                <th>Start Date</th>
                                <th>End Date</th>
                                <th>Status</th>
                                <th>Action</th>
                            </tr>
                            </thead>
                            <tbody>
                            @foreach($exams as $key => $exam)
                                <tr class="removable-item">
                                    {{-- <td> {{ ++$key}}</td> --}}
                                    <td> {{ $exam->title}}</td>
                                    <td> {{ question_types($exam->type_id) }} </td>
                                    <td> {{ strtoupper( $exam->model) }} </td>
                                    <td style="cursor:pointer;" onclick="approvedAndUnapproved({{$exam->id}},{{$exam->is_approved}})" 
                                        title="Click to approve or unapprove the exam">
                                       {{ get_is_approved($exam->is_approved) }} 
                                   </td>
                                    <td> {{ is_completed($exam->is_completed) }} </td>
                                    <td> {{ $exam->starts_at }} </td>
                                    <td> {{ $exam->exam_time_end }} </td>
                                    <td> {{ $exam->status == 1 ? "Active" : "Deactivated" }}</td>
                                    <td style="padding: 1rem; white-space: nowrap; border-bottom: 1px solid #e5e7eb;">
                                        <div style="display: flex; align-items: center; gap: 0.5rem;">

                                            @if($exam->model == 'live')
                                                <a onclick="genarateExamLink({{$exam->id}},'live')" style="display: inline-flex; align-items: center; padding: 0.25rem 0.5rem; font-size: 0.875rem; font-weight: 500; border-radius: 0.375rem; background-color: #e0f2fe; color: #0369a1; border: 1px solid #7dd3fc; cursor: pointer; transition: background-color 0.2s;" onmouseover="this.style.backgroundColor='#bae6fd'" onmouseout="this.style.backgroundColor='#e0f2fe'" aria-label="Setup">
                                                    <img src="{{ asset('admin/images/icons/qus.svg') }}" alt="questions" style="margin-right: 5px;">
                                                    Copy Link
                                                </a>
                                                @else
                                                <a onclick="genarateExamLink({{$exam->id}},'self')" style="display: inline-flex; align-items: center; padding: 0.25rem 0.5rem; font-size: 0.875rem; font-weight: 500; border-radius: 0.375rem; background-color: #e0f2fe; color: #0369a1; border: 1px solid #7dd3fc; cursor: pointer; transition: background-color 0.2s;" onmouseover="this.style.backgroundColor='#bae6fd'" onmouseout="this.style.backgroundColor='#e0f2fe'" aria-label="Setup">
                                                    <img src="{{ asset('admin/images/icons/qus.svg') }}" alt="questions" style="margin-right: 5px;">
                                                    Copy Link
                                                </a>
                                            @endif
                                            <a href="{{route('common.exam.examManageQuestions', [$exam->slug])}}" style="display: inline-flex; align-items: center; padding: 0.25rem 0.5rem; font-size: 0.875rem; font-weight: 500; border-radius: 0.375rem; background-color: #e0f2fe; color: #0369a1; border: 1px solid #7dd3fc; cursor: pointer; transition: background-color 0.2s;" onmouseover="this.style.backgroundColor='#bae6fd'" onmouseout="this.style.backgroundColor='#e0f2fe'" aria-label="Setup">
                                                <img src="{{asset('admin/images/icons/qus.svg')}}" alt="questions" style="margin-right: 5px;">
                                                Question Setup
                                            </a>
                                            <a href="{{route('common.exam.edit',[$exam->id])}}"  style="display: inline-flex; align-items: center; padding: 0.25rem 0.5rem; font-size: 0.875rem; font-weight: 500; border-radius: 0.375rem; background-color: #ffedd5; color: #c2410c; border: 1px solid #fed7aa; cursor: pointer; transition: background-color 0.2s;" onmouseover="this.style.backgroundColor='#fed7aa'" onmouseout="this.style.backgroundColor='#ffedd5'" aria-label="Edit">
                                                <img src="{{asset('admin/images/icons/edit-2.svg')}}" alt="edit" style="margin-right: 5px;">
                                                Edit
                                            </a>
                                            <a class="deleteItem" data-formid="delete_row_form_{{ $exam->id }}"  style="display: inline-flex; align-items: center; padding: 0.25rem 0.5rem; font-size: 0.875rem; font-weight: 500; border-radius: 0.375rem; background-color: #fee2e2; color: #b91c1c; border: 1px solid #fecaca; cursor: pointer; transition: background-color 0.2s;" onmouseover="this.style.backgroundColor='#fecaca'" onmouseout="this.style.backgroundColor='#fee2e2'" aria-label="Delete">
                                                <img src="{{ asset('admin/images/icons/trash-2.svg') }}" alt="trash" style="margin-right: 5px;">
                                                Delete
                                            </a>

                                            <a href="{{route('common.exam.examReport',[$exam->id])}}"  style="display: inline-flex; align-items: center; padding: 0.25rem 0.5rem; font-size: 0.875rem; font-weight: 500; border-radius: 0.375rem; background-color: #ffedd5; color: #c2410c; border: 1px solid #fed7aa; cursor: pointer; transition: background-color 0.2s;" onmouseover="this.style.backgroundColor='#fed7aa'" onmouseout="this.style.backgroundColor='#ffedd5'" aria-label="Edit">
                                                <img src="{{asset('admin/images/icons/edit-2.svg')}}" alt="edit" style="margin-right: 5px;">
                                                Report
                                            </a>

                                        </div>
                                        <form action="{{route('common.exam.delete', [$exam->id])}}" method="post" id="delete_row_form_{{ $exam->id }}">
                                            {{ method_field('DELETE') }}
                                            <input type="hidden" name="_token" value="{{ csrf_token() }}">
                                        </form>
                                    </td>
                                </tr>
                            @endforeach
                            </tbody>
                        </table>
                </div>
            </div>
            <div class="mt-3">
                {{$exams->links()}}
            </div>
            @else
                <!-- If there is no data Show Empty Design Start -->
                <div class="empty-data">
                    <img src="{{ asset('frontend/assets/img/empty-data-img.png') }}" alt="img" class="img-fluid">
                    <h5 class="my-3">{{__('Empty Exam')}}</h5>
                </div>
                <!-- If there is no data Show Empty Design End -->
            @endif
        </div>


    </div>
</div>


@endsection

@push('style')
<link rel="stylesheet" href="{{asset('frontend/assets/css/custom/img-view.css')}}">
@endpush

@push('script')
<script src="{{asset('frontend/assets/js/custom/img-view.js')}}"></script>
@endpush

@push('script')
    <script src="{{asset('admin/js/jquery.dataTables.min.js')}}"></script>
    <script src="{{asset('admin/js/custom/data-table-page.js')}}"></script>


    <script>
        $(document).ready(function() {
            $('[data-toggle="tooltip"]').tooltip();
        });
        function approvedAndUnapproved(id, isApproved) {
            $.ajax({
                url: "{{ route('common.exam.examApprovedAndUnapproved') }}",
                type: "POST",
                data: {
                    id: id,
                    is_approved: isApproved,
                    _token: "{{ csrf_token() }}"
                },
                success: function(response) {
                    if (response.status) {
                        toastr.success(response.message);
                        location.reload();
                    } else {
                        toastr.error(response.message);
                    }
                }
            });
        }
       $('#filter-exam-manage').on('change', function(){
            var filter = $(this).val();
            var url = "{{ route('common.exam.index') }}";
            if(filter){
                url = url + '?filter=' + filter;
            }
            console.log('url',url)
            window.location.href = url;
        });
    </script>
    <script>

        function genarateExamLink(id,model){
            $.ajax({
                url: "{{ route('common.exam.genarateExamLink') }}",
                type: "GET",
                data: {
                    exam_id: id,
                    model: model,
                },
                success: function(response) {
                    if (response.status) {
                        copyToClipboard(response.url);
                    } else {
                        toastr.error(response.message);
                    }
                }
            });
        }

        function copyToClipboard(url) {
            const linkToCopy = url;
            if (navigator.clipboard && navigator.clipboard.writeText) {
                // Use the modern clipboard API if supported
                navigator.clipboard.writeText(linkToCopy)
                    .then(() => {
                        toastr.success('Link copied to clipboard!');
                    })
                    .catch(err => {
                        toastr.error('Failed to copy link to clipboard!');
                    });
            } else {
                // Fallback for older browsers
                const tempTextarea = $('<textarea>'); // Create a temporary textarea element
                $('body').append(tempTextarea); // Append it to the body
                tempTextarea.val(linkToCopy).select(); // Set its value and select the text
                try {
                    document.execCommand('copy'); // Copy the text
                    toastr.success('Link copied to clipboard!');
                } catch (err) {
                    toastr.error('Failed to copy link to clipboard!');
                }
                tempTextarea.remove(); // Remove the temporary element
            }
        }
    </script>
@endpush