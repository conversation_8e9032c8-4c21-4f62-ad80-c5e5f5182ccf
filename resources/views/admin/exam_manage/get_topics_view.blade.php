<div class="form-group mb-2">
    <label>Topics <span class="text-danger"></span></label>
</div>
<div class="form-group form-check">
    <input type="checkbox" class="form-check-input check-all-topics" onclick="toggleAllTopics(this)">
    <label class="form-check-label">Check all</label>
</div>

@foreach ($topics as $chapter_id => $chapterTopics)
    @foreach ($chapterTopics as $topic)
        <div class="form-group form-check">
            <input type="checkbox" class="form-check-input topic-checkbox" name="topics[{{ $chapter_id }}][]" value="{{ $topic->id }}">
            <label class="form-check-label">{{ $topic->title }}</label>
        </div>
    @endforeach
@endforeach
<script>
    function toggleAllTopics(checkbox) {
        const checkboxes = document.querySelectorAll('.topic-checkbox');
        checkboxes.forEach(checkbox => checkbox.checked = !checkbox.checked);
    }
</script>