@extends('layouts.instructor')

@section('breadcrumb')
<div class="page-banner-content text-center">
    <h3 class="page-banner-heading text-white pb-15"> {{ __('Bundles Courses') }} </h3>

    <!-- Breadcrumb Start-->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb justify-content-center">
            <li class="breadcrumb-item font-14"><a href="{{ route('instructor.dashboard') }}">{{ __('Dashboard') }}</a>
            </li>
            <li class="breadcrumb-item font-14 active" aria-current="page">{{ __('Exam Title : ') }} {{$exam->title}}</li>
        </ol>
    </nav>
</div>
@endsection

@section('content')

<style>
    .add-more-button{
        height: 40px;
        width: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 36px;
    }

</style>

<div class="instructor-profile-right-part">
    <div class="instructor-quiz-list-page instructor-all-student-page">

        <div class="instructor-my-courses-title d-flex justify-content-between align-items-center">
            <h6>{{$exam->title}}</h6>
        </div>

        <div class="row">
            
            <form action="{{route('common.exam.examConfigurationStore')}}" method="post" class="form-horizontal">
                @csrf

                <div class="row mb-3">
                    <div class="col-md-12">
                        <div class="input__group text-black">
                            <label>{{ __('Exam Configuration Title') }} <span class="text-danger">*</span></label>
                            <input type="text" name="title" value="{{$exam->title}}" placeholder="{{ __('Type Exam Configuration') }}" class="form-control" required>
                            @if ($errors->has('title'))
                                <span class="text-danger"><i class="fas fa-exclamation-triangle"></i> {{ $errors->first('title') }}</span>
                            @endif
                        </div>
                    </div>
                </div>

                <input type="hidden" value="{{$exam->id}}" name="exam_management_id">

                <div id="subject_chapter_groups">
                    <div class="row mb-3 subject-group">
                        <div class="col-md-3">
                            <div class="input__group text-black">
                                <label>{{ __('Subject') }} <span class="text-danger">*</span> </label>
                                <select name="subject_id[]" data-subject_id="0" class="form-control subject-dropdown multiple-basic-single" required>
                                    <option>Select Subject</option>
                                    @foreach($subjects as $subject)
                                        <option value="{{ $subject->id }}">{{ $subject->title }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                
                        <div class="col-md-3">
                            <div class="input__group text-black">
                                <label>{{ __('Mix Difficulty') }} <span class="text-danger">*</span> </label>
                                <select name="min_difficulty[]" class="form-control" required>
                                    <option value="10">10%</option>
                                    <option value="20">20%</option>
                                    <option value="30">30%</option>
                                    <option value="40">40%</option>
                                    <option selected value="50">50%</option>
                                    <option value="60">60%</option>
                                    <option value="70">70%</option>
                                    <option value="80">80%</option>
                                    <option value="90">90%</option>
                                    <option value="100">100%</option>
                                </select>
                            </div>
                        </div>
                
                        <div class="col-md-3">
                            <div class="input__group text-black">
                                <label>{{ __('Max Difficulty') }} <span class="text-danger">*</span> </label>
                                <select name="max_difficulty[]" class="form-control" required>
                                    <option value="10">10%</option>
                                    <option value="20">20%</option>
                                    <option value="30">30%</option>
                                    <option value="40">40%</option>
                                    <option value="50">50%</option>
                                    <option value="60">60%</option>
                                    <option value="70">70%</option>
                                    <option value="80">80%</option>
                                    <option value="90">90%</option>
                                    <option selected value="100">100%</option>
                                </select>
                            </div>
                        </div>
                
                        <div class="col-md-3">
                            <div class="input__group text-black">
                                <button type="button" class="btn btn-info btn-sm add-more-button">+ Add More</button>
                            </div>
                        </div>
                
                        <div class="col-md-12">
                            <div id="chapters-0"></div> <!-- This will hold the chapters for each subject -->
                        </div>
                    </div>
                </div>

                <div>
                    <button type="submit" class="theme-btn theme-button1 default-hover-btn">{{__('Continue')}}</button>
                </div>

            </form>



        </div>


    </div>
</div>


@endsection



@push('style')

@endpush

@push('script')
    <script src="{{ asset('admin/js/custom/coupon-create.js') }}"></script>
    <script>

$(document).ready(function() {
    // Counter for subject groups
    var subjectCounter = 1;

    // Function to generate the add chapters HTML based on subject
    function generateAddChaptersHtml(subject_id, group_id) {
        $.ajax({
            url: '/common/common-exam-Managment/get-chapters/' + subject_id,
            method: 'GET',
            success: function(response) {
                var chapters = response.chapters;
                var chapter_html = "";

                // Loop through chapters and generate checkboxes
                for (var chapter of chapters) {
                    chapter_html += `
                    <div class="form-group form-check">
                        <input type="checkbox" class="form-check-input chapter-checkbox" name="chapters[` + subject_id + `][]" value="` + chapter.id + `">
                        <label class="form-check-label">` + chapter.title + `</label>
                    </div>
                    `;
                }

                // Append to the relevant group
                var html = `
                <div class="form-group mb-2">
                    <label>Chapters <span class="text-danger">*</span></label>
                </div>
                <div class="form-group form-check">
                    <input onchange="add_check_all(this)" type="checkbox" class="form-check-input">
                    <label class="form-check-label"> Check all</label>
                </div>
                ` + chapter_html;

                $("#chapters-" + group_id).html(html);
            },
            error: function(error) {
                console.error("Error fetching chapters:", error);
            }
        });
    }

    // Event listener for subject dropdown change
    $(document).on('change', '.subject-dropdown', function() {
        var subject_id = $(this).val();
        var group_id = $(this).data('subject_id');
        if (subject_id) {
            generateAddChaptersHtml(subject_id, group_id);
        }
    });

    // Event listener for Add More button
    $(document).on('click', '.add-more-button', function() {
        var newGroup = `
        <div class="row mb-3 subject-group" id="group-` + subjectCounter + `">
            <div class="col-md-3">
                <div class="input__group text-black">
                    <label>{{ __('Subject') }} <span class="text-danger">*</span> </label>
                    <select name="subject_id[]" data-subject_id="` + subjectCounter + `" class="form-control subject-dropdown multiple-basic-single" required>
                        <option>Select Subject</option>
                        @foreach($subjects as $subject)
                            <option value="{{ $subject->id }}">{{ $subject->title }}</option>
                        @endforeach
                    </select>
                </div>
            </div>

            <div class="col-md-3">
                <div class="input__group text-black">
                    <label>{{ __('Mix Difficulty') }} <span class="text-danger">*</span> </label>
                    <select name="min_difficulty[]" class="form-control" required>
                        <option value="10">10%</option>
                        <option value="20">20%</option>
                        <option value="30">30%</option>
                        <option value="40">40%</option>
                        <option selected value="50">50%</option>
                        <option value="60">60%</option>
                        <option value="70">70%</option>
                        <option value="80">80%</option>
                        <option value="90">90%</option>
                        <option value="100">100%</option>
                    </select>
                </div>
            </div>

            <div class="col-md-3">
                <div class="input__group text-black">
                    <label>{{ __('Max Difficulty') }} <span class="text-danger">*</span> </label>
                    <select name="max_difficulty[]" class="form-control" required>
                        <option value="10">10%</option>
                        <option value="20">20%</option>
                        <option value="30">30%</option>
                        <option value="40">40%</option>
                        <option value="50">50%</option>
                        <option value="60">60%</option>
                        <option value="70">70%</option>
                        <option value="80">80%</option>
                        <option value="90">90%</option>
                        <option selected value="100">100%</option>
                    </select>
                </div>
            </div>

            <div class="col-md-3">
                <div class="input__group text-black">
                    <button type="button" class="btn btn-info btn-sm add-more-button">+ Add More</button>
                    <button type="button" class="btn btn-danger btn-sm remove-group-button">Remove</button>
                </div>
            </div>

            <div class="col-md-12">
                <div id="chapters-` + subjectCounter + `"></div>
            </div>
        </div>`;

        // Append the new group
        $("#subject_chapter_groups").append(newGroup);
        subjectCounter++;
    });

    // Event listener for Remove button
    $(document).on('click', '.remove-group-button', function() {
        $(this).closest('.subject-group').remove(); // Removes the entire subject group div
    });
});

function add_check_all(source) {
    var checkboxes = document.querySelectorAll('.chapter-checkbox');
    for (var i = 0; i < checkboxes.length; i++) {
        checkboxes[i].checked = source.checked;
    }
}





    </script>
@endpush

