@extends('layouts.instructor')

@section('breadcrumb')
<div class="page-banner-content text-center">
    <h3 class="page-banner-heading text-white pb-15"> {{ __('Bundles Courses') }} </h3>

    <!-- Breadcrumb Start-->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb justify-content-center">
            <li class="breadcrumb-item font-14"><a href="{{ route('instructor.dashboard') }}">{{ __('Dashboard') }}</a>
            </li>
            <li class="breadcrumb-item font-14 active" aria-current="page">{{ __('Update Exam') }}</li>
        </ol>
    </nav>
</div>
@endsection

@section('content')

<div class="instructor-profile-right-part">
    <div class="instructor-quiz-list-page instructor-all-student-page">

        <div class="instructor-my-courses-title d-flex justify-content-between align-items-center">
            <h6>{{__('Update Exam')}}</h6>
        </div>

        <div class="row">

            <form action="{{route('common.exam.update',[$exam->id])}}" method="post" class="form-horizontal">
                @csrf

                <div class="row mb-3">
                    <div class="col-md-12">
                        <div class="input__group text-black">
                            <label>{{ __('Exam Name') }} <span class="text-danger">*</span></label>
                            <input type="text" name="title" value="{{ old('title', $exam->title) }}"
                                placeholder="{{ __('Type Exam Name') }}" class="form-control" required>
                            @if ($errors->has('title'))
                            <span class="text-danger"><i class="fas fa-exclamation-triangle"></i> {{
                                $errors->first('title') }}</span>
                            @endif
                        </div>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="input__group text-black">
                            <label>{{ __('Exam Fees') }} <span class="text-danger">*</span></label>
                            <input type="number" min="0" name="fee" value="{{old('fee', $exam->fee)}}"
                                placeholder="{{ __('Enter Exam Fee') }}" class="form-control" required>
                            @if ($errors->has('fee'))
                            <span class="text-danger"><i class="fas fa-exclamation-triangle"></i> {{
                                $errors->first('fee') }}</span>
                            @endif
                        </div>
                    </div>

                   <div class="col-md-6">
                    <div class="input__group text-black">
                        <label for="type_id">{{ __('Question Types') }}</label>
                        <select name="type_id" id="type_id" class="form-control multiple-basic-single">
                            @foreach ($question_types as $question_type)
                            <option value="{{ $question_type->id }}" {{ old('type_id', $exam->type_id) == $question_type->id ?
                                'selected' : '' }}>
                                {{ $question_type->title }}
                            </option>
                            @endforeach
                        </select>
                    </div>
                </div>

                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="input__group text-black">
                            <label>{{ __('Number of Questions') }} <span class="text-danger">*</span></label>
                            <input type="number" min="5" name="number_of_question" value="{{old('number_of_question', $exam->number_of_question)}}"
                                placeholder="{{ __('Number of Questions') }}" class="form-control" required>
                            @if ($errors->has('number_of_question'))
                            <span class="text-danger"><i class="fas fa-exclamation-triangle"></i> {{
                                $errors->first('number_of_question') }}</span>
                            @endif
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="input__group text-black">
                            <label>{{ __('Exam Duration (Minutes)') }} <span class="text-danger">*</span></label>
                            <input type="number" min="5" name="total_duration" value="{{old('total_duration', $exam->total_duration)}}"
                                placeholder="{{ __('Exam Duration (Minutes)') }}" class="form-control" required>
                            @if ($errors->has('total_duration'))
                            <span class="text-danger"><i class="fas fa-exclamation-triangle"></i> {{
                                $errors->first('total_duration') }}</span>
                            @endif
                        </div>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="input__group text-black">
                            <label>{{ __('Marks per Question') }} <span class="text-danger">*</span></label>
                            <input type="number" name="marks_per_question" value="{{old('marks_per_question', $exam->marks_per_question)}}"
                                placeholder="{{ __('Marks per Question') }}" class="form-control" required>
                            @if ($errors->has('marks_per_question'))
                            <span class="text-danger"><i class="fas fa-exclamation-triangle"></i> {{
                                $errors->first('marks_per_question') }}</span>
                            @endif
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="input__group text-black">
                            <label>{{ __('Negative marks per Question') }} <span class="text-danger">*</span></label>
                            <input type="number" name="negative_marks_per_question"
                                value="{{old('negative_marks_per_question', $exam->negative_marks_per_question)}}"
                                placeholder="{{ __('Negative marks per Question') }}" class="form-control" required
                                step="any">
                            @if ($errors->has('negative_marks_per_question'))
                            <span class="text-danger"><i class="fas fa-exclamation-triangle"></i> {{
                                $errors->first('negative_marks_per_question') }}</span>
                            @endif
                        </div>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="input__group text-black">
                            <label for="instructor_ids">{{ __('Model') }} <span class="text-danger">*</span></label>
                            <select name="model" class="form-control" id="model" required>
                                <option value="live" {{ old('model', $exam->model) == 'live' ? 'selected' : '' }}>Live</option>
                                <option value="self" {{ old('model', $exam->model) == 'self' ? 'selected' : '' }}>Self</option>
                            </select>
                        </div>
                    </div>

                    <div class="col-md-6" id="starts_at">
                        <div class="input__group text-black">
                            <label>{{ __('Exam Starts At') }} <span class="text-danger">*</span></label>
                            <input type="datetime-local" name="starts_at"
                                value="{{ old('starts_at', date('Y-m-d\TH:i', strtotime($exam->starts_at))) }}"
                                placeholder="{{ __('Exam Starts At') }}" class="form-control" required>
                            @if ($errors->has('starts_at'))
                            <span class="text-danger"><i class="fas fa-exclamation-triangle"></i> {{
                                $errors->first('starts_at') }}</span>
                            @endif
                        </div>
                    </div>

                </div>

                <div class="row mb-3">

                    <div class="col-md-6">
                        <div class="input__group text-black">
                            <label for="instructor_ids">{{ __('Exam Type') }}<span class="text-danger">*</span></label>
                            <select name="exam_type_id" id="exam_type_id" class="form-control multiple-basic-single"
                                required>
                                <option value="">Select Exam Type</option>
                                @foreach($exam_types as $exam_type)
                                <option value="{{ $exam_type->id }}" {{ old('exam_type_id', $exam->exam_type_id) == $exam_type->id ? 'selected' : '' }}>
                                    {{ $exam_type->title }}
                                </option>
                                @endforeach
                            </select>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="input__group text-black">
                            <label for="instructor_ids">{{ __('Result Type') }}<span
                                    class="text-danger">*</span></label>
                            <select name="result_type" class="form-control" id="result_type" required>
                                <option value="">Select Result Type</option>
                                <option value="auto" {{ old('result_type', $exam->result_type) == 'auto' ? 'selected' : '' }}>Auto</option>
                                <option value="manual" {{ old('result_type', $exam->result_type) == 'manual' ? 'selected' : '' }}>Manual</option>
                            </select>
                        </div>
                    </div>

                </div>

                <div class="row mb-3">

                    <div class="col-md-6">
                        <div class="input__group text-black">
                            <label for="instructor_ids">{{ __('Academy Types') }}<span
                                    class="text-danger">*</span></label>
                            <select name="academy_type_ids[]" id="academy_type_ids" disabled
                                class="multiple-basic-single form-control" required multiple>
                                <option value="">Select Academy Types</option>
                                @foreach($acadymic_types as $acadymic_type)
                                <option value="{{ $acadymic_type->id }}" {{ in_array($acadymic_type->id, $exam_academy_types) ? 'selected' : '' }}>
                                    {{ $acadymic_type->title }}
                                </option>
                                @endforeach
                            </select>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="input__group text-black">
                            <label for="instructor_ids">{{ __('Instant Result Publish') }}<span
                                    class="text-danger">*</span></label>
                            <select name="is_result_published" class="form-control" id="is_result_published" required>
                                <option value="1" {{ old('is_result_published', $exam->is_result_published) == '1' ? 'selected' : '' }}>Yes</option>
                                <option value="0" {{ old('is_result_published', $exam->is_result_published) == '0' ? 'selected' : '' }}>No</option>
                            </select>
                        </div>
                    </div>

                </div>



                <div class="row mb-3">

                    <div class="col-md-6">
                        <div class="input__group text-black">
                            <label for="instructor_ids">{{ __('Is Expire Date') }}<span
                                    class="text-danger">*</span></label>
                            <select name="is_expire_date" id="is_expire_date" class="multiple-basic-single form-control"
                                required>
                                <option value="0" {{ old('is_expire_date', $exam->is_expire_date) == '0' ? 'selected' : '' }}>No</option>
                                <option value="1" {{ old('is_expire_date', $exam->is_expire_date) == '1' ? 'selected' : '' }}>Yes</option>
                            </select>
                        </div>
                    </div>

                    <div class="col-md-6">
                    <div class="input__group text-black">
                        <label for="batches">{{ __('Batches') }}<span class="text-danger">*</span></label>
                        <select name="batches[]" id="batches" class="multiple-basic-single form-control" required multiple>
                            <option value="">Select Batches</option>
                            @foreach($batches as $batch)
                                <option value="{{ $batch->id }}" 
                                        @if($exam->batches->contains($batch->id)) selected @endif>
                                    {{ $batch->title }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                </div>
   

                    <div class="col-md-6" id="end_time_local" style="display: none;">
                        <div class="input__group text-black">
                            <label>{{ __('Exam Time End') }} <span class="text-danger">*</span></label>
                            <input type="datetime-local" name="exam_time_end" value="{{old('exam_time_end', $exam->exam_time_end)}}"
                                placeholder="{{ __('Exam Time End') }}" class="form-control">
                            @if ($errors->has('exam_time_end'))
                            <span class="text-danger"><i class="fas fa-exclamation-triangle"></i> {{
                                $errors->first('exam_time_end') }}</span>
                            @endif
                        </div>
                    </div>

                </div>

                <h5>Result Grading Process</h5> <br>

                <div class="row mb-3">

                    <div class="col-md-6">
                        <div class="input__group text-black">
                            <label for="instructor_ids">{{ __('Result Grading') }}<span
                                    class="text-danger">*</span></label>
                            <select name="base" id="base" class="multiple-basic-single form-control" required>
                                <option value="1" {{ old('base', $result->type) == '1' ? 'selected' : '' }}>Marks Base</option>
                                <option value="2" {{ old('base', $result->type) == '2' ? 'selected' : '' }}>Percentage Base</option>
                            </select>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="input__group text-black">
                            <label>{{ __('Result Marks') }} <span class="text-danger">*</span></label>
                            <input type="number" min="5" name="marks_data" value="{{old('marks_data', $result->type ==1 ? $result->cut_marks : $result->pass_percentage)}}"
                                placeholder="{{ __('Marks Data') }}" class="form-control" required>
                            @if ($errors->has('marks_data'))
                            <span class="text-danger"><i class="fas fa-exclamation-triangle"></i> {{
                                $errors->first('marks_data') }}</span>
                            @endif
                        </div>
                    </div>

                </div>


                <div class="row mb-3">
                    <div class="col-md-12">
                        <div class="input__group text-black">
                            <label>{{ __('Status') }} <span class="text-danger">*</span></label>
                            <select name="status" id="status" class="form-control">
                                <option value="">--{{ __('Select Option') }}--</option>
                                <option value="1" {{ old('status', $exam->status) == '1' ? 'selected' : '' }}>{{ __('Active') }}</option>
                                <option value="0" {{ old('status', $exam->status) == '0' ? 'selected' : '' }}>{{ __('Deactivated') }}</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div>
                    <button type="submit" class="theme-btn theme-button1 default-hover-btn">{{__('Update')}}</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
@push('style')
<link rel="stylesheet" href="{{asset('common/css/select2.css')}}">
@endpush
@push('script')
<script src="{{ asset('admin/js/custom/coupon-create.js') }}"></script>
<script src="{{asset('common/js/select2.min.js')}}"></script>
<script>
    $(document).ready(function() {
        $("#academy_type_ids").select2({
            placeholder: "{{ __('Select Academy Types') }}"
        });
        $("#batches").select2({
            placeholder: "{{ __('Select Batches') }}"
        });
        $("#is_expire_date").on("change", function() {
            if ($(this).val() == "1") {
            $("#end_time_local").show();
            } else {
            $("#end_time_local").hide();
            }
        });
    });
</script>
@endpush