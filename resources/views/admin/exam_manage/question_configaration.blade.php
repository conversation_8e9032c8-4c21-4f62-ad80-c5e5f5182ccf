@extends('layouts.instructor')
@section('breadcrumb')
<div class="page-banner-content text-center">
    <h3 class="page-banner-heading text-white pb-15"> {{ __('Bundles Courses') }} </h3>

    <!-- Breadcrumb Start-->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb justify-content-center">
            <li class="breadcrumb-item font-14"><a href="{{ route('instructor.dashboard') }}">{{ __('Dashboard') }}</a>
            </li>
            <li class="breadcrumb-item font-14 active" aria-current="page">{{ __('Exam Title : ') }} {{$exam->title}}</li>
        </ol>
    </nav>
</div>
@endsection
<style>
/* Base layout */
/* Container for the quiz */
.quiz-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  max-width: 1200px;
  margin: 0 auto;
  padding: 10px;
}

/* Question block styling */
.question-block {
  padding: 15px;
  border: 1px solid #ccc;
  border-radius: 8px;
  background-color: #f9f9f9;
}

/* Subject and chapter in a row */
.subject-chapter-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.subject-title {
  font-size: 18px;
  font-weight: bold;
}

.chapter-title {
  font-size: 16px;
  font-style: italic;
  color: #555;
}

.topic-title {
  font-size: 16px;
  font-style: italic;
  color: #555;
}

/* Options styled for MCQ-like layout */
.options ul {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.options ul li {
  width: calc(50% - 10px); /* Two options per row */
  background-color: #f0f0f0;
  border-radius: 5px;
  list-style: none;
  display: flex;
  gap: 2px;
}

/* Correct Answer and Explanation */
.qright-ans, .qlistname {
  margin-top: 15px;
  font-size: 16px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .subject-chapter-row {
    flex-direction: column;
    align-items: flex-start;
  }

  .options ul li {
    width: 100%; /* Options go full-width on smaller screens */
  }
}

@media (max-width: 480px) {
  .question-block {
    padding: 10px;
  }

  .subject-title, .chapter-title {
    font-size: 16px;
  }
}
</style>
@section('content')
<div class="instructor-profile-right-part">
    <div class="instructor-quiz-list-page instructor-all-student-page">
        <div style="display: flex; align-items: center; justify-content: space-between; padding: 10px; border-radius: 5px; width: 100%;font-family: Arial, sans-serif;">
          <span style="font-weight: bold;">{{$exam->title}}</span>
          <span>Max Add Limit: <strong id="max-limit">{{$exam->number_of_question}}</strong></span>
          <span>Selected Question: <strong id="selected-count">0</strong></span>
      </div>
        <div class="row" >
            <form action="{{ route('common.exam.examManageQuestionsStore') }}" method="post" class="form-horizontal">
                @csrf
                <div class="upload-course-item-block radius-8" id="examManageQuestionsDiv" style="overflow-y: auto;max-height: 600px;">
                    @foreach ($allQuestions as $key => $question)
                        <div class="quiz-container">
                            <!-- Question Block 1 -->
                            <div class="question-block">
                                <div class="d-flex align-items-center justify-content-between">
                                    <h4>Question {{ ++$key }}</h4>
                                    <input type="checkbox" name="selected_questions[]" class="add-question"
                                        value="{{ json_encode(['group_id'=>$exam->id,'question_id' => $question->id, 'subject_id' => $question->subject_id, 'chapter_id' => $question->chapter_id]) }}"
                                        @if(in_array($question->id, $questionConfigurations)) checked @endif
                                     > 
                                </div>
                                <!-- Subject and Chapter Title on the same row -->
                                <div class="subject-chapter-row">
                                    <h4 class="subject-title">Subject: {{$question->subject_name}}</h4>
                                    <h4 class="chapter-title">Chapter Title: {{$question->chapter_name}}</h4>
                                    <h4 class="topic-title">Topic Title: {{$question->topic_name}}</h4>
                                </div>
                                <p>
                                    {!! $question->question_text !!}
                                </p>
                                <div class="options">
                                    <ul>
                                        @if($question->option_1) <li>(i) {!! $question->option_1 !!}</li> @endif
                                        @if($question->option_2) <li>(ii) {!! $question->option_2 !!}</li> @endif
                                        @if($question->option_3) <li>(iii) {!! $question->option_3 !!}</li> @endif
                                        @if($question->option_4) <li>(iv) {!! $question->option_4 !!}</li> @endif
                                        @if($question->question_type_id == 4 && $question->option_5) <li>(v) {!! $question->option_5 !!}</li> @endif
                                    </ul>
                                </div>
                                <!-- Correct option and explanation -->
                                <div class="col-md-12">
                                    <div class="true-false-item-wrap align-items-center">
                                        @if ($question && $question->correct_option)
                                            <div class="qright-ans">
                                                <strong>Ans:</strong> {{ getQuestionOption($question->correct_option) }}
                                            </div><br/>
                                            <div class="qlistname">
                                                <strong>Explanation:</strong> {!! $question->solution !!}
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
                <div class="form-group">
                    <button type="submit" class="theme-btn theme-button1 default-hover-btn">{{__('Save')}}</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
@push('style')

@endpush
@push('script')
{{-- <script>
$(document).ready(function() {
    var page = 1;
    var loading = false;

    $("#examManageQuestionsDiv").scroll(function() {
        var divHeight = $(this).height();
        var scrollHeight = $(this).prop('scrollHeight');
        var scrollTop = $(this).scrollTop();
        console.log('scroll', scrollTop, scrollHeight - divHeight);
        if (scrollTop >= scrollHeight - divHeight - 100) {
            if (!loading) {
                loading = true;
                console.log('loading');
                // loadMoreItems();
            }
        }
    });

    function loadMoreItems() {
      var currentPage = '{{ Request::url() }}';
      var nextPage = currentPage + '?page=' + page;
      $.ajax({
          type: 'GET',
          url: nextPage,
          success: function(data) {
              $('#items-container').append(data.html);
              page++;
              loading = false;
          }
      });
  }
});
</script> --}}
<script>
  $(document).ready(function() {
      const maxLimit = parseInt($('#max-limit').text());
      // Function to update the selected count
      function updateSelectedCount() {
          const selectedCount = $('.add-question:checked').length;
          $('#selected-count').text(selectedCount);

          // Disable checkboxes if the limit is reached
          if (selectedCount >= maxLimit) {
              $('.add-question:not(:checked)').prop('disabled', true); // Disable unchecked checkboxes
              toastr.success('You have reached the maximum limit of questions');
          } else {
              $('.add-question').prop('disabled', false); // Enable all if under limit
          }
      }
       // Initial count update on page load
       updateSelectedCount();
      // Event listener for checkboxes
      $('.add-question').on('change', function() {
          updateSelectedCount();
      });
  });
</script>
@endpush

