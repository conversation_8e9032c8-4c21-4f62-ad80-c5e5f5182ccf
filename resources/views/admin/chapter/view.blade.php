@extends('layouts.instructor')
<style>
    .dropdown.all-student-filter-dropdown{
        display: flex;
        align-items: end;
        gap: 10px;
        width: 50%;
    }
    .searching-parent{
        display: flex;
        align-items: center;
        gap: 25px;
        width: 100%;
        position: relative;
    }

    button.theme-btn.default-hover-btn.theme-button1 {
        position: absolute;
        right: 8px;
        top: 6px;
    }

    .searching-flex-parent{
        width: 100%;
    }
    .common-add-btn {
        border: 1px solid rgba(0, 0, 0, 0.07);
        padding: 11px 20px;
        border-radius: 8px;
        white-space: nowrap;
    }
</style>

@section('breadcrumb')
<div class="page-banner-content text-center">
    <h3 class="page-banner-heading text-white pb-15"> {{ __('Bundles Courses') }} </h3>

    <!-- Breadcrumb Start-->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb justify-content-center">
            <li class="breadcrumb-item font-14"><a href="{{ route('instructor.dashboard') }}">{{ __('Dashboard') }}</a>
            </li>
            <li class="breadcrumb-item font-14 active" aria-current="page">{{ __('Bundles Courses') }}</li>
        </ol>
    </nav>
</div>
@endsection

@section('content')

<div class="instructor-profile-right-part">
    <div class="instructor-quiz-list-page instructor-all-student-page">
        <div class="instructor-my-courses-title d-flex justify-content-between align-items-center">
            <h6>{{__('All Chapter')}}</h6>
            <div class="dropdown all-student-filter-dropdown">
                 <form action="{{ route('instructor.chapter.index') }}" name="get" class="searching-parent">
                    <div class=" m-0 p-0 searching-flex-parent">
                        <input type="search" name="search_name" class="form-control" placeholder="{{ __('Searching...') }}" value="{{ app('request')->search_name }}">
                    </div>
                    <button type="submit" class="theme-btn default-hover-btn theme-button1"><i class="fas fa-search"></i></button>
                </form>
                <a href="{{route('instructor.chapter.add')}}" class="common-add-btn" type="button">{{__('Add Chapter')}}</a>
            </div>
        </div>
        <div class="row">
            @if(count($chapters) > 0)
            <div class="col-12">
                <div class="table-responsive table-responsive-xl">
                    <table id="customers-table" class="table">
                        <thead>
                        <tr>
                            {{-- <th>SL</th> --}}
                            <th>Subject</th>
                            <th>Chapter</th>
                            <th>Status</th>
                            <th>Action</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach($chapters as $key => $chapter)
                            <tr class="removable-item">
                                {{-- <td> {{ ++$key}}</td> --}}
                                <td> {{$chapter->subject_title}}</td>
                                <td>{{ $chapter->title}}</td>
                                <td> {{ $chapter->status == 1 ? "Active" : "Deactivated" }}</td>
                                <td>
                                    <div class="action__buttons">
                                        <a href="{{route('instructor.chapter.edit', [$chapter->id])}}" class="btn-action mr-30" title="Edit Details">
                                            <img src="{{asset('admin/images/icons/edit-2.svg')}}" alt="edit">
                                        </a>

                                        <a href="{{route('instructor.chapterBaseQuestion.update',['id'=>$chapter->id])}}">
                                            <img src="{{asset('admin/images/icons/qus.svg')}}" alt="questions">
                                        </a>

                                        <button class="btn-action ms-2 deleteItem"
                                                        data-formid="delete_row_form_{{ $chapter->id }}">
                                                        <img src="{{ asset('admin/images/icons/trash-2.svg') }}"
                                                            alt="trash">
                                        </button>

                                        <form action="{{route('instructor.chapter.delete', [$chapter->id])}}" method="post" id="delete_row_form_{{ $chapter->id }}">
                                            {{ method_field('DELETE') }}
                                            <input type="hidden" name="_token" value="{{ csrf_token() }}">
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                    <div class="mt-3">
                        {{$chapters->links()}}
                    </div>
                </div>
            </div>

            @else
                <!-- If there is no data Show Empty Design Start -->
                <div class="empty-data">
                    <img src="{{ asset('frontend/assets/img/empty-data-img.png') }}" alt="img" class="img-fluid">
                    <h5 class="my-3">{{__('Empty Chapters')}}</h5>
                </div>
                <!-- If there is no data Show Empty Design End -->
            @endif
        </div>


    </div>
</div>


@endsection

@push('style')
<link rel="stylesheet" href="{{asset('frontend/assets/css/custom/img-view.css')}}">
@endpush

@push('script')
<script src="{{asset('frontend/assets/js/custom/img-view.js')}}"></script>
@endpush

@push('script')
    <script src="{{asset('admin/js/jquery.dataTables.min.js')}}"></script>
    <script src="{{asset('admin/js/custom/data-table-page.js')}}"></script>
@endpush