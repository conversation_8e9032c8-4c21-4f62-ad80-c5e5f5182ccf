@extends('layouts.instructor')

@section('breadcrumb')
<div class="page-banner-content text-center">
    <h3 class="page-banner-heading text-white pb-15"> {{ __('Bundles Courses') }} </h3>

    <!-- Breadcrumb Start-->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb justify-content-center">
            <li class="breadcrumb-item font-14"><a href="{{ route('instructor.dashboard') }}">{{ __('Dashboard') }}</a>
            </li>
            <li class="breadcrumb-item font-14 active" aria-current="page">{{ __('Add Subject') }}</li>
        </ol>
    </nav>
</div>
@endsection



@section('content')

<style>
    .add-more-button{
        height: 40px;
        width: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 6px;
    }
    .history{
        gap:20px; 
        align-items: center; 
        display:flex
    }
    .section_history{
        gap:20px; 
        align-items: center; 
        display:flex
    }
    span.copy-math-equition {
        position: relative;
        background-color: #000;
        color: #fff;
        padding: 6px 20px;
        cursor: pointer;
        border-radius: 3px;
        font-size: 14px;
        line-height: 20px;
        display: inline-block;
    }

    .math-tex {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 3px;
        padding: 2px 4px;
        margin: 0 2px;
        display: inline-block;
    }

    #mathPreview {
        font-size: 16px;
        text-align: center;
    }
    .note-modal .modal-dialog {
        max-width: 960px;
    }
</style>

<div class="instructor-profile-right-part">
    <div class="instructor-quiz-list-page instructor-all-student-page">

        <div class="instructor-my-courses-title d-flex justify-content-between align-items-center">
            <h6>{{__('Add Subject')}}</h6>
        </div>

        <div class="row">
            
            <form action="" method="post" class="form-horizontal" enctype="multipart/form-data">
                <div class="row">
                    <div class="col-md-3">
                        <div class="input__group mb-10">
                            <label class="label-text-title color-heading font-medium font-16 mb-3">{{
                                __('Academy Type')
                                }} <span class="text-danger">*</span></label>
                            <span>{{$question->academy_type_name}}</span>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="input__group mb-25">
                            <label class="label-text-title color-heading font-medium font-16 mb-3">{{
                                __('Subject')
                                }} <span class="text-danger">*</span></label>
                            <span>{{$question->subject_name}}</span>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="input__group mb-25">
                            <label class="label-text-title color-heading font-medium font-16 mb-3">{{
                                __('Chapter
                                ')
                                }} <span class="text-danger">*</span></label>
                            <span>{{$question->chapter_name}}</span>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="input__group mb-25">
                            <label class="label-text-title color-heading font-medium font-16 mb-3">{{__('Topic')}} <span class="text-danger">*</span></label>
                            <span>{{$question->topic_name}}</span>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="input__group mb-25">
                            <label class="label-text-title color-heading font-medium font-16 mb-3">{{
                                __('Question Type')
                                }} <span class="text-danger">*</span></label>
                            <span>{{$question->question_type}}</span>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="input__group mb-25">
                            <label class="label-text-title color-heading font-medium font-16 mb-3">{{ __('
                                Difficulty Level ')
                                }} <span class="text-danger">*</span></label>
                            <span>{{$question->difficulty_level}}</span>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="input__group mb-25">
                            <label class="label-text-title color-heading font-medium font-16 mb-3">{{
                                __('Marks')
                                }} <span class="text-danger">*</span></label>
                            <span> {{$question->marks}}</span>
                        </div>
                    </div>

                    <div class="col-md-12">
                        <div class="input__group mb-25">
                            <label>{{__('Whrite Question')}} <span class="text-danger">*</span></label>
                            <textarea rows="8" name="question_text"
                                class="form-control @error('question_text') is-invalid @enderror"
                                id="WhriteQuestion">{{old('question_text')}} {!! $question->question_text!!}</textarea>
                        </div>
                    </div>
                    @if($question->question_type_id == 1)
                    <div class="col-md-6">
                        <div class="input__group mb-25">
                            <label class="label-text-title color-heading font-medium font-16 mb-3">{{ __('Option
                                1')
                                }} <span class="text-danger">*</span></label>
                            <textarea name="option_1"
                                class="form-control options @error('option_1') is-invalid @enderror"
                                id="option_1">{{old('option_1')}} {{ $question->option_1}}</textarea>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="input__group mb-25">
                            <label class="label-text-title color-heading font-medium font-16 mb-3">{{ __('Option
                                2')
                                }} <span class="text-danger">*</span></label>
                            <textarea name="option_2"
                                class="form-control options @error('option_2') is-invalid @enderror"
                                id="option_2">{{old('option_2')}} {{ $question->option_2}}</textarea>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="input__group mb-25">
                            <label class="label-text-title color-heading font-medium font-16 mb-3">{{ __('Option
                                3')
                                }} <span class="text-danger">*</span></label>
                            <textarea name="option_3"
                                class="form-control options @error('option_3') is-invalid @enderror"
                                id="option_3">{{old('option_3')}} {{ $question->option_3}} </textarea>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="input__group mb-25">
                            <label class="label-text-title color-heading font-medium font-16 mb-3">{{ __('Option
                                4')
                                }} <span class="text-danger">*</span></label>
                            <textarea name="option_4"
                                class="form-control options @error('option_4') is-invalid @enderror"
                                id="option_4">{{old('option_4')}} {{ $question->option_4}}</textarea>
                        </div>
                    </div>

                    @if($question->question_type_id == 4)
                        <div class="col-md-12">
                            <div class="input__group mb-25">
                                <label class="label-text-title color-heading font-medium font-16 mb-3">{{ __('Option 5')}} <span class="text-danger">*</span></label>
                                <textarea name="option_5"
                                    class="form-control options @error('option_5') is-invalid @enderror"
                                    id="option_5">{{old('option_5')}} {{ $question->option_5 ?? ''}}</textarea>
                            </div>
                        </div>
                    @endif

                    <div class="col-md-6">
                        <div class="input__group mb-25">
                            <label class="label-text-title color-heading font-medium font-16 mb-3">{{
                                __('Correct Option ')}}
                                <span class="text-danger">*</span></label>
                            <input type="text" name="" id="" value="{{getQuestionOption($question->correct_option)}}">
                        </div>
                    </div>
                    @endif

                    <div class="col-md-6">
                        <div class="input__group mb-25">
                            <label class="label-text-title color-heading font-medium font-16 mb-3">{{ __('Hins -
                                If Have')
                                }} <span class="text-danger"></span></label>
                            <input type="text" name="hint" placeholder="Hints" value="{{$question->hint}}"
                                class="form-control" id="hint">
                        </div>
                    </div>

                    <div class="col-md-12">
                        <div class="input__group mb-20">
                            <label>{{__('Question Detail (If any)')}} <span class="text-danger"></span></label>
                            <textarea rows="5" name="question_detail" class="form-control"
                                id="questionDetail"> {{ $question->question_detail}}</textarea>
                        </div>
                    </div>

                    <div class="col-md-12">
                        <div class="input__group mb-20">
                            <label>{{__('Solution (If any)')}} <span class="text-danger"></span></label>
                            <textarea rows="5" name="solution" class="form-control"
                                id="solution">{{$question->solution}}</textarea>
                        </div>
                    </div>

                    <div class="row mb-3">

                    </div>
            </form>
            



        </div>


    </div>
</div>


@endsection

@push('style')
<link rel="stylesheet" href="{{asset('admin/css/custom/image-preview.css')}}">
@endpush

@push('script')
<script src="{{asset('admin/js/custom/image-preview.js')}}"></script>
<script src="{{asset('admin/js/custom/admin-profile.js')}}"></script>
{{-- <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script> --}}
<script>
    $(document).ready(function () {
        $('#academyTypeSelect').on('change', function () {
            var academyTypeId = $(this).val();
            console.log('id',academyTypeId)
            var url = "{{route('admin.getSubjects', ':academyType')}}";
            url = url.replace(':academyType', academyTypeId);
            if (academyTypeId) {
                $('#subjectSelect').prop('disabled', false);
                $('#subjectSelect').html('<option value="">Loading...</option>');
                $.ajax({
                    url: url, // Change this URL to your actual route
                    type: 'GET',
                    success: function (data) {
                        $('#subjectSelect').html('<option value="">Select Subject Name</option>');
                        $.each(data, function (key, value) {
                            $('#subjectSelect').append('<option value="' + value.id + '">' + value.title + '</option>');
                        });
                    }
                });
            } else {
                $('#subjectSelect').prop('disabled', true);
                $('#subjectSelect').html('<option value="">{{ __("Select Subject Name") }}</option>');
            }
        });
    });

    $(document).ready(function () {
        $('#subjectSelect').on('change', function () {
            var subjectId = $(this).val();
            var url = "{{route('admin.getChapters', ':subjectId')}}";
            url = url.replace(':subjectId', subjectId);
            if (subjectId) {
                $('#chapterSelect').prop('disabled', false);
                $('#chapterSelect').html('<option value="">Loading...</option>');
                $.ajax({
                    url: url, // Change this URL to your actual route
                    type: 'GET',
                    success: function (data) {
                        $('#chapterSelect').html('<option value="">Select Chapter Name</option>');
                        $.each(data, function (key, value) {
                            $('#chapterSelect').append('<option value="' + value.id + '">' + value.title + '</option>');
                        });
                    }
                });
            } else {
                $('#chapterSelect').prop('disabled', true);
                $('#chapterSelect').html('<option value="">{{ __("Select Chapter Name") }}</option>');
            }
        });
    });

    $(document).ready(function () {
        
        $(document).on( 'change', '.instituteSelect', function(){
        var selectid = $(this).attr("id"); 
        var intsId = $('#'+selectid).val();
        var matchResult = selectid.match(/instituteSelect(\d+)/);
        var unitId = matchResult && matchResult[1] ? matchResult[1] : '';
        var url = "{{route('admin.getUnits', ':intsId')}}";
        url = url.replace(':intsId', intsId);
            if (intsId) {
                $('#unitSelect'+unitId).prop('disabled', false);
                $('#unitSelect'+unitId).html('<option value="">Loading...</option>');
                $.ajax({
                    url: url, // Change this URL to your actual route
                    type: 'GET',
                    success: function (data) {
                        $('#unitSelect'+unitId).html('<option value="">Select Unit Name</option>');
                        $.each(data, function (key, value) {
                            $('#unitSelect'+unitId).append('<option value="' + value.id + '">' + value.title + '</option>');
                        });
                    }
                });
            } else {
                $('#unitSelect'+unitId).prop('disabled', true);
                $('#unitSelect'+unitId).html('<option value="">{{ __("Select Unit Name") }}</option>');
            }
        });
    });

</script>
<link href="{{ url('/plugins/mathquillmatheditor/lib/mathquill.css') }}" rel="stylesheet"> 
<link href="{{ url('/plugins/mathquillmatheditor/lib/matheditor.css') }}" rel="stylesheet"> 
<script src="{{ url('/plugins/mathquillmatheditor/lib/mathquill.min.js') }}"></script>
<script src="{{ url('/plugins/mathquillmatheditor/lib/matheditor.js') }}"></script>
<script src="{{ url('/plugins/mathquillmatheditor/lib/jquery.jslatex.packed.js') }}"></script>
<script src="{{asset('frontend/assets/js/summernote_math.js')}}"></script>
<script type="text/javascript" async
    src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.1/MathJax.js?config=TeX-MML-AM_HTMLorMML">
</script>
<script type="text/javascript">
    const optionsHtml=`
        <div class="form-row">
            <div class="form-group col-md-6">
                <label for="WhriteQuestion">Option 1</label>
                <textarea name="option_1" class="form-control options @error('option_1') is-invalid @enderror"
                    id="option_1">{{old('option_1')}}</textarea>
                @error('option_1')
                <span class="invalid-feedback" role="alert">
                    <strong>{{ $message }}</strong>
                </span>
                @enderror
            </div>
            <div class="form-group col-md-6">
                <label for="WhriteQuestion">Option 2</label>
                <textarea name="option_2" class="form-control options @error('option_2') is-invalid @enderror"
                    id="option_2">{{old('option_2')}}</textarea>
                @error('option_2')
                <span class="invalid-feedback" role="alert">
                    <strong>{{ $message }}</strong>
                </span>
                @enderror
            </div>
        </div>
        <div class="form-row">
            <div class="form-group col-md-6">
                <label for="WhriteQuestion">Option 3</label>
                <textarea name="option_3" class="form-control options @error('option_3') is-invalid @enderror"
                    id="option_3">{{old('option_3')}}</textarea>
                @error('option_3')
                <span class="invalid-feedback" role="alert">
                    <strong>{{ $message }}</strong>
                </span>
                @enderror
            </div>
            <div class="form-group col-md-6">
                <label for="WhriteQuestion">Option 4</label>
                <textarea name="option_4" class="form-control options @error('option_4') is-invalid @enderror"
                    id="option_4">{{old('option_4')}}</textarea>
                @error('option_4')
                <span class="invalid-feedback" role="alert">
                    <strong>{{ $message }}</strong>
                </span>
                @enderror
            </div>
        </div>
        <div class="form-group">
            <label for="correct_option">Correct Option</label>
            <select name="correct_option" class="form-control" id="correct_option">
                <option value="option_1">Option 1</option>
                <option value="option_2">Option 2</option>
                <option value="option_3">Option 3</option>
                <option value="option_4">Option 4</option>
            </select>
        </div>
        `;
        // Custom math button for Summernote
        const mathButton = function (context) {
            const ui = $.summernote.ui;
            const button = ui.button({
                contents: '<i class="fa fa-calculator"></i>',
                tooltip: 'Insert Math (LaTeX)',
                click: function () {
                    showMathDialog(context);
                }
            });
            return button.render();
        };

        const noteSetting={
            toolbar: [
            ['insert', ['mathCustom', 'picture']],
            ['style', ['bold', 'italic', 'underline', 'clear']],
            ['font', ['superscript', 'subscript']],
            ['para', ['ul', 'ol']],
            ['codeview', ['codeview']],
            ],
            buttons: {
                mathCustom: mathButton
            }
        };

        $(document).ready(function() {
          $('#math_editor').summernote({
            toolbar: [
                ['insert', ['math','picture']],
                ['style', ['bold', 'italic', 'underline', 'clear']],
                ['font', ['superscript', 'subscript']],
                ['para', ['ul', 'ol']],
                ['codeview', ['codeview']],
                ],
            });
            // $('#WhriteQuestion').summernote(noteSetting);
            $('#questionDetail').summernote(noteSetting);
            $('#solution').summernote(noteSetting);
            $('#WhriteQuestion').summernote(noteSetting);
            $('#options').html(optionsHtml);
            for(option of $('.options')){
                $(option).summernote(noteSetting);
            }
        });

</script>
<script type="text/javascript">
    var mathsecondeditor = new MathEditor('math_v2_editor');
</script>

<script>
    $(document).ready(function() {
  
    jQuery.fn.selectText = function(){
        this.find('input').each(function() {
            if($(this).prev().length == 0 || !$(this).prev().hasClass('p_copy')) { 
                $('<p class="p_copy" style="position: absolute; z-index: -1;"></p>').insertBefore($(this));
            }
            $(this).prev().html($(this).val());
        });
        var doc = document;
        var element = this[0];
        console.log(this, element);
        if (doc.body.createTextRange) {
            var range = document.body.createTextRange();
            range.moveToElementText(element);
            range.select();
        } else if (window.getSelection) {
            var selection = window.getSelection();        
            var range = document.createRange();
            range.selectNodeContents(element);
            selection.removeAllRanges();
            selection.addRange(range);
        }
    };
    // Copy Math Equition On click
    $(".copy-math-equition").on("click", function(){ 
        // .getValue
        // .getLatex
        var mathequitionlatex = mathsecondeditor.getLatex();   
        $("#renderer").empty();  
        $("#renderer").append("$$" + mathequitionlatex + "$$"); 
        MathJax.Hub.Queue(["Typeset", MathJax.Hub, $("#renderer")[0]]); 
        setTimeout(function() {
        var mathequitionCopied = $('.MathJax_Display').selectText();  
        document.execCommand('copy');
        }, 500); 
    });
});

$(document).ready(function() {
    $('#question_type_id').change(function() {
        var selectedValue = $(this).val();
        if (selectedValue == '2' || selectedValue == '3') {
            $('.optionBox1, .optionBox2, .optionBox3, .optionBox4 , .CorrectOption').hide();
        } else {
            $('.optionBox1, .optionBox2, .optionBox3, .optionBox4 , .CorrectOption').show();
        }
    });
});

$(document).ready(function() {
    // Function to toggle the visibility of option boxes based on question_type_id
    function toggleOptions(questionTypeId) {
        if (questionTypeId == 1) {
            // Show all option boxes if question_type_id is 1
            $('.optionBox1, .optionBox2, .optionBox3, .optionBox4, .CorrectOption').show();
        } else if (questionTypeId == 2 || questionTypeId == 3) {
            // Hide all option boxes if question_type_id is 2 or 3
            $('.optionBox1, .optionBox2, .optionBox3, .optionBox4, .CorrectOption').hide();
        } else {
            // Hide all option boxes by default for other cases
            $('.optionBox1, .optionBox2, .optionBox3, .optionBox4, .CorrectOption').hide();
        }
    }

    // On page load, check the initial question type ID and toggle options accordingly
    toggleOptions($('#question_type_id').val());

    // When the user changes the question type ID
    $('#question_type_id').change(function() {
        var selectedTypeId = $(this).val();
        toggleOptions(selectedTypeId);
    });
});

// Function to show math dialog
function showMathDialog(context) {
    const modalHtml = `
        <div class="modal fade" id="mathModal" tabindex="-1" role="dialog">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Insert Math (LaTeX)</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="form-group">
                            <label>LaTeX Input:</label>
                            <input type="text" id="latexInput" class="form-control" placeholder="Enter LaTeX code (e.g., x^2, \\frac{a}{b}, \\sqrt{x})">
                            <small class="form-text text-muted">
                                Examples: x^2, \\frac{a}{b}, \\sqrt{x}, \\alpha, \\beta, \\sum_{i=1}^n, \\int_a^b
                            </small>
                        </div>
                        <div class="form-group">
                            <label>Preview:</label>
                            <div id="mathPreview" style="min-height: 50px; border: 1px solid #ddd; padding: 10px; background: #f9f9f9; font-size: 16px; text-align: center;"></div>
                        </div>
                        <div class="form-group">
                            <label>Common LaTeX Expressions:</label>
                            <div class="row">
                                <div class="col-md-6">
                                    <button type="button" class="btn btn-sm btn-outline-secondary m-1 latex-btn" data-latex="x^2">x²</button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary m-1 latex-btn" data-latex="x_1">x₁</button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary m-1 latex-btn" data-latex="\\frac{a}{b}">a/b</button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary m-1 latex-btn" data-latex="\\sqrt{x}">√x</button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary m-1 latex-btn" data-latex="\\alpha">α</button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary m-1 latex-btn" data-latex="\\beta">β</button>
                                </div>
                                <div class="col-md-6">
                                    <button type="button" class="btn btn-sm btn-outline-secondary m-1 latex-btn" data-latex="\\sum_{i=1}^n">Σ</button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary m-1 latex-btn" data-latex="\\int_a^b">∫</button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary m-1 latex-btn" data-latex="\\lim_{x \\to \\infty}">lim</button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary m-1 latex-btn" data-latex="\\pi">π</button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary m-1 latex-btn" data-latex="\\theta">θ</button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary m-1 latex-btn" data-latex="\\infty">∞</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary" id="insertMath">Insert</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal if any
    $('#mathModal').remove();

    // Add modal to body
    $('body').append(modalHtml);

    // Show modal
    $('#mathModal').modal('show');

    // Handle LaTeX input and preview
    $('#latexInput').on('input', function() {
        const latex = $(this).val();
        updatePreview(latex);
    });

    // Handle predefined LaTeX buttons
    $('.latex-btn').on('click', function() {
        const latex = $(this).data('latex');
        $('#latexInput').val(latex);
        updatePreview(latex);
    });

    // Handle close button (X)
    $('#mathModal .close').on('click', function() {
        $('#mathModal').modal('hide');
    });

    // Handle cancel button
    $('#mathModal [data-dismiss="modal"]').on('click', function() {
        $('#mathModal').modal('hide');
    });

    // Handle insert button
    $('#insertMath').on('click', function() {
        const latex = $('#latexInput').val();
        if (latex.trim()) {
            // Create math span with LaTeX
            const mathHtml = `<span class="math-tex">\\(${latex}\\)</span>`;
            context.invoke('editor.pasteHTML', mathHtml);

            // Re-render MathJax for the new content
            if (window.MathJax) {
                MathJax.Hub.Queue(["Typeset", MathJax.Hub]);
            }
        }
        $('#mathModal').modal('hide');
    });

    // Handle ESC key to close modal
    $(document).on('keydown', function(e) {
        if (e.key === 'Escape' && $('#mathModal').is(':visible')) {
            $('#mathModal').modal('hide');
        }
    });

    function updatePreview(latex) {
        const previewDiv = document.getElementById('mathPreview');
        if (latex.trim()) {
            try {
                if (window.katex) {
                    katex.render(latex, previewDiv);
                } else {
                    previewDiv.innerHTML = `\\(${latex}\\)`;
                    if (window.MathJax) {
                        MathJax.Hub.Queue(["Typeset", MathJax.Hub, previewDiv]);
                    }
                }
            } catch (e) {
                previewDiv.innerHTML = '<span style="color: red;">Invalid LaTeX</span>';
            }
        } else {
            previewDiv.innerHTML = '';
        }
    }
}
</script>

@endpush

