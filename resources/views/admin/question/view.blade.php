@extends('layouts.instructor')
<style>
    .dropdown.all-student-filter-dropdown{
        display: flex;
        align-items: end;
        gap: 10px;
        width: 50%;
    }
    .searching-parent{
        display: flex;
        align-items: center;
        gap: 25px;
        width: 100%;
        position: relative;
    }

    .question-search-icon {
        position: absolute;
        right: 8px;
        top: 6px;
    }

    .searching-flex-parent{
        width: 100%;
    }


    .common-add-btn {
        border: 1px solid rgba(0, 0, 0, 0.07);
        padding: 11px 20px;
        border-radius: 8px;
        white-space: nowrap;
    }

</style>
@section('breadcrumb')
<div class="page-banner-content text-center">
    <h3 class="page-banner-heading text-white pb-15"> {{ __('Bundles Courses') }} </h3>

    <!-- Breadcrumb Start-->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb justify-content-center">
            <li class="breadcrumb-item font-14"><a href="{{ route('instructor.dashboard') }}">{{ __('Dashboard') }}</a>
            </li>
            <li class="breadcrumb-item font-14 active" aria-current="page">{{ __('Bundles Courses') }}</li>
        </ol>
    </nav>
</div>
@endsection

@section('content')
<div class="instructor-profile-right-part">
    <div class="instructor-quiz-list-page instructor-all-student-page">
        <div class="instructor-my-courses-title d-flex justify-content-between align-items-center">
            <h6>{{__('All Questions')}}</h6>
            <div class="dropdown all-student-filter-dropdown">
                 <form action="{{ route('instructor.academy_question.index') }}" name="get" class="searching-parent">
                    <div class=" m-0 p-0 searching-flex-parent">
                        <input type="search" name="search_name" class="form-control" placeholder="{{ __('Searching...') }}" value="{{ app('request')->search_name }}">
                    </div>
                    <button type="submit" class="theme-btn default-hover-btn theme-button1 question-search-icon"><i class="fas fa-search"></i></button>
                </form>
                <a href="{{route('instructor.academy_question.create')}}" class="common-add-btn" type="button">{{__('Add Question')}}</a>
            </div>
        </div>
        <div class="row">
            @if(count($questions) > 0)
            <div class="col-md-12">
                <div class="customers__area bg-style mb-30">
                    <div class="table-responsive table-responsive-xl">
                        <table class="table">
                            <thead>
                            <tr>
                                {{-- <th>SL</th> --}}
                                <th>Title</th>
                                <th>Subject</th>
                                <th>Chapter</th>
                                <th>Topic</th>
                                <th>Academy Type</th>
                                <th>Question Type</th>
                                <th>Action</th>
                            </tr>
                            </thead>
                            <tbody>
                            @foreach($questions as $key => $question)
                                <tr class="removable-item">
                                    {{-- <td> {{ ++$key}}</td> --}}
                                    <td>{!! $question->question_text !!}</td>
                                    <td>{{ $question->subject_name}}</td>
                                    <td>{{ $question->chapter_name}}</td>
                                    <td>{{ $question->topic_name}}</td>
                                    <td>{{ $question->academy_type_name}}</td>
                                    <td>{{ $question->question_type}}</td>
                                    <td>
                                        <div class="action__buttons">

                                            <a href="{{route('instructor.question.edit',['id'=>$question->id])}}">
                                                <button class="theme-btn theme-button1 orange-theme-btn default-hover-btn">Edit</button>
                                            </a>

                                            <a href="{{route('instructor.question.details',['id'=>$question->id])}}">
                                                <button class="theme-btn theme-button1 green-theme-btn default-hover-btn btn btn-outline-info">Details</button>
                                            </a>

                                            <a onclick="questionDelete({{$question->id}})">
                                                <button class="theme-btn theme-button1 red-theme-btn default-hover-btn">Delete</button>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="mt-3">
                    {{$questions->links()}}
                </div>
            </div>

            @else
                <!-- If there is no data Show Empty Design Start -->
                <div class="empty-data">
                    <img src="{{ asset('frontend/assets/img/empty-data-img.png') }}" alt="img" class="img-fluid">
                    <h5 class="my-3">{{__('Empty Questions')}}</h5>
                </div>
                <!-- If there is no data Show Empty Design End -->
            @endif
        </div>


    </div>
</div>


@endsection

@push('style')
    <link rel="stylesheet" href="{{asset('admin/css/jquery.dataTables.min.css')}}">
@endpush

@push('script')
    <script src="{{asset('admin/js/jquery.dataTables.min.js')}}"></script>
    <script src="{{asset('admin/js/custom/data-table-page.js')}}"></script>
    <script>
        function questionDelete(id){

            Swal.fire({
                title: "{{ __('Are you sure to delete question?') }}",
                text: "{{ __('You won`t be able to revert this!') }}",
                icon: "warning",
                showCancelButton: true,
                confirmButtonText: "{{__('Yes')}}",
                cancelButtonText: "{{__('No!')}}",
                reverseButtons: true
            }).then(function (result) {
                if (result.value) {
                    $.ajax({
                        type: "DELETE",
                        url: "{{ route('instructor.question.delete', ['id' => ':id']) }}".replace(':id', id),
                        data: {"_token": "{{ csrf_token() }}",},
                        success: function (data) {
                            toastr.options.positionClass = 'toast-bottom-right';
                            toastr.success('', "{{ __('Question has been deleted') }}");
                        },
                        error: function (error) {
                            console.log("Error!",error);
                        },
                    });
                } else if (result.dismiss === "cancel") {
                }
            });

        }
    </script>
@endpush
