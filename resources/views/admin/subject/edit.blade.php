@extends('layouts.instructor')

@section('breadcrumb')
<div class="page-banner-content text-center">
    <h3 class="page-banner-heading text-white pb-15"> {{ __('Bundles Courses') }} </h3>

    <!-- Breadcrumb Start-->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb justify-content-center">
            <li class="breadcrumb-item font-14"><a href="{{ route('instructor.dashboard') }}">{{ __('Dashboard') }}</a>
            </li>
            <li class="breadcrumb-item font-14 active" aria-current="page">{{ __('Add Subject') }}</li>
        </ol>
    </nav>
</div>
@endsection

@section('content')

<div class="instructor-profile-right-part">
    <div class="instructor-quiz-list-page instructor-all-student-page">

        <div class="instructor-my-courses-title d-flex justify-content-between align-items-center">
            <h6>{{__('Edit Subject')}}</h6>
        </div>

        <div class="row">
            
            <form action="{{route('instructor.subject.update',$subject->id)}}" method="post" class="form-horizontal">
                @csrf

                <div class="row mb-3">
                    <div class="col-md-12">
                        <div class="input__group text-black">
                            <label>{{ __('Subject Name') }} <span class="text-danger">*</span></label>
                            <input type="text" name="title" value="{{old('title',$subject->title)}}" placeholder="{{ __('Type Subject Name') }}"
                                   class="form-control" required>
                            @if ($errors->has('title'))
                                <span class="text-danger"><i class="fas fa-exclamation-triangle"></i> {{ $errors->first('title') }}</span>
                            @endif
                        </div>
                    </div>
                </div>
               
                <div class="row mb-3" id="">
                    <div class="col-md-12">
                        <div class="input__group text-black">
                            <label for="instructor_ids">{{ __('Acadymic Type') }}</label>
                            <select name="academy_type"  id="academy_type" class="form-control multiple-basic-single">
                                @foreach($acadymic_types as $acadymic_type)
                                    <option @if($acadymic_type->id==$subject->academy_type) selected @endif  value="{{ $acadymic_type->id }}">{{ $acadymic_type->title }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                </div>
               
                <div class="row mb-3">
                    <div class="col-md-12">
                        <div class="input__group text-black">
                            <label>{{ __('Status') }} <span class="text-danger">*</span></label>
                            <select name="status" id="status" class="form-control">
                                <option value="">--{{ __('Select Option') }}--</option>
                                <option @if($subject->status == 1) selected @endif value="1"> {{ __('Active') }}</option>
                                <option @if($subject->status == 0) selected @endif value="0">  {{ __('Deactivated') }}</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div>
                    <button type="submit" class="theme-btn theme-button1 default-hover-btn">{{__('Update')}}</button>
                </div>

            </form>



        </div>


    </div>
</div>


@endsection



@push('style')

@endpush

@push('script')
    <script src="{{ asset('admin/js/custom/coupon-create.js') }}"></script>
@endpush

