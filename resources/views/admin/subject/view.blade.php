@extends('layouts.instructor')

@section('content')
<style>
    .dropdown.all-student-filter-dropdown{
        display: flex;
        align-items: end;
        gap: 10px;
        width: 50%;
    }
    .searching-parent{
        display: flex;
        align-items: center;
        gap: 25px;
        width: 100%;
        position: relative;
    }

    button.theme-btn.default-hover-btn.theme-button1 {
        position: absolute;
        right: 8px;
        top: 6px;
    }

    .searching-flex-parent{
        width: 100%;
    }

    .common-add-btn {
        border: 1px solid rgba(0, 0, 0, 0.07);
        padding: 11px 20px;
        border-radius: 8px;
        white-space: nowrap;
    }

</style>

<div class="instructor-profile-right-part">
    <div class="instructor-quiz-list-page instructor-all-student-page">

        <div class="instructor-my-courses-title d-flex justify-content-between align-items-center">
            <h6>{{__('All Subjects')}}</h6>

            <div class="dropdown all-student-filter-dropdown">
                 <form action="{{ route('instructor.subject.index') }}" name="get" class="searching-parent">
                    <div class=" m-0 p-0 searching-flex-parent">
                        <!-- <div class="label-text-title color-heading font-medium font-16">{{__('Search By Name')}}</div> -->

                        <input type="search" name="search_name" class="form-control" placeholder="{{ __('Searching...') }}" value="{{ app('request')->search_name }}">
                    </div>
                   
                    <button type="submit" class="theme-btn default-hover-btn theme-button1"><i class="fas fa-search"></i></button>
                </form>
                <a href="{{route('instructor.subject.add')}}" class="common-add-btn" type="button">{{__('Add Subject')}}</a>
            </div>

        </div>

        <div class="row">
            @if(count($subjects) > 0)
            <div class="col-12">
                <div class="table-responsive table-responsive-xl">
                    <table class="table">
                        <thead>
                            <tr>
                                {{-- <th>SL</th> --}}
                                <th>Title</th>
                                <th>Academy Type</th>
                                <th>Status</th>
                                <th>Action</th>
                            </tr>
                            </thead>
                            <tbody>
                            @foreach($subjects as $key => $subject)
                                <tr class="removable-item">
                                    {{-- <td> {{ ++$key}}</td> --}}
                                    <td>{{ $subject->title}}</td>
                                    <td> {{ $subject->academyType->title ?? '' }}</td>
                                    <td> {{ $subject->status == 1 ? "Active" : "Deactivated" }}</td>
                                    <td>
                                        <div class="action__buttons">
                                            <a href="{{route('instructor.subject.edit', [$subject->id])}}" class="btn-action mr-30" title="Edit Details">
                                                <img src="{{asset('admin/images/icons/edit-2.svg')}}" alt="edit">
                                            </a>

                                            <a href="{{route('instructor.subjectBaseQuestion.update', [$subject->id])}}" class="btn-action mr-30" title="Edit Details">
                                                <img src="{{asset('admin/images/icons/qus.svg')}}" alt="questions">
                                            </a>

                                            <button class="btn-action ms-2 deleteItem"
                                                        data-formid="delete_row_form_{{ $subject->id }}">
                                                        <img src="{{ asset('admin/images/icons/trash-2.svg') }}"
                                                            alt="trash">
                                            </button>

                                            <form action="{{route('instructor.subject.delete', [$subject->id])}}" method="post" id="delete_row_form_{{ $subject->id }}">
                                                {{ method_field('DELETE') }}
                                                <input type="hidden" name="_token" value="{{ csrf_token() }}">
                                            </form>

                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                            </tbody>
                        </table>
                        <div class="mt-3">
                            {{$subjects->links()}}
                        </div>
                </div>
            </div>

            @else
                <!-- If there is no data Show Empty Design Start -->
                <div class="empty-data">
                    <img src="{{ asset('frontend/assets/img/empty-data-img.png') }}" alt="img" class="img-fluid">
                    <h5 class="my-3">{{__('Empty Subjects')}}</h5>
                </div>
                <!-- If there is no data Show Empty Design End -->
            @endif
        </div>


    </div>
</div>


@endsection

@push('style')
<link rel="stylesheet" href="{{asset('frontend/assets/css/custom/img-view.css')}}">
@endpush

@push('script')
<script src="{{asset('frontend/assets/js/custom/img-view.js')}}"></script>
@endpush

@push('script')
    <script src="{{asset('admin/js/jquery.dataTables.min.js')}}"></script>
    <script src="{{asset('admin/js/custom/data-table-page.js')}}"></script>
@endpush