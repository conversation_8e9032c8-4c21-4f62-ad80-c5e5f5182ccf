@extends('layouts.instructor')

@section('breadcrumb')
<div class="page-banner-content text-center">
    <h3 class="page-banner-heading text-white pb-15"> {{ __('Bundles Courses') }} </h3>
    <!-- Breadcrumb Start-->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb justify-content-center">
            <li class="breadcrumb-item font-14"><a href="{{ route('instructor.dashboard') }}">{{ __('Dashboard') }}</a>
            </li>
            <li class="breadcrumb-item font-14 active" aria-current="page">{{ __('Add Topic') }}</li>
        </ol>
    </nav>
</div>
@endsection
@section('content')
<div class="instructor-profile-right-part">
    <div class="instructor-quiz-list-page instructor-all-student-page">
        <div class="instructor-my-courses-title d-flex justify-content-between align-items-center">
            <h6>{{__('Add Topic')}}</h6>
            <button type="button" class="theme-btn theme-button default-delete-btn-red" onclick="clearFormAndCallRoute()">Clear Form</button>
        </div>
        <div class="row">
            <form action="{{route('instructor.topic.store')}}" method="post" class="form-horizontal">
                @csrf
                <div class="row mb-3">
                    <div class="col-md-4">
                        <div class="input__group text-black">
                            <label>{{ __('Academy Type') }} <span class="text-danger">*</span></label>
                            <select class="form-control" id="academyTypeSelect" name="academy_type">
                                <option value>{{ __("Select Academy Type") }}</option>
                                @foreach ($academy_types as $academy_type)
                                <option value="{{ $academy_type->id }}" {{ (isset($cachedData['academy_type']) && $cachedData['academy_type'] == $academy_type->id) ? 'selected' : '' }}> {{$academy_type->title}} </option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="input__group text-black">
                            <label>{{ __('Subject') }} <span class="text-danger">*</span></label>
                            <select class="form-control" id="subjectSelect" name="subject_id" disabled>
                                <option value>{{ __("Select Subject Name") }}</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="input__group text-black">
                            <label for="instructor_ids">{{ __('Chapter Name') }} <span
                                    class="text-danger">*</span></label>
                            <select name="chapter_id" id="chapterSelect" class="form-control" disabled>
                                <option value="">{{ __('Select Chapter Name') }}</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-12">
                        <div class="input__group text-black">
                            <label>{{ __('Topic Name') }} <span class="text-danger">*</span></label>
                            <input type="text" name="title" value="{{old('title')}}"
                                placeholder="{{ __('Type Topic Name') }}" class="form-control" required>
                            @if ($errors->has('title'))
                            <span class="text-danger"><i class="fas fa-exclamation-triangle"></i> {{
                                $errors->first('title') }}</span>
                            @endif
                        </div>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-12">
                        <div class="input__group text-black">
                            <label>{{ __('Status') }} <span class="text-danger">*</span></label>
                            <select name="status" id="status" class="form-control">
                                <option value="">--{{ __('Select Option') }}--</option>
                                <option value="1" {{ (isset($cachedData['status']) && $cachedData['status'] == '1') ? 'selected' : '' }}>{{ __('Active') }}</option>
                                <option value="0" {{ (isset($cachedData['status']) && $cachedData['status'] == '0') ? 'selected' : '' }}>{{ __('Deactivated') }}</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div>
                    <button type="submit" class="theme-btn theme-button1 default-hover-btn">{{__('Create')}}</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
@push('style')
@endpush
@push('script')
<script src="{{ asset('admin/js/custom/coupon-create.js') }}"></script>

<script>
    // Cache data from server
    var cachedData = @json($cachedData);

    $(document).ready(function () {
        // Load cached data if available
        if (cachedData) {
            loadCachedData();
        }

        $('#academyTypeSelect').on('change', function () {
            var academyTypeId = $(this).val();
            var url = "{{route('topic.getSubjects', ':academyType')}}";
            url = url.replace(':academyType', academyTypeId);
            if (academyTypeId) {
                $('#subjectSelect').prop('disabled', false);
                $('#subjectSelect').html('<option value="">Loading...</option>');
                $.ajax({
                    url: url,
                    type: "GET",
                    success: function (data) {
                        $('#subjectSelect').html('<option value="">Select Subject Name</option>');
                        $.each(data, function (key, value) {
                            $('#subjectSelect').append('<option value="' + value.id + '">' + value.title + '</option>');
                        });
                    }
                });
            } else {
                $('#subjectSelect').prop('disabled', true);
                $('#subjectSelect').html('<option value="">{{ __("Select Subject Name") }}</option>');
            }
        });
    });

    $(document).ready(function () {
        $('#subjectSelect').on('change', function () {
            var subjectId = $(this).val();
            var url = "{{route('topic.getChapters', ':subjectId')}}";
            url = url.replace(':subjectId', subjectId);
            if (subjectId) {
                $('#chapterSelect').prop('disabled', false);
                $('#chapterSelect').html('<option value="">Loading...</option>');
                $.ajax({
                    url: url,
                    type: 'GET',
                    success: function (data) {
                        $('#chapterSelect').html('<option value="">Select Chapter Name</option>');
                        $.each(data, function (key, value) {
                            $('#chapterSelect').append('<option value="' + value.id + '">' + value.title + '</option>');
                        });
                    }
                });
            } else {
                $('#chapterSelect').prop('disabled', true);
                $('#chapterSelect').html('<option value="">{{ __("Select Chapter Name") }}</option>');
            }
        });
    });

    // Function to load cached data and populate dependency chain
    function loadCachedData() {
        console.log('Loading cached data:', cachedData);

        // Set academy type and trigger change
        if (cachedData.academy_type) {
            $('#academyTypeSelect').val(cachedData.academy_type);
            loadSubjects(cachedData.academy_type, function() {
                // After subjects are loaded, set subject and load chapters
                if (cachedData.subject_id) {
                    $('#subjectSelect').val(cachedData.subject_id);
                    loadChapters(cachedData.subject_id, function() {
                        // After chapters are loaded, set chapter
                        if (cachedData.chapter_id) {
                            $('#chapterSelect').val(cachedData.chapter_id);
                        }
                    });
                }
            });
        }

        // Set status
        if (cachedData.status) {
            $('#status').val(cachedData.status);
        }
    }

    // Helper function to load subjects
    function loadSubjects(academyTypeId, callback) {
        var url = "{{route('topic.getSubjects', ':academyType')}}";
        url = url.replace(':academyType', academyTypeId);

        $('#subjectSelect').prop('disabled', false);
        $('#subjectSelect').html('<option value="">Loading...</option>');

        $.ajax({
            url: url,
            type: "GET",
            success: function (data) {
                $('#subjectSelect').html('<option value="">Select Subject Name</option>');
                $.each(data, function (key, value) {
                    $('#subjectSelect').append('<option value="' + value.id + '">' + value.title + '</option>');
                });
                if (callback) callback();
            }
        });
    }

    // Helper function to load chapters
    function loadChapters(subjectId, callback) {
        var url = "{{route('topic.getChapters', ':subjectId')}}";
        url = url.replace(':subjectId', subjectId);

        $('#chapterSelect').prop('disabled', false);
        $('#chapterSelect').html('<option value="">Loading...</option>');

        $.ajax({
            url: url,
            type: 'GET',
            success: function (data) {
                $('#chapterSelect').html('<option value="">Select Chapter Name</option>');
                $.each(data, function (key, value) {
                    $('#chapterSelect').append('<option value="' + value.id + '">' + value.title + '</option>');
                });
                if (callback) callback();
            }
        });
    }

    function clearFormAndCallRoute() {
        $.ajax({
            url: "{{ route('instructor.cacheClearLatestTopicInformation') }}",
            type: "GET",
            success: function(response) {
                toastr.success('','Cache cleared successfully.');
                window.location.reload();
            },
            error: function(xhr) {
                toastr.error('','An error occurred while clearing the cache.');
            }
        });
    };
</script>
@endpush