.matheditor-btn-span{
    width: 45px;
    height: 30px;
    background: transparent;
    border: 1px solid #ddd;
    margin-right: 4px;
    margin-bottom: 4px;
    background: #F9F9F9;
    cursor: pointer;
    overflow: hidden;
    vertical-align: middle;
    display: inline-block;
    color: #000000;
    font-size: 10pt;
    line-height: 30px;
}
.matheditor-btn-span:hover{
    background: #FFF;
    border: 1px solid #aaa
}
.mq-editable-field span.mq-hasCursor{
  box-shadow: #68b4df 0 0 3px 2px !important; 
}
.op-btn .op-btn-icon{
    width: 100%;
    text-align: center;
    cursor: pointer;
}
div[class^="matheditor-wrapper-"]{
    /*margin: auto;*/
}
div[class^="matheditor-toolbar-"]{
    margin-bottom: 3px;
    border: 1px solid #ddd;
    padding: 5px;
    border-radius: 4px;
    background: #FFF;
}
div[class^="matheditor-wrapper-"] sup{
    top: 0;
}
div[class^="matheditor-wrapper-"] .mq-editable-field, .mq-math-mode .mq-editable-field{
    border: 1px solid #ddd;
    border-radius: 4px;
}
div[class^="matheditor-wrapper-"] .mq-editable-field.mq-focused{
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: none;
}
div[class^="matheditor-wrapper-"] .op-btn img {
    width: 14pt;
    margin: auto;
    display: block;
    position: relative;
    top: 5px;
}
div[class^="matheditor-wrapper-"] .mq-root-block, .mq-math-mode .mq-root-block{
    white-space: initial;
    word-wrap: break-word;
    box-shadow: none !important;
}

div[class^="matheditor-wrapper-"] ul[class^="tabs-"]{
    margin: 0px;
    padding: 0px;
    list-style: none;
    border-bottom: 1px solid #f4f4f4;
    margin-bottom: 5px;
}
div[class^="matheditor-wrapper-"] ul[class^="tabs-"] li{
    display: inline-block;
    padding: 2px 10px;
    cursor: pointer;
    margin: 0px;
    height: 20px;
}

div[class^="matheditor-wrapper-"] li.current{
    background: #F9F9F9;
    color: #222;
    font-weight: bold;
}

div[class^="matheditor-wrapper-"] .tab-content-me{
    display: none;
}

div[class^="matheditor-wrapper-"] .tab-content-me.current{
    display: inline-block;
}

[id^="selectable-cube_root-"] .mq-root-block, [id^="selectable-root-"] .mq-root-block, [id^="selectable-square_root-"] .mq-root-block{
   padding-top: 5px !important;
   font-size: 10pt;
}

[id^="tab-2-"] .mq-root-block, [id^="tab-3-"] .mq-root-block{
    padding-top: 4px !important;
    font-size: 15pt;
}

.mq-math-mode var.mq-f {
    margin-right: 0;
    margin-left: 0;
}
span[id^="close-btn-"]{
    float: right;
    border: 1px solid;
    border-radius: 3px;
    padding: 0px 5px;
    color: pink;
    cursor: pointer;
}
div[id^="keys-"]{
    text-align: center;
    display: none; 
    position:fixed;
    z-index:999999;
    bottom:0px;
    left:0px;
    width:100%;
    background: #f5f5f5;
}
div[id^="keys-"] .ks{
    height: 45px;
    width: 35px;
    display: inline-block;
    background: #f5f5f5;
    text-align: center;
    line-height: 45px;
    text-decoration: none;
    color: #000000;
    cursor: pointer;
    font-size: 14pt;
}
div[id^="keys-"] .ks:hover{
    background: #cccccc;
}
div[id^="keys-"] .ks:active{
    background: #bbbbbb;
}
div[id^="keys-"] .ks.long{
    width: 45px;
}
div[id^="keys-"] .ks.icon{
    font-weight: bold;
    font-size: 14pt;
}
div[id^="keys-"] .ks.takeup{
    background: #FFC0CB
}
div[id^="keys-"] .ks.too_long{
    background: #eeeeee;
}
div.close-btn{
    height: 30px;
    position: relative;
    right: 8px;
}


.matheditor-wrapper-math_v2_editor {
    width: 100% !important;
}

.matheditor-toolbar-math_v2_editor {
    width: 100% !important;
    max-width: 100% !important;
}

div#math_v2_editor {
    width: 100% !important;
    max-width: 100% !important;
    min-height: 150px !important;
}