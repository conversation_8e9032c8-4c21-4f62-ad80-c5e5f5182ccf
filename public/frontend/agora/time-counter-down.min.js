!function(e){"use strict";e.fn.extend({countdown100:function(t){t=e.extend({timeZone:"",endtimeYear:0,endtimeMonth:0,endtimeDate:0,endtimeHours:0,endtimeMinutes:0,endtimeSeconds:0},t);return this.each(function(){var n=e(this),a=new Date,o=t.timeZone,i=t.endtimeYear,r=t.endtimeMonth,s=t.endtimeDate,d=t.endtimeHours,m=t.endtimeMinutes,u=t.endtimeSeconds;if(""==o)var f=new Date(i,r-1,s,d,m,u);else f=moment.tz([i,r-1,s,d,m,u],o).format();Date.parse(f)<Date.parse(a)&&(f=new Date(Date.parse(new Date)+24*s*60*60*1e3+60*d*60*1e3+60*m*1e3+1e3*u)),function(t){var a=e(n).find(".days"),o=e(n).find(".hours"),i=e(n).find(".minutes"),r=e(n).find(".seconds");function s(){var e=function(e){var t=Date.parse(e)-Date.parse(new Date),n=Math.floor(t/1e3%60),a=Math.floor(t/1e3/60%60),o=Math.floor(t/36e5%24),i=Math.floor(t/864e5);return{total:t,days:i,hours:o,minutes:a,seconds:n}}(t);a.html(e.days),o.html(e.hours),i.html(e.minutes),r.html(e.seconds),e.total<=0&&clearInterval(d)}s();var d=setInterval(s,1e3)}(f)})}})}(jQuery);
