
.agora-users {
  height: calc(100vh - 137px);
  overflow-y: auto;
}

.user-card .avatar {
  width: 40px;
  height: 40px;
}

.agora-stream .stream-player {
  width: 100%;
  height: calc(100vh - 220px);
  background-color: transparent;
}

.agora-stream .player {
  width: 100%;
  height: calc(100vh - 220px);
  background-color: #f1f1f1;
  border-radius: 10px;
}

.agora-stream .player > div {
  border-radius: 10px;
  background-color: transparent;
}

.agora-stream .player video {
  border-radius: 10px;
  -o-object-fit: contain !important;
     object-fit: contain !important;
}

@media (max-width: 991px) {
  .agora-stream .player {
    height: 60vh;
  }
}

.agora-stream .remote-stream-box {
  display: flex;
  align-items: center;
  position: absolute;
  right: 0;
  bottom: 15px;
  z-index: 131;
}

.agora-stream .remote-stream-box.is-fullscreen {
  position: static !important;
}

.agora-stream .remote-stream-box .remote-stream {
  position: relative;
  transition: all 0.3s ease;
}

.agora-stream .remote-stream-box .remote-stream > div {
  margin-right: 15px;
  width: 200px !important;
  height: 140px !important;
  border-radius: 5px;
  border: 1px solid #585656;
}

@media (max-width: 991px) {
  .agora-stream .remote-stream-box .remote-stream > div {
    width: 100px !important;
    height: 90px !important;
  }
}

.agora-stream .remote-stream-box .remote-stream .remote-stream-fullscreen {
  position: absolute;
  top: 10px;
  right: 25px;
  width: 32px;
  height: 32px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 5px;
  border-radius: 5px;
  background-color: rgba(0, 0, 0, 0.5);
  color: #FFFFFF;
  cursor: pointer;
  z-index: 111;
}

@media (max-width: 991px) {
  .agora-stream .remote-stream-box .remote-stream .remote-stream-fullscreen {
    width: 24px;
    height: 24px;
    padding: 3px;
    top: 5px;
    right: 20px;
  }
}

.agora-stream .remote-stream-box .remote-stream .remote-stream-user-info {
  position: absolute;
  width: 90%;
  padding: 5px;
  border-radius: 5px;
  background-color: rgba(0, 0, 0, 0.5);
  font-size: 12px;
  font-weight: 500;
  color: #FFFFFF;
  bottom: 0;
  z-index: 11;
}

.agora-stream .remote-stream-box .remote-stream.is-fullscreen {
  position: absolute;
  inset: 0;
  z-index: 131;
}

.agora-stream .remote-stream-box .remote-stream.is-fullscreen > div {
  margin-right: 0;
  width: 100% !important;
  height: 100% !important;
  border-radius: 10px;
  border: none;
}

.agora-stream .time-item {
  width: 24px;
}

.agora-stream .stream-player.screen-shared {
  position: absolute !important;
  width: 250px;
  height: 150px;
  left: 0;
  z-index: 5;
}

.agora-stream .stream-footer .stream-bottom-actions.dont-join-users .icon-box {
  position: relative;
}

.agora-stream .stream-footer .stream-bottom-actions.dont-join-users .icon-box:after {
  content: "";
  position: absolute;
  width: 40px;
  height: 2px;
  background-color: #343434;
  transform: rotate(38deg);
  left: -5px;
  top: 12px;
}

@media (max-width: 991px) {
  .agora-stream .stream-footer {
    flex-wrap: wrap;
  }

  .agora-stream .stream-footer .stream-bottom-actions {
    flex: 1 1 90px;
  }
}


.agora-page .agora-tabs {
  width: 70px;
  height: 100vh;
  transition: all 0.3s ease;
}

.agora-page .agora-tabs .agora-tabs-icons {
  width: 20px;
  height: 20px;
}

.agora-page .agora-tabs .agora-tabs-icons svg {
  width: 100%;
  height: 100%;
}

.agora-page .agora-tabs .agora-tabs-link-text,
.agora-page .agora-tabs .tab-content {
  display: none;
  transition: all 0.4s ease;
}

.agora-page .agora-tabs.show {
  width: 373px;
}

.agora-page .agora-tabs.show .agora-tabs-link-text,
.agora-page .agora-tabs.show .tab-content {
  display: block;
}

.agora-page .agora-tabs:not(.show) .nav-tabs {
  flex-direction: column;
}

.agora-page .agora-tabs:not(.show) .nav-tabs .nav-item {
  margin-top: 20px;
}

.agora-page .agora-tabs:not(.show) .nav-tabs .nav-item:first-child {
  margin-top: 0;
}

.agora-page .agora-tabs:not(.show) .nav-tabs .nav-item a:after {
  display: none;
}

@media (max-width: 991px) {
  .agora-page .agora-tabs {
    width: 100% !important;
    height: auto;
  }

  .agora-page .agora-tabs .nav-tabs {
    padding-top: 15px;
  }
}

button.disabled:hover, button.disabled.theme-button1:hover, button.disabled.theme-button2:hover {
  color:  #343434 !important;
}
