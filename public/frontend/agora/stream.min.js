/******/ (function(modules) { // webpackBootstrap
/******/ 	// The module cache
/******/ 	var installedModules = {};
/******/
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/
/******/ 		// Check if module is in cache
/******/ 		if(installedModules[moduleId]) {
/******/ 			return installedModules[moduleId].exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = installedModules[moduleId] = {
/******/ 			i: moduleId,
/******/ 			l: false,
/******/ 			exports: {}
/******/ 		};
/******/
/******/ 		// Execute the module function
/******/ 		modules[moduleId].call(module.exports, module, module.exports, __webpack_require__);
/******/
/******/ 		// Flag the module as loaded
/******/ 		module.l = true;
/******/
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/
/******/
/******/ 	// expose the modules object (__webpack_modules__)
/******/ 	__webpack_require__.m = modules;
/******/
/******/ 	// expose the module cache
/******/ 	__webpack_require__.c = installedModules;
/******/
/******/ 	// define getter function for harmony exports
/******/ 	__webpack_require__.d = function(exports, name, getter) {
/******/ 		if(!__webpack_require__.o(exports, name)) {
/******/ 			Object.defineProperty(exports, name, { enumerable: true, get: getter });
/******/ 		}
/******/ 	};
/******/
/******/ 	// define __esModule on exports
/******/ 	__webpack_require__.r = function(exports) {
/******/ 		if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 			Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 		}
/******/ 		Object.defineProperty(exports, '__esModule', { value: true });
/******/ 	};
/******/
/******/ 	// create a fake namespace object
/******/ 	// mode & 1: value is a module id, require it
/******/ 	// mode & 2: merge all properties of value into the ns
/******/ 	// mode & 4: return value when already ns object
/******/ 	// mode & 8|1: behave like require
/******/ 	__webpack_require__.t = function(value, mode) {
/******/ 		if(mode & 1) value = __webpack_require__(value);
/******/ 		if(mode & 8) return value;
/******/ 		if((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;
/******/ 		var ns = Object.create(null);
/******/ 		__webpack_require__.r(ns);
/******/ 		Object.defineProperty(ns, 'default', { enumerable: true, value: value });
/******/ 		if(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));
/******/ 		return ns;
/******/ 	};
/******/
/******/ 	// getDefaultExport function for compatibility with non-harmony modules
/******/ 	__webpack_require__.n = function(module) {
/******/ 		var getter = module && module.__esModule ?
/******/ 			function getDefault() { return module['default']; } :
/******/ 			function getModuleExports() { return module; };
/******/ 		__webpack_require__.d(getter, 'a', getter);
/******/ 		return getter;
/******/ 	};
/******/
/******/ 	// Object.prototype.hasOwnProperty.call
/******/ 	__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };
/******/
/******/ 	// __webpack_public_path__
/******/ 	__webpack_require__.p = "/";
/******/
/******/
/******/ 	// Load entry module and return exports
/******/ 	return __webpack_require__(__webpack_require__.s = 1);
/******/ })
/************************************************************************/
/******/ ({

/***/ "./node_modules/@babel/runtime/regenerator/index.js":
/*!**********************************************************!*\
  !*** ./node_modules/@babel/runtime/regenerator/index.js ***!
  \**********************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__(/*! regenerator-runtime */ "./node_modules/regenerator-runtime/runtime.js");


/***/ }),

/***/ "./node_modules/regenerator-runtime/runtime.js":
/*!*****************************************************!*\
  !*** ./node_modules/regenerator-runtime/runtime.js ***!
  \*****************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

/**
 * Copyright (c) 2014-present, Facebook, Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

var runtime = (function (exports) {
  "use strict";

  var Op = Object.prototype;
  var hasOwn = Op.hasOwnProperty;
  var undefined; // More compressible than void 0.
  var $Symbol = typeof Symbol === "function" ? Symbol : {};
  var iteratorSymbol = $Symbol.iterator || "@@iterator";
  var asyncIteratorSymbol = $Symbol.asyncIterator || "@@asyncIterator";
  var toStringTagSymbol = $Symbol.toStringTag || "@@toStringTag";

  function define(obj, key, value) {
    Object.defineProperty(obj, key, {
      value: value,
      enumerable: true,
      configurable: true,
      writable: true
    });
    return obj[key];
  }
  try {
    // IE 8 has a broken Object.defineProperty that only works on DOM objects.
    define({}, "");
  } catch (err) {
    define = function(obj, key, value) {
      return obj[key] = value;
    };
  }

  function wrap(innerFn, outerFn, self, tryLocsList) {
    // If outerFn provided and outerFn.prototype is a Generator, then outerFn.prototype instanceof Generator.
    var protoGenerator = outerFn && outerFn.prototype instanceof Generator ? outerFn : Generator;
    var generator = Object.create(protoGenerator.prototype);
    var context = new Context(tryLocsList || []);

    // The ._invoke method unifies the implementations of the .next,
    // .throw, and .return methods.
    generator._invoke = makeInvokeMethod(innerFn, self, context);

    return generator;
  }
  exports.wrap = wrap;

  // Try/catch helper to minimize deoptimizations. Returns a completion
  // record like context.tryEntries[i].completion. This interface could
  // have been (and was previously) designed to take a closure to be
  // invoked without arguments, but in all the cases we care about we
  // already have an existing method we want to call, so there's no need
  // to create a new function object. We can even get away with assuming
  // the method takes exactly one argument, since that happens to be true
  // in every case, so we don't have to touch the arguments object. The
  // only additional allocation required is the completion record, which
  // has a stable shape and so hopefully should be cheap to allocate.
  function tryCatch(fn, obj, arg) {
    try {
      return { type: "normal", arg: fn.call(obj, arg) };
    } catch (err) {
      return { type: "throw", arg: err };
    }
  }

  var GenStateSuspendedStart = "suspendedStart";
  var GenStateSuspendedYield = "suspendedYield";
  var GenStateExecuting = "executing";
  var GenStateCompleted = "completed";

  // Returning this object from the innerFn has the same effect as
  // breaking out of the dispatch switch statement.
  var ContinueSentinel = {};

  // Dummy constructor functions that we use as the .constructor and
  // .constructor.prototype properties for functions that return Generator
  // objects. For full spec compliance, you may wish to configure your
  // minifier not to mangle the names of these two functions.
  function Generator() {}
  function GeneratorFunction() {}
  function GeneratorFunctionPrototype() {}

  // This is a polyfill for %IteratorPrototype% for environments that
  // don't natively support it.
  var IteratorPrototype = {};
  IteratorPrototype[iteratorSymbol] = function () {
    return this;
  };

  var getProto = Object.getPrototypeOf;
  var NativeIteratorPrototype = getProto && getProto(getProto(values([])));
  if (NativeIteratorPrototype &&
      NativeIteratorPrototype !== Op &&
      hasOwn.call(NativeIteratorPrototype, iteratorSymbol)) {
    // This environment has a native %IteratorPrototype%; use it instead
    // of the polyfill.
    IteratorPrototype = NativeIteratorPrototype;
  }

  var Gp = GeneratorFunctionPrototype.prototype =
    Generator.prototype = Object.create(IteratorPrototype);
  GeneratorFunction.prototype = Gp.constructor = GeneratorFunctionPrototype;
  GeneratorFunctionPrototype.constructor = GeneratorFunction;
  GeneratorFunction.displayName = define(
    GeneratorFunctionPrototype,
    toStringTagSymbol,
    "GeneratorFunction"
  );

  // Helper for defining the .next, .throw, and .return methods of the
  // Iterator interface in terms of a single ._invoke method.
  function defineIteratorMethods(prototype) {
    ["next", "throw", "return"].forEach(function(method) {
      define(prototype, method, function(arg) {
        return this._invoke(method, arg);
      });
    });
  }

  exports.isGeneratorFunction = function(genFun) {
    var ctor = typeof genFun === "function" && genFun.constructor;
    return ctor
      ? ctor === GeneratorFunction ||
        // For the native GeneratorFunction constructor, the best we can
        // do is to check its .name property.
        (ctor.displayName || ctor.name) === "GeneratorFunction"
      : false;
  };

  exports.mark = function(genFun) {
    if (Object.setPrototypeOf) {
      Object.setPrototypeOf(genFun, GeneratorFunctionPrototype);
    } else {
      genFun.__proto__ = GeneratorFunctionPrototype;
      define(genFun, toStringTagSymbol, "GeneratorFunction");
    }
    genFun.prototype = Object.create(Gp);
    return genFun;
  };

  // Within the body of any async function, `await x` is transformed to
  // `yield regeneratorRuntime.awrap(x)`, so that the runtime can test
  // `hasOwn.call(value, "__await")` to determine if the yielded value is
  // meant to be awaited.
  exports.awrap = function(arg) {
    return { __await: arg };
  };

  function AsyncIterator(generator, PromiseImpl) {
    function invoke(method, arg, resolve, reject) {
      var record = tryCatch(generator[method], generator, arg);
      if (record.type === "throw") {
        reject(record.arg);
      } else {
        var result = record.arg;
        var value = result.value;
        if (value &&
            typeof value === "object" &&
            hasOwn.call(value, "__await")) {
          return PromiseImpl.resolve(value.__await).then(function(value) {
            invoke("next", value, resolve, reject);
          }, function(err) {
            invoke("throw", err, resolve, reject);
          });
        }

        return PromiseImpl.resolve(value).then(function(unwrapped) {
          // When a yielded Promise is resolved, its final value becomes
          // the .value of the Promise<{value,done}> result for the
          // current iteration.
          result.value = unwrapped;
          resolve(result);
        }, function(error) {
          // If a rejected Promise was yielded, throw the rejection back
          // into the async generator function so it can be handled there.
          return invoke("throw", error, resolve, reject);
        });
      }
    }

    var previousPromise;

    function enqueue(method, arg) {
      function callInvokeWithMethodAndArg() {
        return new PromiseImpl(function(resolve, reject) {
          invoke(method, arg, resolve, reject);
        });
      }

      return previousPromise =
        // If enqueue has been called before, then we want to wait until
        // all previous Promises have been resolved before calling invoke,
        // so that results are always delivered in the correct order. If
        // enqueue has not been called before, then it is important to
        // call invoke immediately, without waiting on a callback to fire,
        // so that the async generator function has the opportunity to do
        // any necessary setup in a predictable way. This predictability
        // is why the Promise constructor synchronously invokes its
        // executor callback, and why async functions synchronously
        // execute code before the first await. Since we implement simple
        // async functions in terms of async generators, it is especially
        // important to get this right, even though it requires care.
        previousPromise ? previousPromise.then(
          callInvokeWithMethodAndArg,
          // Avoid propagating failures to Promises returned by later
          // invocations of the iterator.
          callInvokeWithMethodAndArg
        ) : callInvokeWithMethodAndArg();
    }

    // Define the unified helper method that is used to implement .next,
    // .throw, and .return (see defineIteratorMethods).
    this._invoke = enqueue;
  }

  defineIteratorMethods(AsyncIterator.prototype);
  AsyncIterator.prototype[asyncIteratorSymbol] = function () {
    return this;
  };
  exports.AsyncIterator = AsyncIterator;

  // Note that simple async functions are implemented on top of
  // AsyncIterator objects; they just return a Promise for the value of
  // the final result produced by the iterator.
  exports.async = function(innerFn, outerFn, self, tryLocsList, PromiseImpl) {
    if (PromiseImpl === void 0) PromiseImpl = Promise;

    var iter = new AsyncIterator(
      wrap(innerFn, outerFn, self, tryLocsList),
      PromiseImpl
    );

    return exports.isGeneratorFunction(outerFn)
      ? iter // If outerFn is a generator, return the full iterator.
      : iter.next().then(function(result) {
          return result.done ? result.value : iter.next();
        });
  };

  function makeInvokeMethod(innerFn, self, context) {
    var state = GenStateSuspendedStart;

    return function invoke(method, arg) {
      if (state === GenStateExecuting) {
        throw new Error("Generator is already running");
      }

      if (state === GenStateCompleted) {
        if (method === "throw") {
          throw arg;
        }

        // Be forgiving, per 25.3.3.3.3 of the spec:
        // https://people.mozilla.org/~jorendorff/es6-draft.html#sec-generatorresume
        return doneResult();
      }

      context.method = method;
      context.arg = arg;

      while (true) {
        var delegate = context.delegate;
        if (delegate) {
          var delegateResult = maybeInvokeDelegate(delegate, context);
          if (delegateResult) {
            if (delegateResult === ContinueSentinel) continue;
            return delegateResult;
          }
        }

        if (context.method === "next") {
          // Setting context._sent for legacy support of Babel's
          // function.sent implementation.
          context.sent = context._sent = context.arg;

        } else if (context.method === "throw") {
          if (state === GenStateSuspendedStart) {
            state = GenStateCompleted;
            throw context.arg;
          }

          context.dispatchException(context.arg);

        } else if (context.method === "return") {
          context.abrupt("return", context.arg);
        }

        state = GenStateExecuting;

        var record = tryCatch(innerFn, self, context);
        if (record.type === "normal") {
          // If an exception is thrown from innerFn, we leave state ===
          // GenStateExecuting and loop back for another invocation.
          state = context.done
            ? GenStateCompleted
            : GenStateSuspendedYield;

          if (record.arg === ContinueSentinel) {
            continue;
          }

          return {
            value: record.arg,
            done: context.done
          };

        } else if (record.type === "throw") {
          state = GenStateCompleted;
          // Dispatch the exception by looping back around to the
          // context.dispatchException(context.arg) call above.
          context.method = "throw";
          context.arg = record.arg;
        }
      }
    };
  }

  // Call delegate.iterator[context.method](context.arg) and handle the
  // result, either by returning a { value, done } result from the
  // delegate iterator, or by modifying context.method and context.arg,
  // setting context.delegate to null, and returning the ContinueSentinel.
  function maybeInvokeDelegate(delegate, context) {
    var method = delegate.iterator[context.method];
    if (method === undefined) {
      // A .throw or .return when the delegate iterator has no .throw
      // method always terminates the yield* loop.
      context.delegate = null;

      if (context.method === "throw") {
        // Note: ["return"] must be used for ES3 parsing compatibility.
        if (delegate.iterator["return"]) {
          // If the delegate iterator has a return method, give it a
          // chance to clean up.
          context.method = "return";
          context.arg = undefined;
          maybeInvokeDelegate(delegate, context);

          if (context.method === "throw") {
            // If maybeInvokeDelegate(context) changed context.method from
            // "return" to "throw", let that override the TypeError below.
            return ContinueSentinel;
          }
        }

        context.method = "throw";
        context.arg = new TypeError(
          "The iterator does not provide a 'throw' method");
      }

      return ContinueSentinel;
    }

    var record = tryCatch(method, delegate.iterator, context.arg);

    if (record.type === "throw") {
      context.method = "throw";
      context.arg = record.arg;
      context.delegate = null;
      return ContinueSentinel;
    }

    var info = record.arg;

    if (! info) {
      context.method = "throw";
      context.arg = new TypeError("iterator result is not an object");
      context.delegate = null;
      return ContinueSentinel;
    }

    if (info.done) {
      // Assign the result of the finished delegate to the temporary
      // variable specified by delegate.resultName (see delegateYield).
      context[delegate.resultName] = info.value;

      // Resume execution at the desired location (see delegateYield).
      context.next = delegate.nextLoc;

      // If context.method was "throw" but the delegate handled the
      // exception, let the outer generator proceed normally. If
      // context.method was "next", forget context.arg since it has been
      // "consumed" by the delegate iterator. If context.method was
      // "return", allow the original .return call to continue in the
      // outer generator.
      if (context.method !== "return") {
        context.method = "next";
        context.arg = undefined;
      }

    } else {
      // Re-yield the result returned by the delegate method.
      return info;
    }

    // The delegate iterator is finished, so forget it and continue with
    // the outer generator.
    context.delegate = null;
    return ContinueSentinel;
  }

  // Define Generator.prototype.{next,throw,return} in terms of the
  // unified ._invoke helper method.
  defineIteratorMethods(Gp);

  define(Gp, toStringTagSymbol, "Generator");

  // A Generator should always return itself as the iterator object when the
  // @@iterator function is called on it. Some browsers' implementations of the
  // iterator prototype chain incorrectly implement this, causing the Generator
  // object to not be returned from this call. This ensures that doesn't happen.
  // See https://github.com/facebook/regenerator/issues/274 for more details.
  Gp[iteratorSymbol] = function() {
    return this;
  };

  Gp.toString = function() {
    return "[object Generator]";
  };

  function pushTryEntry(locs) {
    var entry = { tryLoc: locs[0] };

    if (1 in locs) {
      entry.catchLoc = locs[1];
    }

    if (2 in locs) {
      entry.finallyLoc = locs[2];
      entry.afterLoc = locs[3];
    }

    this.tryEntries.push(entry);
  }

  function resetTryEntry(entry) {
    var record = entry.completion || {};
    record.type = "normal";
    delete record.arg;
    entry.completion = record;
  }

  function Context(tryLocsList) {
    // The root entry object (effectively a try statement without a catch
    // or a finally block) gives us a place to store values thrown from
    // locations where there is no enclosing try statement.
    this.tryEntries = [{ tryLoc: "root" }];
    tryLocsList.forEach(pushTryEntry, this);
    this.reset(true);
  }

  exports.keys = function(object) {
    var keys = [];
    for (var key in object) {
      keys.push(key);
    }
    keys.reverse();

    // Rather than returning an object with a next method, we keep
    // things simple and return the next function itself.
    return function next() {
      while (keys.length) {
        var key = keys.pop();
        if (key in object) {
          next.value = key;
          next.done = false;
          return next;
        }
      }

      // To avoid creating an additional object, we just hang the .value
      // and .done properties off the next function object itself. This
      // also ensures that the minifier will not anonymize the function.
      next.done = true;
      return next;
    };
  };

  function values(iterable) {
    if (iterable) {
      var iteratorMethod = iterable[iteratorSymbol];
      if (iteratorMethod) {
        return iteratorMethod.call(iterable);
      }

      if (typeof iterable.next === "function") {
        return iterable;
      }

      if (!isNaN(iterable.length)) {
        var i = -1, next = function next() {
          while (++i < iterable.length) {
            if (hasOwn.call(iterable, i)) {
              next.value = iterable[i];
              next.done = false;
              return next;
            }
          }

          next.value = undefined;
          next.done = true;

          return next;
        };

        return next.next = next;
      }
    }

    // Return an iterator with no values.
    return { next: doneResult };
  }
  exports.values = values;

  function doneResult() {
    return { value: undefined, done: true };
  }

  Context.prototype = {
    constructor: Context,

    reset: function(skipTempReset) {
      this.prev = 0;
      this.next = 0;
      // Resetting context._sent for legacy support of Babel's
      // function.sent implementation.
      this.sent = this._sent = undefined;
      this.done = false;
      this.delegate = null;

      this.method = "next";
      this.arg = undefined;

      this.tryEntries.forEach(resetTryEntry);

      if (!skipTempReset) {
        for (var name in this) {
          // Not sure about the optimal order of these conditions:
          if (name.charAt(0) === "t" &&
              hasOwn.call(this, name) &&
              !isNaN(+name.slice(1))) {
            this[name] = undefined;
          }
        }
      }
    },

    stop: function() {
      this.done = true;

      var rootEntry = this.tryEntries[0];
      var rootRecord = rootEntry.completion;
      if (rootRecord.type === "throw") {
        throw rootRecord.arg;
      }

      return this.rval;
    },

    dispatchException: function(exception) {
      if (this.done) {
        throw exception;
      }

      var context = this;
      function handle(loc, caught) {
        record.type = "throw";
        record.arg = exception;
        context.next = loc;

        if (caught) {
          // If the dispatched exception was caught by a catch block,
          // then let that catch block handle the exception normally.
          context.method = "next";
          context.arg = undefined;
        }

        return !! caught;
      }

      for (var i = this.tryEntries.length - 1; i >= 0; --i) {
        var entry = this.tryEntries[i];
        var record = entry.completion;

        if (entry.tryLoc === "root") {
          // Exception thrown outside of any try block that could handle
          // it, so set the completion value of the entire function to
          // throw the exception.
          return handle("end");
        }

        if (entry.tryLoc <= this.prev) {
          var hasCatch = hasOwn.call(entry, "catchLoc");
          var hasFinally = hasOwn.call(entry, "finallyLoc");

          if (hasCatch && hasFinally) {
            if (this.prev < entry.catchLoc) {
              return handle(entry.catchLoc, true);
            } else if (this.prev < entry.finallyLoc) {
              return handle(entry.finallyLoc);
            }

          } else if (hasCatch) {
            if (this.prev < entry.catchLoc) {
              return handle(entry.catchLoc, true);
            }

          } else if (hasFinally) {
            if (this.prev < entry.finallyLoc) {
              return handle(entry.finallyLoc);
            }

          } else {
            throw new Error("try statement without catch or finally");
          }
        }
      }
    },

    abrupt: function(type, arg) {
      for (var i = this.tryEntries.length - 1; i >= 0; --i) {
        var entry = this.tryEntries[i];
        if (entry.tryLoc <= this.prev &&
            hasOwn.call(entry, "finallyLoc") &&
            this.prev < entry.finallyLoc) {
          var finallyEntry = entry;
          break;
        }
      }

      if (finallyEntry &&
          (type === "break" ||
           type === "continue") &&
          finallyEntry.tryLoc <= arg &&
          arg <= finallyEntry.finallyLoc) {
        // Ignore the finally entry if control is not jumping to a
        // location outside the try/catch block.
        finallyEntry = null;
      }

      var record = finallyEntry ? finallyEntry.completion : {};
      record.type = type;
      record.arg = arg;

      if (finallyEntry) {
        this.method = "next";
        this.next = finallyEntry.finallyLoc;
        return ContinueSentinel;
      }

      return this.complete(record);
    },

    complete: function(record, afterLoc) {
      if (record.type === "throw") {
        throw record.arg;
      }

      if (record.type === "break" ||
          record.type === "continue") {
        this.next = record.arg;
      } else if (record.type === "return") {
        this.rval = this.arg = record.arg;
        this.method = "return";
        this.next = "end";
      } else if (record.type === "normal" && afterLoc) {
        this.next = afterLoc;
      }

      return ContinueSentinel;
    },

    finish: function(finallyLoc) {
      for (var i = this.tryEntries.length - 1; i >= 0; --i) {
        var entry = this.tryEntries[i];
        if (entry.finallyLoc === finallyLoc) {
          this.complete(entry.completion, entry.afterLoc);
          resetTryEntry(entry);
          return ContinueSentinel;
        }
      }
    },

    "catch": function(tryLoc) {
      for (var i = this.tryEntries.length - 1; i >= 0; --i) {
        var entry = this.tryEntries[i];
        if (entry.tryLoc === tryLoc) {
          var record = entry.completion;
          if (record.type === "throw") {
            var thrown = record.arg;
            resetTryEntry(entry);
          }
          return thrown;
        }
      }

      // The context.catch method must only be called with a location
      // argument that corresponds to a known catch block.
      throw new Error("illegal catch attempt");
    },

    delegateYield: function(iterable, resultName, nextLoc) {
      this.delegate = {
        iterator: values(iterable),
        resultName: resultName,
        nextLoc: nextLoc
      };

      if (this.method === "next") {
        // Deliberately forget the last sent value so that we don't
        // accidentally pass it on to the delegate.
        this.arg = undefined;
      }

      return ContinueSentinel;
    }
  };

  // Regardless of whether this script is executing as a CommonJS module
  // or not, return the runtime object so that we can declare the variable
  // regeneratorRuntime in the outer scope, which allows this module to be
  // injected easily by `bin/regenerator --include-runtime script.js`.
  return exports;

}(
  // If this script is executing as a CommonJS module, use module.exports
  // as the regeneratorRuntime namespace. Otherwise create a new empty
  // object. Either way, the resulting object will be used to initialize
  // the regeneratorRuntime variable at the top of this file.
   true ? module.exports : undefined
));

try {
  regeneratorRuntime = runtime;
} catch (accidentalStrictMode) {
  // This module should not be running in strict mode, so the above
  // assignment should always work unless something is misconfigured. Just
  // in case runtime.js accidentally runs in strict mode, we can escape
  // strict mode using a global Function call. This could conceivably fail
  // if a Content Security Policy forbids using Function, but in that case
  // the proper solution is to fix the accidental strict mode problem. If
  // you've misconfigured your bundler to force strict mode and applied a
  // CSP to forbid Function, and you're not willing to fix either of those
  // problems, please detail your unique predicament in a GitHub issue.
  Function("r", "regeneratorRuntime = r")(runtime);
}


/***/ }),

/***/ "./resources/js/parts/agora/stream.js":
/*!********************************************!*\
  !*** ./resources/js/parts/agora/stream.js ***!
  \********************************************/
/*! no exports provided */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/regenerator */ "./node_modules/@babel/runtime/regenerator/index.js");
/* harmony import */ var _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_0__);


function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }

function _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }

function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }

function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }

function _iterableToArrayLimit(arr, i) { if (typeof Symbol === "undefined" || !(Symbol.iterator in Object(arr))) return; var _arr = []; var _n = true; var _d = false; var _e = undefined; try { for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i["return"] != null) _i["return"](); } finally { if (_d) throw _e; } } return _arr; }

function _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }

function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }

function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }

(function ($) {
  "use strict";

  var liveEndedHtml = "<div class=\"no-result default-no-result d-flex align-items-center justify-content-center flex-column w-100 h-100\">\n        <div class=\"no-result-logo\">\n            <img src=\"/assets/default/img/no-results/support.png\" alt=\"\">\n        </div>\n        <div class=\"d-flex align-items-center flex-column mt-30 text-center\">\n            <h2 class=\"text-dark-blue\">".concat(liveEndedLang, "</h2>\n            <p class=\"mt-5 text-center text-gray font-weight-500\">").concat(redirectToPanelInAFewMomentLang, "</p>\n        </div>\n    </div>");
  var featherIconsConf = {
    width: 20,
    height: 20
  };
  var maximizeIcon = feather.icons['maximize-2'].toSvg(featherIconsConf); // create Agora client

  var client = AgoraRTC.createClient({
    mode: "live",
    codec: "vp8"
  });
  var localTracks = {
    videoTrack: null,
    audioTrack: null,
    screenAudioTrack: null,
    screenVideoTrack: null,
    shareScreenActived: false
  };
  var remoteUsers = {}; // Agora client options

  var options = {
    appid: appId,
    channel: channelName,
    uid: null,
    token: rtcToken,
    role: 'host',
    //streamRole, // host or audience
    audienceLatency: 2
  };
  var $remoteStreamPlayerEl = $('#remote-stream-player');
  var $shareScreenButton = $('#shareScreen');

  function handleJoinOrCreateStream() {
    return _handleJoinOrCreateStream.apply(this, arguments);
  }

  function _handleJoinOrCreateStream() {
    _handleJoinOrCreateStream = _asyncToGenerator( /*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_0___default.a.mark(function _callee() {
      var playerHtml, player, startAt;
      return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_0___default.a.wrap(function _callee$(_context) {
        while (1) {
          switch (_context.prev = _context.next) {
            case 0:
              _context.prev = 0;

              if (streamRole === "audience") {
                client.on("user-published", handleUserPublished);
                client.on("user-unpublished", handleUserUnpublished);
                client.on("user-left", handleHostEndLive);
                client.on("user-joined", handlePeerOnline);
              }

              client.setClientRole(options.role); // join the channel

              _context.next = 5;
              return client.join(options.appid, options.channel, options.token || null, authUserId);

            case 5:
              options.uid = _context.sent;

              if (!(streamRole === "host" || sessionStreamType === 'multiple')) {
                _context.next = 30;
                break;
              }

              _context.next = 9;
              return AgoraRTC.createMicrophoneAudioTrack();

            case 9:
              localTracks.audioTrack = _context.sent;
              _context.next = 12;
              return AgoraRTC.createCameraVideoTrack();

            case 12:
              localTracks.videoTrack = _context.sent;

              if (!(streamRole === "audience")) {
                _context.next = 22;
                break;
              }

              _context.next = 16;
              return getRemoteUserCardHtml(authUserId);

            case 16:
              playerHtml = _context.sent;
              player = $(playerHtml);
              console.log("16");
              $("#remote-player-".concat(authUserId)).remove();
              $remoteStreamPlayerEl.append(player);
              localTracks.videoTrack.play("remote-player-".concat(authUserId));
              _context.next = 23;
              break;

            case 22:
              localTracks.videoTrack.play("stream-player");

            case 23:
              _context.next = 25;
              return client.publish([localTracks.videoTrack, localTracks.audioTrack]);

            case 25:
              if (streamRole === "host") {
                client.on("user-published", handleUserPublished);
                client.on("user-unpublished", handleUserUnpublished);
                client.on("user-joined", handlePeerOnline);
                client.on("user-left", handleAudienceLeft);
              }

              startAt = streamStartAt && streamStartAt > 0 ? new Date().getTime() / 1000 - streamStartAt : 0;
              handleTimer(startAt);
              console.log("publish success");
              $(".agora-stream-loading").addClass('d-none');

            case 30:
              _context.next = 35;
              break;

            case 32:
              _context.prev = 32;
              _context.t0 = _context["catch"](0);
              console.error(_context.t0);

            case 35:
            case "end":
              return _context.stop();
          }
        }
      }, _callee, null, [[0, 32]]);
    }));
    return _handleJoinOrCreateStream.apply(this, arguments);
  }

  handleJoinOrCreateStream();

  function handlePeerOnline(evt) {
    /*onsole.log('#################### Online')
    console.log(evt)*/
  }

  window.getUserInfoCache = {};

  window.getUserInfo = function (uid) {
    return new Promise(function (resolve, reject) {
      if (getUserInfoCache && typeof getUserInfoCache[uid] !== "undefined") {
        resolve(getUserInfoCache[uid]);
      } else {
        $.get(getUserIfoRoute+'?id='+uid, function (result) {
          if (result && result.user) {
            getUserInfoCache[uid] = result.user;
            resolve(result.user);
          } else {
            reject(null);
          }
        });
      }
    });
  };

  function subscribe(_x, _x2) {
    return _subscribe.apply(this, arguments);
  }

  function _subscribe() {
    _subscribe = _asyncToGenerator( /*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_0___default.a.mark(function _callee2(user, mediaType) {
      var uid, playerHtml, player, startAt;
      return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_0___default.a.wrap(function _callee2$(_context2) {
        while (1) {
          switch (_context2.prev = _context2.next) {
            case 0:
              uid = user.uid; // subscribe to a remote user

              _context2.next = 3;
              return client.subscribe(user, mediaType);

            case 3:
              console.log("subscribe success");

              if (!(mediaType === 'video')) {
                _context2.next = 15;
                break;
              }

              if (!(uid === hostUserId)) {
                _context2.next = 9;
                break;
              }

              user.videoTrack.play("stream-player");
              _context2.next = 15;
              break;

            case 9:
              _context2.next = 11;
              return getRemoteUserCardHtml(uid);

            case 11:
              playerHtml = _context2.sent;
              player = $(playerHtml);
              console.log("11");
              $("#remote-player-".concat(uid)).remove();
              $remoteStreamPlayerEl.append(player);
              user.videoTrack.play("remote-player-".concat(uid));

            case 15:
              if (mediaType === 'audio') {
                user.audioTrack.play();
              }

              $(".agora-stream-loading").addClass('d-none');
              $("#notStartedAlert").removeClass('d-flex').addClass('d-none');
              startAt = streamStartAt && streamStartAt > 0 ? new Date().getTime() / 1000 - streamStartAt : 0;
              handleTimer(startAt);

            case 20:
            case "end":
              return _context2.stop();
          }
        }
      }, _callee2);
    }));
    return _subscribe.apply(this, arguments);
  }

  function leave() {
    return _leave.apply(this, arguments);
  }

  function _leave() {
    _leave = _asyncToGenerator( /*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_0___default.a.mark(function _callee3() {
      var trackName, track;
      return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_0___default.a.wrap(function _callee3$(_context3) {
        while (1) {
          switch (_context3.prev = _context3.next) {
            case 0:
              for (trackName in localTracks) {
                track = localTracks[trackName];

                if (track) {
                  track.stop();
                  track.close();
                  localTracks[trackName] = undefined;
                }
              } // remove remote users and player views
              // leave the channel


              _context3.next = 3;
              return client.leave();

            case 3:
              if (redirectAfterLeave) {
                window.location = redirectAfterLeave;
              }

              console.log("client leaves channel success");

            case 5:
            case "end":
              return _context3.stop();
          }
        }
      }, _callee3);
    }));
    return _leave.apply(this, arguments);
  }

  function handleUserPublished(user, mediaType) {
    var id = user.uid;
    remoteUsers[id] = user;
    subscribe(user, mediaType);
  }

  function handleUserUnpublished(user, mediaType) {
    if (mediaType === 'video') {
      var id = user.uid;
      delete remoteUsers[id];
      $("#player-wrapper-".concat(id)).html('');
    }
  }

  function handleHostEndLive(user, mediaType) {
    var id = user.uid;
    $("#player-wrapper-".concat(id)).html(liveEndedHtml);
    setTimeout(function () {
      if (redirectAfterLeave) {
        window.location = redirectAfterLeave;
      }
    }, 5000);
  }

  function handleAudienceLeft(user, mediaType) {
    var id = user.uid;
    $("#remote-player-".concat(id)).remove();
  }

  function handleShareScreen() {
    return _handleShareScreen.apply(this, arguments);
  }

  function _handleShareScreen() {
    _handleShareScreen = _asyncToGenerator( /*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_0___default.a.mark(function _callee4() {
      var screenTrack, _yield$Promise$all, _yield$Promise$all2;

      return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_0___default.a.wrap(function _callee4$(_context4) {
        while (1) {
          switch (_context4.prev = _context4.next) {
            case 0:
              if (localTracks.shareScreenActived) {
                _context4.next = 15;
                break;
              }

              _context4.next = 3;
              return Promise.all([AgoraRTC.createScreenVideoTrack({
                encoderConfig: {
                  framerate: 30,
                  height: 720,
                  width: 1280
                }
              }, "auto")]);

            case 3:
              _yield$Promise$all = _context4.sent;
              _yield$Promise$all2 = _slicedToArray(_yield$Promise$all, 1);
              screenTrack = _yield$Promise$all2[0];

              if (screenTrack instanceof Array) {
                localTracks.screenVideoTrack = screenTrack[0];
                localTracks.screenAudioTrack = screenTrack[1];
              } else {
                localTracks.screenVideoTrack = screenTrack;
              } // play local video track


              if (!localTracks.screenVideoTrack) {
                _context4.next = 15;
                break;
              }

              localTracks.screenVideoTrack.play("stream-player"); // publish local tracks to channel

              _context4.next = 11;
              return handleCameraEffect(true);

            case 11:
              _context4.next = 13;
              return client.publish([localTracks.screenVideoTrack, localTracks.audioTrack]);

            case 13:
              localTracks.shareScreenActived = true;
              localTracks.screenVideoTrack.on("track-ended", function () {
                client.unpublish(localTracks.screenVideoTrack).then(function () {
                  localTracks.screenVideoTrack.stop();
                  localTracks.screenVideoTrack.close();
                  localTracks.shareScreenActived = false;
                  handleCameraEffect(false);
                });
              });

            case 15:
            case "end":
              return _context4.stop();
          }
        }
      }, _callee4);
    }));
    return _handleShareScreen.apply(this, arguments);
  }

  function handleEndShareScreen() {
    return _handleEndShareScreen.apply(this, arguments);
  }

  function _handleEndShareScreen() {
    _handleEndShareScreen = _asyncToGenerator( /*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_0___default.a.mark(function _callee5() {
      return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_0___default.a.wrap(function _callee5$(_context5) {
        while (1) {
          switch (_context5.prev = _context5.next) {
            case 0:
              if (localTracks.shareScreenActived) {
                client.unpublish(localTracks.screenVideoTrack).then(function () {
                  localTracks.screenVideoTrack.stop();
                  localTracks.screenVideoTrack.close();
                  localTracks.shareScreenActived = false;
                  handleCameraEffect(false);
                });
              }

            case 1:
            case "end":
              return _context5.stop();
          }
        }
      }, _callee5);
    }));
    return _handleEndShareScreen.apply(this, arguments);
  }

  $('body').on('click', '#leave', function (e) {
    var $this = $(this);
    var sessionId = $this.attr('data-id');
    $this.addClass('loadingbar primary').prop('disabled', true);
    leave();
  });
  $('body').on('click', '#shareScreen', function (e) {
    handleShareScreen();
    $(this).removeClass('d-flex').addClass('d-none');
    $('#endShareScreen').removeClass('d-none').addClass('d-flex');
  });
  $('body').on('click', '#endShareScreen', function (e) {
    handleEndShareScreen();
    $(this).removeClass('d-flex').addClass('d-none');
    $('#shareScreen').removeClass('d-none').addClass('d-flex');
  });
  $('body').on('click', '#microphoneEffect', function (e) {
    var $this = $(this);
    var icon = feather.icons['mic'].toSvg(featherIconsConf);

    if (localTracks.audioTrack) {
      if ($this.hasClass('active')) {
        $this.removeClass('active');
        $this.addClass('disabled');
        icon = feather.icons['mic-off'].toSvg(featherIconsConf);
        client.unpublish(localTracks.audioTrack);
      } else {
        $this.addClass('active');
        $this.removeClass('disabled');
        client.publish(localTracks.audioTrack);
      }
    }

    $this.find('.icon').html(icon);
  });
  $('body').on('click', '#cameraEffect', function (e) {
    var $this = $(this);

    if (!localTracks.shareScreenActived) {
      handleCameraEffect($this.hasClass('active'));
    }
  });

  function handleCameraEffect() {
    return _handleCameraEffect.apply(this, arguments);
  }

  function _handleCameraEffect() {
    _handleCameraEffect = _asyncToGenerator( /*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_0___default.a.mark(function _callee6() {
      var isActive,
          $button,
          icon,
          playerHtml,
          player,
          _args6 = arguments;
      return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_0___default.a.wrap(function _callee6$(_context6) {
        while (1) {
          switch (_context6.prev = _context6.next) {
            case 0:
              isActive = _args6.length > 0 && _args6[0] !== undefined ? _args6[0] : false;
              $button = $('#cameraEffect');
              icon = feather.icons['video'].toSvg(featherIconsConf);
              
              if (!isActive) {
                _context6.next = 10;
                break;
              }

              $button.removeClass('active');
              $button.addClass('disabled');
              icon = feather.icons['video-off'].toSvg(featherIconsConf);
              
              if (localTracks.videoTrack) {
                $(document).find('#video_'+(localTracks.videoTrack._ID)).closest('.remote-stream').remove()
                localTracks.videoTrack.stop();
                localTracks.videoTrack.close();
                client.unpublish(localTracks.videoTrack);
              }

              _context6.next = 26;
              break;

            case 10:
              $button.addClass('active');
              $button.removeClass('disabled');
              _context6.next = 14;
              return AgoraRTC.createCameraVideoTrack();

            case 14:
              localTracks.videoTrack = _context6.sent;

              if (!(authUserId === hostUserId)) {
                _context6.next = 19;
                break;
              }

              localTracks.videoTrack.play("stream-player");
              _context6.next = 25;
              break;

            case 19:
              _context6.next = 21;
              return getRemoteUserCardHtml(authUserId);

            case 21:
              playerHtml = _context6.sent;
              player = $(playerHtml);
              $("#remote-player-".concat(authUserId)).remove();
              $remoteStreamPlayerEl.append(player);
              localTracks.videoTrack.play("remote-player-".concat(authUserId));

            case 25:
              client.publish(localTracks.videoTrack);

            case 26:
              $button.find('.icon').html(icon);

            case 27:
            case "end":
              return _context6.stop();
          }
        }
      }, _callee6);
    }));
    return _handleCameraEffect.apply(this, arguments);
  }

  function getRemoteUserCardHtml(_x3) {
    return _getRemoteUserCardHtml.apply(this, arguments);
  }

  function _getRemoteUserCardHtml() {
    _getRemoteUserCardHtml = _asyncToGenerator( /*#__PURE__*/_babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_0___default.a.mark(function _callee7(uid) {
      var userInfo;
      return _babel_runtime_regenerator__WEBPACK_IMPORTED_MODULE_0___default.a.wrap(function _callee7$(_context7) {
        while (1) {
          switch (_context7.prev = _context7.next) {
            case 0:
              _context7.next = 2;
              return getUserInfo(uid);

            case 2:
              userInfo = _context7.sent;
              return _context7.abrupt("return", "<div id=\"remote-player-".concat(uid, "\" class=\"remote-stream\">\n                    <span class=\"remote-stream-fullscreen\">").concat(maximizeIcon, "</span>\n                    ").concat(userInfo ? "<span class=\"remote-stream-user-info\">".concat(userInfo.name, "</span>") : '', "\n                </div>"));

            case 4:
            case "end":
              return _context7.stop();
          }
        }
      }, _callee7);
    }));
    return _getRemoteUserCardHtml.apply(this, arguments);
  }

  function handleTimer() {
    var startAt = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;
    var streamTimer = $('#streamTimer');
    var hoursLabel = streamTimer.find('.hours');
    var minutesLabel = streamTimer.find('.minutes');
    var secondsLabel = streamTimer.find('.seconds');
    var totalSeconds = startAt;
    setInterval(setTime, 1000);

    function setTime() {
      ++totalSeconds;
      var seconds = pad(Math.floor(totalSeconds % 60));
      var minutes = pad(Math.floor(totalSeconds / 60 % 60));
      var hours = pad(Math.floor(totalSeconds / (60 * 60) % 24));
      hoursLabel.html(hours);
      minutesLabel.html(minutes);
      secondsLabel.html(seconds);
    }

    function pad(val) {
      var valString = val + "";

      if (valString.length < 2) {
        return "0" + valString;
      } else {
        return valString;
      }
    }
  }

  $('body').on('click', '#collapseBtn', function () {
    var $tabs = $('.agora-tabs');
    $tabs.toggleClass('show');
  });
  $('body').on('click', '.remote-stream-fullscreen', function () {
    var $parent = $(this).closest('.remote-stream');
    $parent.toggleClass('is-fullscreen');
    $remoteStreamPlayerEl.toggleClass('is-fullscreen');
  });
  $('body').on('click', '#handleUsersJoin', function (e) {
    var $this = $(this);
    var notActive = $this.hasClass('dont-join-users');

    if (notActive) {
      $this.find('span').text(joinIsActiveLang);
    } else {
      $this.find('span').text(joiningIsDisabledLang);
    }

    $this.toggleClass('dont-join-users');
    $this.prop('disabled', true);
    $.get("/panel/sessions/".concat(sessionId, "/toggleUsersJoinToAgora"), function (result) {
      if (result) {
        $.toast({
          heading: result.heading,
          text: result.text,
          bgColor: result.icon === 'error' ? '#f63c3c' : '#43d477',
          textColor: 'white',
          hideAfter: 10000,
          position: 'bottom-right',
          icon: result.icon
        });
      }

      $this.prop('disabled', false);
    });
  });
})(jQuery);

/***/ }),

/***/ 1:
/*!**************************************************!*\
  !*** multi ./resources/js/parts/agora/stream.js ***!
  \**************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

module.exports = __webpack_require__(/*! /home/<USER>/projects/webinar/resources/js/parts/agora/stream.js */"./resources/js/parts/agora/stream.js");


/***/ })

/******/ });