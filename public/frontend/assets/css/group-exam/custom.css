p {
    margin-bottom: 0;
}

a {
    text-decoration: none;
}

.exam-page {
    overflow-y: hidden;
}

header {
    margin: 8px auto;
    background-color: #ffffff;
    border-radius: 10px;
    padding: 10px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.header-right {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
}

.header-right .exam-info {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 16px;
    color: #666;
    margin-bottom: 10px;
}

.header-middle {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 4px;
}

.answer-sheet-col {
    max-width: 32.5%;
    padding: 12px;
    background-color: #ffffff;
    border-radius: 8px;
    box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 12px;
}

.answer-sheet-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 14px;
}

.question {
    flex-basis: 48%;
    gap: 10px;
}

.options {
    justify-content: space-between;
}

.mcq-questions-area {
    max-height: 87vh;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.answer-sheet-col {
    max-height: 85vh;
    overflow-y: auto;
}

.mcq-questions-area::-webkit-scrollbar,
.answer-sheet-col::-webkit-scrollbar {
    display: none;
}

.mcq-questions-area,
.answer-sheet-col {
    scrollbar-width: none;
}

.mcq-question {
    margin-bottom: 0;
}

.question-number {
    margin-right: 0;
}

.exam-icon {
    text-transform: capitalize;
}

.offcanvas.offcanvas-bottom {
    height: 100%;
    max-height: 90%;
    overflow-y: auto;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
}

.title {
    font-size: 24px;
    font-weight: bold;
}

.progress-container {
    display: flex;
    justify-content: space-between;
}

.progress-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 18px;
}

.progress-item i {
    font-size: 18px;
}

.button-container {
    display: flex;
    justify-content: flex-end;
}

.button {
    padding: 10px 20px;
    font-size: 16px;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.finish-button {
    background-color: #4CAF50;
}

.finish-button:hover {
    background-color: #45a049;
}

.omr-button {
    display: none;
    background-color: #2196F3;
    margin-right: 10px;
}

.omr-button:hover {
    background-color: #1e87db;
}

.the-question p {
    font-size: 16px;
    margin-left: 5px;
    text-align: left;
    word-break: break-word;
    overflow-wrap: break-word;
    white-space: normal;
}

.the-question p span{
    white-space: normal;
    word-break: auto-phrase;
}

.arrow-icon-parent i {
    transform: rotate(45deg);
}

.exam-illustration {
    width: 100px;
    height: 100px;
}

.exam-btns {
    flex-wrap: wrap;
}

.exam-feature-card-row {
    padding-bottom: 20px;
}

.exam-feature-card-row a {
    display: block;
}

.exam-mcq-and-omr .offcanvas {
    max-width: 55%;
    margin: 0 auto;
}

.exam-mcq-and-omr .offcanvas-body {
    padding-top: 0;
}

.exam-mcq-and-omr .offcanvas-body .mt-3 {
    margin-top: 0 !important;
}

.omr .question {
    justify-content: center;
}

.correct-ans-page-options {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
}

.correct-ans-page-options label{
    margin-bottom: 0;
    flex-basis: 20%;
    flex-grow: 1;
}

.ans-btns {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
}

/* Exam Center Icon */
.exam-feature-card-row{
    align-items: stretch !important;
}

.exam-feature-card.no-btn{
    height: 100%;
    display: flex;
    align-items: center;
    height: 130px;
    /* justify-content: center; */
}

/* .exam-feature-card.no-btn .exam-feature-title {
    flex-direction: column;
    gap: 10px;
}

.exam-feature-card.no-btn .feature-icon{
    margin-right: 0 !important;
} */

.exam-feature-card.no-btn p{
    display: none;
}

.exam-feature-title{
    margin-bottom: 0 !important;
}

p.option-label {
    display: flex;
    width: 23px;
    height: 23px;
    border: 1px solid #111;
    line-height: 0;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    padding-top: 2px;
}

input.live-exam-input[type="radio"]:checked+p{
    font-weight: 900;
    color: #000;
}

.mcq-option input[type="radio"] {
    position: relative;
    height: 33px;
    width: 25px;
}

.mcq-option input[type="radio"]::before {
    position: absolute;
    content: attr(data-letter);
    font-size: 16px;
    color: black;
    text-align: center;
    top: 5px;
    left: 7px;
}

.mcq-option{
    position: relative;
}

input#searchCourse::placeholder {
    color: #fff;
}

.form-control:focus{
    box-shadow: none;
}

.correctanswer-body{
    overflow: hidden;
}

@media screen and (max-width: 991px) {
    .exam-feature-card.no-btn {
        height: 117px;
    }

    .exam-feature-card h2 {
        font-size: 20px !important;
    }
}

@media screen and (max-width: 767px) {
    .exam-feature-card h2 {
        font-size: 24px !important;
    }
}

/* @media (max-width: 575px) {
    .exam-feature-card.no-btn {
        justify-content: center;
    }

    .exam-feature-title {
        margin-bottom: 0px !important;
    }
} */

/* Styling for the animated clock icon */
.left-time {
    font-size: 50px;
    color: #3498db;
    animation: spin 3s infinite linear;
}

/* Footer */
.footer-area ul{
    padding-left: 0;
}

/* CSS keyframes for rotation animation */
@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}
