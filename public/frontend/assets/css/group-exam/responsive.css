@media (min-width: 992px) and (max-width: 1399px) {
    .answer-sheet-wrapper {
        gap: 8px;
    }

    .question-number,
    .option {
        width: 18px;
        height: 18px;
    }

    .question {
        width: 48%;
        flex-basis: auto;
        padding: 8px;
    }

    .options {
        display: flex;
        gap: 6px;
    }

    .relative {
        max-width: 1140px;
    }
}

@media (min-width: 992px) and (max-width: 1199px) {
    .next-self-exam-card {
        padding: 16px;
        height: 190px;
    }

    .exam-icon,
    .rapid-exam-title {
        margin-bottom: 5px;
    }

    .rapid-exam-subtitle {
        margin-bottom: 10px;
    }

    .exam-btn-secondary {
        margin-left: 0;
    }

    .relative {
        max-width: 960px;
    }
}

@media (max-width: 991px) {
    .answer-sheet-col {
        display: none;
    }

    .mcq-question p {
        font-size: 22px !important;
        line-height: 28px;
        word-break: break-all;
    }

    .title-flex-parent {
        margin-top: 0;
    }

    .exam-type-card h4 {
        font-size: 19px;
        margin-bottom: 0;
    }

    .start-exam-btn-parent {
        margin-top: 12px;
    }

    .start-exam-btn {
        padding: 7px 18px;
    }

    .next-self-exam-card {
        padding: 16px;
        height: 190px;
    }

    .exam-icon,
    .rapid-exam-title {
        margin-bottom: 5px;
    }

    .rapid-exam-subtitle {
        margin-bottom: 10px;
    }

    .exam-btn-secondary {
        margin-left: 0;
    }

    .exam-illustration {
        width: 60px;
        height: auto;
    }

    .exam-feature-card a {
        padding: 3px 7px;
    }

    .exam-feature-card h2 {
        font-size: 18px !important;
    }

    .exam-feature-card.no-btn p {
        margin-bottom: 0 !important;
    }

    .row.hero-area-row {
        align-items: center;
    }

    .ec-btn.logout {
        display: block !important;
    }

    .menu-notification-btn,
    .menu-user-btn {
        display: none !important;
    }

    .header-nav-right-side .navbar-nav {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 15px;
    }

    .header-nav-right-side .navbar-nav .ec-btn {
        width: 100%;
        margin-right: 0 !important;
    }

    .header-nav-right-side .navbar-nav .ec-btn a {
        display: block;
        text-align: center;
    }

    /* .header-nav-right-side .navbar-nav .ec-btn.logout a {
        background-color: #fff;
        border-color: #fff;
        color: #5e3fd7 !important;
    }

    .header-nav-right-side .navbar-nav .ec-btn.logout a:hover {
        background-color: transparent;
        color: #fff !important;
    } */

    .relative {
        max-width: 720px;
    }
}

@media (max-width: 767px) {
    .button {
        padding: 5px 13px;
        font-size: 14px;
    }

    .hero-area-row{
        margin-top: 0 !important;
    }

    .exam-type-card {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .start-exam-btn-parent {
        margin-top: 0;
    }

    .next-self-exam-card {
        height: auto;
    }

    .exam-illustration {
        width: 80px;
    }

    .xm-btns a {
        padding: 8px 12px;
    }

    ul.nav.nav-pills.exam-tab-parent {
        width: 100%;
    }

    .exam-mcq-and-omr .offcanvas {
        max-width: 75%;
    }

    .live-exam-parent-wrapper {
        display: none;
    }

    .exam-center-header {
        padding-top: 0;
        padding-bottom: 0;
        background-color: transparent;
        margin-bottom: 16px;
        flex-wrap: wrap;
        flex-direction: row !important;
    }

    ul.exam-center-page-tab {
        gap: 12px;
    }

    .exam-tab-parent .nav-link {
        padding: 0.5rem 14px;
    }

    .relative {
        max-width: 540px;
    }

    h1.result-sheet-title {
        position: static;
    }

    .exam-feature-card h2 {
        font-size: 24px !important;
    }

    .correct-ans-page-options {
        flex-direction: column;
    }

    .katex-display {
        text-align: left !important;
    }

    #result-info-section .my-3{
        margin-top: -10px !important;
    }

    /* Correct Answer Page */
    .correctanswer-body .question-section {
        padding-bottom: 40px;
    }

    /* MCQ Page */
    .mcq-questions-area{
        padding-bottom: 60px;
    }
}

@media (max-width: 575px) {
    .header {
        flex-direction: column;
        align-items: flex-start;
    }

    .exam-info {
        margin-top: 10px;
    }

    .progress-container {
        margin-top: -25px;
        width: 100%;
        flex-direction: column;
    }

    .progress-item {
        font-size: 15px;
    }

    .button-container {
        justify-content: space-between;
    }

    .omr-button {
        display: inline-block;
    }

    .exam-mcq-and-omr .align-items-end {
        flex-direction: column;
        justify-content: center !important;
        align-items: center !important;
    }

    .exam-icon {
        font-size: 24px;
        line-height: 30px;
        text-align: center;
        margin-bottom: 4px;
    }

    .header-middle {
        justify-content: flex-end;
    }

    .exam-info {
        display: none !important;
    }

    .header-right {
        flex-direction: row;
        align-items: center;
        justify-content: center;
    }

    .title,
    .progress-item,
    .header-middle {
        justify-content: center;
    }

    .progress-item {
        margin-left: -90px;
    }

    .header-middle {
        margin-left: 50px;
        margin-top: -24px;
    }

    .omr-button {
        margin-right: 0;
    }

    .mcq-question {
        margin: 5px 0;
        padding: 12px;
    }

    .mcq-question p {
        font-size: 18px !important;
        line-height: 26px;
    }


    .mcq-option {
        margin: 7px 0;
    }

    header {
        position: sticky;
        top: 0;
    }

    .button {
        padding: 2px 5px;
        font-size: 13px;
    }

    .marked-row {
        margin-top: 20px;
        margin-bottom: -25px;
    }

    h1.result-sheet-title {
        font-size: 24px;
        margin-bottom: 10px;
    }

    .back-button {
        width: 55px;
        height: 40px;
    }

    .container.exam-result-container {
        padding-left: 0;
        padding-right: 0;
    }

    .score-value {
        font-size: 17px;
    }

    .result-card,
    .score-card,
    .grade-item {
        text-align: center;
    }

    .the-question p {
        font-size: 15px;
    }

    .next-self-exam-card .exam-icon {
        display: none;
    }

    .exam-btn-secondary {
        margin-left: 0;
    }

    .exam-type-card h4 {
        margin-bottom: 0px;
    }

    .exam-type-card {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .start-exam-btn-parent {
        margin-top: 0;
    }

    .exam-type-card h4 {
        margin-bottom: 0px;
    }

    .start-exam-btn {
        padding: 7px 20px;
    }

    .xm-btns a {
        padding: 4px 6px;
    }

    .exam-feature-card svg {
        width: 18px;
    }

    .exam-feature-card .feature-icon {
        width: 30px !important;
        height: 30px !important;
        margin-right: 5px !important;
    }

    .exam-feature-card h2 {
        font-size: 17px !important;
    }

    .exam-feature-title {
        margin-bottom: 5px !important;
    }

    .exam-feature-card.no-btn p {
        margin-bottom: 0px !important;
    }

    ul.nav.nav-pills.exam-tab-parent {
        align-items: center;
    }

    ul.nav.nav-pills.exam-tab-parent li button.nav-link {
        font-size: 11px;
    }

    .exam-mcq-and-omr .offcanvas {
        max-width: 100%;
    }

    ul.nav.nav-pills.exam-tab-parent li {
        border: 1px solid #ddd;
        border-radius: 5px;
    }

    ul.nav.nav-pills.exam-tab-parent li:last-child{
        margin-right: 0;
    }

    .exam-center-header {
        gap: 2px;
    }

    .exam-btn-primary,
    .exam-btn-secondary {
        padding: 5px 15px;
        font-size: 13px;
    }

    .party-popper img{
        width: 50%;
    }

    .party-popper-right {
        top: 63%;
        display: flex;
        justify-content: flex-end;
    }

    .party-popper-left {
        top: 88%;
    }

    .correct-ans-page-options label {
        flex-basis: 35%;
    }

    .correct-ans-page-options label {
        flex-basis: 100%;
    }

    .exam-feature-card h2 {
        font-size: 21px !important;
    }

    div#exam-data-section {
        margin-top: 16px;
    }

    ul.exam-center-page-tab {
        gap: 4px;
    }
}

@media (min-width: 431px) and (max-width: 480px) {
    .exam-feature-card.no-btn {
        height: 70px;
    }

    .gap {
        gap: 8px;
    }
}

@media (max-width: 430px) {
    .exam-icon {
        font-size: 18px;
        line-height: 24px;
    }

    .answer-sheet-wrapper {
        gap: 8px;
    }

    .question-number,
    .option {
        width: 18px;
        height: 18px;
    }

    .question {
        width: 48%;
        flex-basis: auto;
        padding: 8px;
    }

    .options {
        display: flex;
        gap: 6px;
    }

    .exam-icon {
        margin-bottom: 5px;
    }

    .exam-icon i,
    .progress-item i,
    .header-middle i {
        font-size: 13px;
    }

    .progress-item {
        margin-left: -100px;
    }

    .header-middle {
        margin-left: 30px;
    }

    .stats-card {
        padding: 16px;
    }

    .stats-card h6 {
        font-size: 16px;
    }

    button.nav-link.result-sheet-button,
    button.nav-link.result-sheet-button.active {
        padding: 5px 6px;
    }

    button.nav-link.result-sheet-button{
        font-size: 12px;
    }

    .back-button {
        padding: 0 !important;
        width: 30px;
        height: 30px;
    }

    button.back-button i {
        font-size: 14px;
    }

    .title-flex-parent {
        gap: 10px;
    }

    .the-question p {
        font-size: 14px;
    }

    .exam-illustration {
        width: 60px;
        height: auto;
    }

    .next-self-exam-card {
        padding: 12px;
    }

    .rapid-exam-title {
        font-size: 22px;
        margin-bottom: 5px;
    }

    .rapid-exam-subtitle {
        line-height: 16px;
        font-size: 16px;
        margin-bottom: 10px;
    }

    .exam-btn-primary,
    .exam-btn-secondary {
        padding: 5px 6px;
    }

    .exam-type-card {
        padding: 12px;
    }

    .xm-btns {
        flex-direction: column;
        gap: 5px !important;
    }

    .start-exam-btn {
        padding: 8px;
        font-size: 13px;
        line-height: 13px;
    }

    .arrow-icon-parent {
        height: 30px;
        width: 30px;
    }

    .exam-type-card h4 {
        font-size: 15px;
    }

    .xm-btns a {
        text-align: center;
    }

    .exam-feature-card h2 {
        font-size: 16px !important;
    }

    .exam-feature-card.no-btn {
        justify-content: center;
    }

    .exam-feature-card.no-btn .exam-feature-title{
        flex-direction: column;
        gap: 8px;
    }

    .exam-feature-card.no-btn .exam-feature-title .feature-icon{
        margin-right: 0 !important;
    }

    .exam-feature-card.no-btn h2 {
        text-align: center;
        font-size: 18px !important;
    }

    .exam-feature-card.no-btn .xm-center-icon {
        width: 40px;
    }

    ul.nav.nav-pills.exam-tab-parent {
        flex-wrap: nowrap;
    }

    ul.nav.nav-pills.exam-tab-parent li button.nav-link {
        padding-left: 0;
        width: 100%;
        text-align: center;
        font-size: 13px;
        line-height: 16px;
    }

    ul.nav.nav-pills.exam-tab-parent li:last-child button.nav-link {
        margin-right: 0px;
    }

    .exam-center-header button {
        padding: 0 !important;
        width: 35px;
        height: 35px;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .exam-feature-card.no-btn {
        height: 90px;
    }

    .xm-center-icon {
        width: 30px;
    }

    .question-single{
        padding: 12px !important;
    }

    .correct-ans-page-options label div{
        padding: 10px !important;
    }

    .correct-ans-page-options label div span{
        margin-bottom: -2px !important;
    }

    .exam-center-header ul li{
        text-align: center;
    }

    .gap {
        gap: 4px;
    }

    .exam-feature-card.no-btn .exam-feature-title.gap{
        gap: 12px;
        margin-bottom: 0 !important;
    }

    .result-sheet-tab-parent li,
    .result-sheet-tab-parent li button{
        border-radius: 4px !important;
    }

    ul.nav.nav-pills.exam-tab-parent li button {
        padding-left: 2px !important;
        padding-right: 2px !important;
    }

    .has-blinking-dot{
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 3px;
    }

    ul.nav.nav-pills.exam-tab-parent li:nth-child(2){
        flex-basis: 88px;
    }

    .blinking-dot {
        flex-basis: 12px;
        width: 0px;
        margin-right: 0px;
    }

    #result-info-section h5{
        font-size: 22px;
    }

    /* Exam Center page gap removing */
    .exam-center-parent {
        padding-top: 90px;
    }

    .exam-center-header {
        margin-bottom: 10px;
    }

    div#exam-data-section {
        margin-top: 10px;
    }

    .next-self-exam-card {
        margin-bottom: 10px;
    }

    .exam-feature-card-row{
        margin-top: -10px;
    }

    .exam-feature-card-row>*{
        margin-top: 10px;
        padding-left: 5px;
        padding-right: 5px;
    }

    .container.exam-center-container {
        padding-left: 0px;
        padding-right: 0px;
    }

    .form-control.search{
        padding: 0 .75rem;
        height: 37px;
        font-size: 12px;
    }

    .back-and-search-wrapper {
        margin-top: 0;
        margin-bottom: 0;
    }

    .exam-center-header.gap-3 {
        gap: 10px !important;
    }

    .date-picker input.date{
        position: relative;
        z-index: 2;
        opacity: 0;
    }

    .icon-calendar-mobile {
        display: block !important;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        left: 0;
        font-size: 36px;
        z-index: 1;
        color: #335B47;
    }

    .search-area {
        margin-left: 0;
        flex-grow: 1;
    }

    .search-form button {
        height: 37px;
        background-color: #335B47;
        border-color: #335B47;
        color: #fff;
    }

    .search-form {
        flex-grow: 1;
    }

    .search-area {
        margin-right: -12px;
    }
}

@media (max-width: 364px){
    .exam-btn-primary, .exam-btn-secondary {
        padding: 5px 15px;
        font-size: 12px;
    }

    .rapid-exam-title {
        font-size: 19px;
    }

    .correct-ans-page-options label {
        flex-basis: 100%;
    }

    ul.nav.nav-pills.exam-tab-parent li button.nav-link {
        font-size: 11px;
    }

    .search-area {
        flex-grow: 1;
    }

    .search-area .date-picker,
    .search-form{
        width: 100%;
    }

    .date-picker {
        flex-basis: 1%;
    }

    .form-control.date {
        width: 26px;
        height: 100%;
        color: #335B47;
        font-size: 22px;
    }

    .back-and-search-wrapper.gap-3{
        gap: 10px !important;
    }

    .search-form {
        border-radius: 6px;
    }

    .search-form input{
        margin-right: 0 !important;
    }

    .search-area {
        margin-right: 6px;
    }

    @media screen and (-webkit-min-device-pixel-ratio:0) {
        .form-control.date {
            color: #335B47;
        }
    }
}

@media (max-width: 329px) {

    .start-exam-btn {
        padding: 4px;
        font-size: 12px;
        line-height: 13px;
    }

    .arrow-icon-parent {
        height: 20px;
        width: 20px;
    }

    .arrow-icon-parent i {
        font-size: 12px;
    }

    .exam-type-card h4 {
        font-size: 14px;
    }

    .result-sheet-tab-parent {
        gap: 1px;
    }
}
