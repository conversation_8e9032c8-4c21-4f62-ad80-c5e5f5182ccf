    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    body {
        font-family: 'Arial', sans-serif;
        background-color: #f8f9fa;
        color: #333;
    }

    .exam-center {
        max-width: 900px;
        margin: 40px auto;
        padding: 20px;
    }

    .back-button {
        width: 50px;
        height: 44px;
    }

    h1 {
        font-size: 32px;
        text-align: center;
        margin-bottom: 20px;
    }

    .exam-filter {
        display: flex;
        justify-content: center;
        margin-bottom: 30px;
    }

    .exam-filter button {
        background-color: #f3f3f3;
        border: none;
        padding: 10px 20px;
        margin: 0 5px;
        cursor: pointer;
        border-radius: 30px;
        font-size: 1rem;
        transition: background-color 0.3s;
    }

    .exam-filter .btn {
        padding: 10px 20px;
        margin-right: 10px;
        border: none;
        background-color: #f0f0f0;
        color: #333;
        text-decoration: none;
        border-radius: 5px;
        font-size: 14px;
    }

    .exam-filter .btn.active {
        background: var(--gradient-banner-bg);
        color: #fff;
    }

    .exam-filter .btn:hover {
        background: var(--gradient-banner-bg);
        color: #fff;
    }


    .exam-card {
        background-color: #fffde5;
        border: 1px solid #e0e0e0;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }

    .exam-info h2 {
        color: #2e7d32;
        font-size: 1.4rem;
        margin-bottom: 5px;
    }

    .exam-info p {
        color: #757575;
        font-size: 1rem;
    }

    .live-tag {
        background-color: #d32f2f;
        color: white;
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.9rem;
    }

    .live-tag.previous {
        background-color: #065FD4;
    }

    .live-tag.upcoming {
        background-color: #335B47;
    }

    .card-body {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .exam-details {
        display: flex;
        flex-wrap: wrap;
    }

    .exam-detail {
        margin-right: 15px;
        margin-bottom: 10px;
        font-size: 0.95rem;
    }

    .exam-detail span:first-child {
        font-weight: bold;
    }

    .start-exam {
        background-color: #2e7d32;
        color: white;
        border: none;
        padding: 10px 20px;
        cursor: pointer;
        border-radius: 8px;
        font-size: 1rem;
        transition: background-color 0.3s;
    }

    .start-exam:hover {
        background-color: #1b5e20;
        color: white;
    }

    .start-exam:active {
        background-color: #145214;
    }


    .exam-center-header {
        /* background-color: #f8f9fa; */
        padding: 12px;
        border-radius: 8px;
    }

    .exam-center-header h2 {
        font-size: 40px;
        font-weight: 700;
        color: black;
    }

    .exam-center-tabs {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-top: 15px;
    }

    .exam-center-tab {
        background-color: #e9ecef;
        padding: 5px 15px;
        border-radius: 20px;
        font-size: 0.9rem;
    }

    .exam-center-tab.active {
        background-color: #ffc107;
    }

    .self-exam-card {
        background-color: #335B47;
        color: white;
        padding: 40px 48px;
        border-radius: 10px;
        margin-bottom: 70px;
    }

    .self-exam-card h3 {
        color: #E7F760;
        font-size: 32px;
        line-height: 40px;
        font-weight: 600;
        margin-top: 20px;
        margin-bottom: 20px;
    }

    .self-exam-card img {
        height: 24px;
        width: 24px;
    }

    .self-exam-icons {
        border-radius: 50%;
        background-color: #FDFEEF;
        height: 50px;
        width: 50px;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .self-exam-card button {
        padding: 12px 25px;
        background-color: #d4e157;
    }

    .self-exam-card p {
        color: #EBEFED;
        font-size: 16px;
        line-height: 22px;
        font-weight: 600;
        margin-bottom: 20px;
    }

    .exam-type-card {
        background-color: #335B47;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 15px;
        transition: all ease 0.3s;
        height: 100%;
    }

    /* .exam-type-card:hover {
        background-color: #335B47;
    } */

    /* .exam-type-card:hover .start-exam-btn-parent {
        margin-left: 40%;
    } */

    .exam-type-card .start-exam-btn {
        background-color: #d4e157;
        color: #1A1A1A;
    }

    .exam-type-card.active-exam .start-exam-btn,
    .exam-type-card.active-exam .arrow-icon-parent{
        background-color: #fff;
    }

    .exam-type-card .arrow-icon-parent {
        background-color: #d4e157;
        color: #1A1A1A;
    }

    /* .exam-type-card:hover h4 {
        color: #ddd !important;
        margin-left: 30%;
    } */


    .exam-type-card h4 {
        font-size: 24px;
        font-weight: 700;
        line-height: 24px;
        color: #ddd;
        margin-bottom: 35px;
        transition: all ease 0.3s;
    }

    .exam-feature-icon {
        background-color: #f8f9fa;
        padding: 15px 25px;
        border: 1px solid #EAEAEA;
        border-radius: 8px;
        margin-bottom: 15px;
        transition: all ease 0.3s;
        height: 100%;
    }

    .exam-feature-icon:hover {
        background-color: #d4e157;
    }

    .exam-feature-icons-parent {
        margin-bottom: 15px;
        background-color: #EBEFED;
        height: 40px;
        width: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 50%;
    }

    .exam-feature-icon p {
        color: #484848;
        font-weight: 600;
        font-size: 24px;
        line-height: 25px;
        padding-bottom: 10px;
    }

    .exam-feature-icon small {
        color: #335B47;
        font-size: 16px;
        font-weight: 600;
        line-height: 22px;
    }

    .exam-package-card {
        background-color: #d4e157;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 15px;
        transition: all ease 0.3s;
    }

    .start-exam-btn {
        background-color: #2c6e49;
        color: white;
        border: none;
        padding: 7px 35px;
        border-radius: 20px;
    }

    .active {
        border: none !important;
    }

    button.exam-active-btn.nav-link.active {
        background-color: #030060 !important;
        border: 1px solid #ddd !important;
        color: black;
    }

    button.exam-active-btn.nav-link {
        color: black;
    }

    ul.nav.nav-pills.exam-tab-parent {
        border-radius: 7px;
    }

    .arrow-icon-parent {
        height: 37px;
        width: 37px;
        background-color: #335B47;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 50%;
    }

    .start-exam-btn-parent {
        display: flex;
        align-items: center;
        margin-top: 40px;
        transition: all ease 0.5s;
        gap: 4px;
    }

    .live-exam-parent-wrapper {
        margin-bottom: 15px;
    }

    h2.exam-pakage-title {
        font-size: 48px;
        font-weight: 600;
        color: #1A1A1A;
        margin-bottom: 35px;
    }

    .pakage-parent {
        display: flex;
        align-items: center;
        margin-top: 40px;
        transition: all ease 0.3s;
    }

    .exam-package-card h5 {
        color: #335B47;
        font-size: 18px;
        font-weight: 600;
        transition: all ease 0.3s;
    }

    .exam-package-card:hover {
        background-color: #335B47;
    }

    .exam-package-card:hover h5 {
        color: #E7F760;
    }

    .exam-package-card:hover p {
        color: #ddd;
    }

    .exam-package-card:hover .start-exam-btn {
        background-color: #d4e157;
        color: #1A1A1A;
    }

    .exam-package-card:hover .arrow-icon-parent {
        background-color: #d4e157;
        color: #1A1A1A;
    }

    .exam-package-card:hover .pakage-parent {
        margin-left: 20%;
    }

    .exam-package-card p {
        color: #1A1A1A;
        font-size: 24px;
        font-weight: 700;
        margin-top: 5px;
        transition: all ease 0.3s;
    }


    .popular-exam-card {
        border: 1px solid #e0e0e0;
        border-radius: 10px;
        overflow: hidden;
        transition: box-shadow 0.3s ease;
        padding: 15px;
    }

    .popular-exam-card:hover {
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .popular-exam-image {
        width: 100%;
        height: 200px;
        object-fit: cover;
    }

    .popular-exam-content {
        padding: 15px 0px;
    }

    .popular-exam-title {
        font-size: 1.1rem;
        font-weight: bold;
        margin-bottom: 5px;
        color: #1A1A1A;
    }

    .popular-exam-author {
        font-size: 0.9rem;
        font-weight: 400;
        color: #606060;
        display: flex;
        align-items: center;
        margin-bottom: 5px;
        gap: 20px;
    }

    .popular-exam-author img {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 5px;
    }

    .popular-exam-stats {
        font-size: 12px;
        color: #606060;
        font-weight: 400;
    }

    h2.mb-4.popular-title {
        color: #1A1A1A;
        font-size: 48px;
        font-weight: 600;
        /* margin-top: 60px; */
    }

    .row.popular-parent {
        margin-bottom: 50px;
    }


    .bup-card {
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 25px 15px;
        margin-bottom: 15px;
        background-color: #fcfcef;
        box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
    }

    .bup-card-title {
        color: #335B47;
        margin-bottom: 5px;
        font-size: 25px;
        font-weight: 700;
    }

    .bup-card-subtitle {
        color: #335B47;
        font-size: 13px;
        margin-bottom: 15px;
        font-weight: 600;
        border-bottom: 1px solid #335B47;
        padding-bottom: 10px;
    }

    .bup-card-info {
        display: flex;
        justify-content: space-between;
        margin-bottom: 20px;
        font-size: 0.85rem;
        color: #34495e;
    }

    .bup-start-btn {
        background-color: #335B47;
        border: none;
        color: white;
        padding: 10px 76px;
        border-radius: 6px;
        cursor: pointer;
        transition: background-color 0.3s;
        margin-top: 30px;
    }

    /* .bup-start-btn:hover {
            background-color: #34495e;
        } */
    .bup-previous-badge-red {
        background-color: #E43350;
        color: white;
        padding: 2px 8px;
        border-radius: 4px;
        font-size: 0.75rem;
        float: right;
        font-weight: 700;
    }

    .bup-previous-badge {
        background-color: #065FD4;
        color: white;
        padding: 2px 8px;
        border-radius: 4px;
        font-size: 0.75rem;
        float: right;
        font-weight: 700;
    }

    .bup-previous-badge-green {
        background-color: #335B47;
        color: white;
        padding: 2px 8px;
        border-radius: 4px;
        font-size: 0.75rem;
        float: right;
        font-weight: 700;
    }

    .exam-center-parent {
        padding-top: 95px;
    }

    header.page-banner-header.gradient-bg.position-relative {
        display: none;
    }

    .bup-card-info span {
        color: #335B47;
        font-size: 15px;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .bup-card-info img {
        height: 20px;
        width: 20px;
    }


    .mcq-container {
        max-width: 1320px;
        margin: 0 auto;
        padding: 20px;
        /* margin-top: 10px; */
    }

    .main-exam-questions-header {
        font-size: 30px;
        margin-bottom: 0px;
        font-weight: 600;
    }

    .mcq-header {
        display: flex;
        justify-content: space-between;
        margin-bottom: 20px;
        color: #333;
        font-size: 14px;
        padding-bottom: 12px;
        border-bottom: 1px solid #b0b0b0;
    }

    .mcq-questions-section {
        background-color: #fafdfc;
        padding: 7px;
        border-radius: 8px;
        margin-bottom: 20px;
        /* box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px; */
        height: 100vh;
        overflow: hidden;
        overflow-y: auto;
        width: 100%;
    }

    .mcq-question {
        background-color: #fafdfc;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 7px;
        box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
    }

    /* .mcq-question {
        margin-bottom: 25px;
    } */

    .mcq-options {
        list-style: none;
        padding-left: 20px;
    }

    .question-type-title {
        color: #484848;
        font-size: 18px;
        font-weight: 600;
    }

    .exam-duration-title {
        color: #484848;
        font-size: 18px;
        font-weight: 600;
    }

    .mcq-option {
        margin: 10px 0;
        display: flex;
        align-items: center;
        font-size: 15px;
        font-weight: 400;
        color: #484848;
        gap: 6px;
    }

    .mcq-option input[type="radio"] {
        /* margin-right: 10px; */
        height: 35px;
        width: 25px;
    }

    .mcq-answer-grid {
        display: grid;
        grid-template-columns: repeat(5, 1fr);
        gap: 10px;
        margin: 20px 0;
    }

    .mcq-answer-box {
        background-color: #fff7f7;
        border: 1px solid #ffcdd2;
        border-radius: 4px;
        padding: 8px;
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .mcq-answer-number {
        /* margin-bottom: 5px; */
        font-weight: bold;
    }

    .mcq-option-circles {
        display: flex;
        justify-content: space-around;
        gap: 17PX;
    }

    .mcq-circle {
        width: 24px;
        height: 24px;
        border: 1px solid #ccc;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 12px;
    }

    .mcq-circle:hover {
        background-color: #f0f0f0;
    }

    .mcq-finish-btn {
        background-color: red;
        color: white;
        padding: 13px 60px;
        border: none;
        border-radius: 9px;
        cursor: pointer;
        font-size: 20px;
        font-weight: 600;
        margin-right: auto;
    }

    .mcq-time-btn {
        background-color: #335B47;
        color: white;
        padding: 13px 64px;
        border: none;
        border-radius: 9px;
        cursor: pointer;
        font-size: 20px;
        font-weight: 600;
        margin-right: auto;
    }

    .wrapper-finishing {
        display: flex;
        gap: 10px;
        align-items: center;
        flex-direction: column;
    }



    p.mcq-footer-text span {
        color: #E43350;
    }

    /* .mcq-finish-btn:hover {
        background-color: #34495e;
    } */


    /* POPUP csss */

    .trigger {
        text-align: center;
        padding: 13px 64px;
        background: #3e3e3e;
        color: #fff;
        font-size: 15px;
        outline: none;
        border: none;
        border-radius: 5px;
        font-family: cursive;
        cursor: pointer;
        display: none;
        font-size: 20px;
        font-weight: 600;
        margin-right: auto;
    }

    .modal {
        position: fixed;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        opacity: 0;
        visibility: hidden;
        transform: scale(1.1);
        transition: visibility 0s linear 0.25s, opacity 0.25s 0s, transform 0.25s;
    }

    .omr-exam-modal-inner.modal-content {
        position: absolute;
        top: 59%;
        left: 50%;
        transform: translate(-50%, -50%);
        background-color: white;
        padding: 40px 20px;
        width: 100%;
        /* max-width: 48%; */
        border-radius: 0.5rem;
        height: 80%;
    }

    .close-button {
        float: right;
        width: 40px;
        height: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 30px;
        background-color: #99c9d7;
        border-radius: 10px;
        color: #ffffff;
    }

    .close-button:hover {
        background-color: darkgray;
    }

    .show-modal {
        opacity: 1;
        visibility: visible;
        transform: scale(1.0);
        transition: visibility 0s linear 0s, opacity 0.25s 0s, transform 0.25s;
    }

    .mcq-question p {
        color: #484848;
        font-size: 19px;
    }

    .question-wrapper-title {
        font-weight: bold;
    }

    .main-flex-row {
        display: flex;
        gap: 10px;
    }

    .question-wrapper-parent {
        width: 100%;
        max-width: 60%;
    }

    .answer-sheet {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        padding: 20px;
        box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 12px;
        gap: 10px;
    }

    .main-wrapper-sheet {

        height: 100vh;
        background-color: white;
        /* border: 1px solid #ccc; */
        border-radius: 10px;
        width: 50%;
        /* max-width: 40%; */
        overflow: hidden;
        overflow-y: auto;
    }

    .mobile-answer-sheet {
        background-color: white;
        /* border: 1px solid #ccc; */
        padding: 20px;
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
        border-radius: 10px;
        width: 100%;
        /* max-width: 40%;.mcq-footer */
        height: 98%;
        overflow: hidden;
        overflow-y: auto;
    }

    .question {
        display: flex;
        align-items: center;
        border: 1px solid #ffcccc;
        border-radius: 5px;
        padding: 10px;
        background-color: #fff0f0;
    }

    .question-number {
        background-color: #ff9999;
        color: white;
        border-radius: 50%;
        width: 25px;
        height: 25px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 10px;
        font-size: 12px;
    }

    .options {
        display: flex;
        gap: 10px;
    }

    .option {
        border: 1px solid #ffcccc;
        border-radius: 50%;
        width: 25px;
        height: 25px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 12px;
        color: black;
    }











    /* my attended exam css code here */
    .exam-dashboard {
        max-width: 1320px;
        margin: 20px auto;
        padding: 0 15px;
        padding-top: 140px;
    }

    .exam-title {
        font-size: 45px;
        font-weight: 600;
        margin-bottom: 30px;
        color: #1A1A1A;
        text-align: left;
    }

    .attended-exam-card {
        background-color: #fff9f3;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 15px;
        border: 1px solid #9C8960;
        position: relative;
        box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
    }

    .exam-header {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
    }

    .exam-score-circle {
        width: 85px;
        height: 85px;
        border-radius: 50%;
        border: 3px solid #244132;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        background-color: white;
        gap: 3px;
    }

    .line-dive {
        width: 30px;
        height: 2px;
        background-color: #9eb8ab;
    }

    .exam-score {
        font-size: 20px;
        font-weight: bold;
        line-height: 1;
    }

    .exam-total {
        font-size: 12px;
        color: black;
        text-align: center;
        font-weight: 600;
    }

    .exam-name {
        font-size: 30px;
        font-weight: 600;
        color: #335B47;
        margin-bottom: 5px;
    }

    .exam-position {
        color: #335B47;
        font-size: 15px;
        font-weight: 600;
    }

    .exam-position strong {
        font-size: 25px;
        font-weight: 600;
    }

    .exam-stats {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 10px;
    }

    .exam-stat-box {
        padding: 25px 24px;
        border-radius: 8px;
        /* text-align: center; */
    }

    .exam-stat-value {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 5px;
    }

    .exam-stat-label {
        font-size: 20px;
        color: #484848;
        font-weight: 600;
        margin-bottom: 10px;
    }

    .exam-total-box {
        background-color: #e8e4ff;
    }

    .exam-not-answered-box {
        background-color: #fff3cd;
    }

    .exam-correct-box {
        background-color: #d1f8d1;
    }

    .exam-wrong-box {
        background-color: #ffd1d1;
    }

    .exam-details-btn {
        position: absolute;
        top: 20px;
        right: 20px;
        background-color: #2c5640;
        color: #E7F760;
        padding: 6px 14px;
        border-radius: 4px;
        font-size: 16px;
        font-weight: 400;
        text-decoration: none;
        display: flex;
        align-items: center;
        gap: 7px;
    }

    .exam-details-btn img {
        height: 24px;
    }

    .row.marked-row {
        margin-top: 20px;
        margin-bottom: -20px;
    }

    .mcq-question-wrapper-inner {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }

    span.question-cpunt-number {
        color: #484848;
        font-size: 18px;
        font-weight: 600;
    }

    .answer-count-number {
        color: #484848;
        font-size: 18px;
        font-weight: 600;
    }

    .revision-count {
        color: #484848;
        font-size: 18px;
        font-weight: 600;
    }

    .parent-result {
        padding-top: 100px;
    }

    .title-flex-parent {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 12px;
        margin-bottom: 10px;
    }

    button.nav-link.result-sheet-button.active {
        background-color: #E7F760;
        color: black;
        padding: 10px 25px;
    }

    .button.nav-link.result-sheet-button .nav-link {
        padding: 10px 25px;
        color: black !important;
    }

    ul.nav.result-sheet-tab-parent.nav-pills.mb-3 {
        background-color: #EBEFED;
        border-radius: 10px;
    }


















    .exam-completed-card {
        background-color: #1e3a2e;
        color: white;
        border-radius: 10px;
        padding: 7px 1px;
        position: relative;
        overflow: hidden;
        margin-bottom: 8px;
    }

    .exam-completed-card h2 {
        color: white;
        font-weight: bold;
    }

    .exam-score-circle-result {
        width: 140px;
        height: 140px;
        border-radius: 50%;
        border: 5px solid #ffd700;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        font-weight: bold;
        margin: 0 auto;
    }

    .line-exam {
        height: 2px;
        width: 40px;
        background-color: #ffd700;
    }

    .exam-score-circle-result span {
        color: #ffd700;
    }

    .exam-score-circle-result span:last-child {
        font-size: 14px;
    }

    .party-popper {
        position: absolute;
        font-size: 40px;
    }

    .party-popper-left {
        left: 4px;
        top: 66%;
        transform: translateY(-50%);
    }

    .party-popper-right {
        right: 10px;
        top: 30%;
        transform: translateY(-50%) scaleX(-1);
        transform: rotateX(0deg);
    }

    .stats-card {
        border-radius: 10px;
        padding: 20px;
        box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
    }

    .stats-card p {
        font-size: 20px;
        font-weight: 700;
        margin-top: 30px;
    }

    .total-questions {
        background-color: #e6e6fa;
    }

    .not-answered {
        background-color: #e6e6fa;
    }

    .correct-answer {
        background-color: #e6e6fa;
    }

    .wrong-answer {
        background-color: #e6e6fa;
    }

    .stats-card h6 {
        color: #484848;
        font-size: 20px;
        font-weight: 600;
    }

    .exam-result-wrapper {
        background-color: #F8F2E4;
        padding: 1rem;
        border-radius: 8px;
        /* max-width: 800px; */
        margin: 2rem auto;
    }

    .exam-title-bengali {
        color: #2d584d;
        text-align: center;
        margin-bottom: 2rem;
        font-size: 1.5rem;
    }

    .result-card {
        background-color: #F3E7CB;
        padding: 1rem;
        border-radius: 6px;
        /* margin-bottom: 1rem; */
    }

    .result-label {
        color: #484848;
        font-size: 15px;
        margin-bottom: 0.3rem;
        font-weight: 400;
    }

    .result-value {
        color: #484848;
        font-size: 26px;
        font-weight: 600;
        margin: 0;
    }

    .result-value.merit {
        color: #2d584d;
    }

    .merit-fraction {
        color: #666;
        font-size: 1rem;
    }

    .btn-merit {
        background-color: #2d584d;
        border: none;
        padding: 0.5rem 1.5rem;
        color: white;
    }

    .btn-merit:hover {
        background-color: #234439;
        color: white;
    }

    .btn-answer {
        background-color: #0d6efd;
        border: none;
        padding: 0.5rem 1.5rem;
        color: white;
    }

    .btn-answer:hover {
        background-color: #0b5ed7;
        color: white;
    }


    .score-report-wrapper {
        margin-top: 10px;
        border-top: 1px solid black;
        padding-top: 10px;
    }


    .score-title {
        color: #335B47;
        text-align: center;
        margin-bottom: 1rem;
        font-size: 1.75rem;
        font-weight: 600;
    }

    .score-card {
        background-color: #F3E7CB;
        padding: 1.2rem;
        border-radius: 6px;
        margin-bottom: 1rem;
        height: 100%;
    }

    .score-label {
        color: #484848;
        font-size: 0.875rem;
        margin-bottom: 0.5rem;
    }

    .score-value {
        color: #484848;
        font-size: 22px;
        font-weight: 600;
        margin-top: 20px;
        margin: 0;
    }

    div#pills-home {
        background-color: transparent;
    }

    h1.result-sheet-title {
        text-align: left;
        font-size: 32px;
        font-weight: 600;
        color: #1A1A1A;
    }


    .score-value.fail {
        color: #484848;
    }

    .score-value.success {
        color: #2d584d;
    }

    .grades-section {
        background-color: #F3E7CB;
        padding: 1rem;
        border-radius: 6px;
        margin: 1.5rem 0;
    }

    .grades-title {
        color: #666;
        font-size: 0.875rem;
        margin-bottom: 1rem;
    }

    .grades-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 1rem;
    }

    .grade-item {
        background-color: #F3E7CB;
        padding: 1rem;
        border-radius: 6px;
        border: 1px solid #9C8A60;
    }

    .grade-label {
        color: #666;
        font-size: 0.875rem;
        margin-bottom: 0.5rem;
    }

    .grade-value {
        color: #2d584d;
        font-size: 1.25rem;
        font-weight: 500;
        margin: 0;
    }


    /* .ranking-list-wrapper {
        max-width: 800px;
        margin: 2rem auto;
        padding: 1rem;
    } */

    .user-list-item {
        background-color: #E8E8E8;
        padding: 0.75rem 1.25rem;
        border-radius: 8px;
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        transition: all 0.2s ease;
    }

    .user-list-item:hover {
        background-color: #f1f3f5;
    }

    .user-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #e9ecef;
        margin-right: 1rem;
        overflow: hidden;
        flex-shrink: 0;
    }

    .user-avatar img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .user-default-avatar {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #dee2e6;
        color: #6c757d;
    }

    .user-name {
        font-size: 17px;
        font-weight: 600;
        color: #484848;
        margin: 0;
        flex-grow: 1;
    }

    .user-score {
        margin: 0 15rem;
        font-size: 1rem;
        color: #495057;
        flex-shrink: 0;
    }


    .ranking-list-wrapper {
        margin-bottom: 50px;
    }

    .user-rank {
        font-size: 1rem;
        font-weight: 600;
        color: #212529;
        flex-shrink: 0;
    }

    button.nav-link.result-sheet-button {
        color: black;
        font-weight: 500;
    }


    /* .quiz-wrapper {
        max-width: 800px;
        margin: 2rem auto;
        padding: 1rem;
    } */

    .quiz-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 2rem;
        padding-bottom: 1rem;
        border-bottom: 1px solid #dee2e6;
    }

    .quiz-title {
        color: #484848;
        text-align: center;
        font-size: 26px;
        font-weight: 600;
        margin-top: 50px;
        margin-bottom: 0;
        width: 100%;
    }

    .quiz-info {
        display: flex;
        justify-content: space-between;
        color: #666;
        font-size: 0.9rem;
        margin-top: 0.5rem;
    }

    .quiz-info span {
        color: #484848;
        font-size: 16px;
        font-weight: 600;
    }

    .question-container {
        background-color: #F5F5F5;
        box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
        padding: 1.5rem;
        border-radius: 8px;
        margin: 13px;
    }

    .quiz-wrapper {
        margin-bottom: 50px;
    }

    .question-text {
        font-size: 20px;
        font-weight: 600;
        color: #484848;
        margin-bottom: 1.5rem;
        display: flex;
    }

    .option-item input[type=checkbox] {
        background-color: red !important;
    }

    .options-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    div#pills-contact {
        background-color: transparent;
    }

    .option-item {
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
    }

    .option-item input[type="radio"] {
        margin-right: 0.5rem;
    }


    .inner-wrapper-qustion-parent {
        height: 100vh;
        overflow: hidden;
        overflow-y: auto;
    }

    .option-text {
        margin: 0;
        font-size: 0.95rem;
        color: #495057;
        display: flex;
        align-items: center;
        gap: 7PX;

    }

    .bold-qustion-next {
        font-weight: 900;
    }

    .action-buttons {
        display: flex;
        gap: 0.5rem;
        margin-top: 1.5rem;
    }

    .btn-explanation {
        background-color: #2d584d;
        color: white;
        border: none;
        padding: 5px 7px;
        font-size: 0.9rem;
    }

    .custom-action-buttons {
        display: flex;
        gap: 10px;
        margin-top: 10px;
    }

    .custom-btn {
        position: relative;
        /* background-color: #007bff; */
        border: none;
        cursor: pointer;
        padding: 5px;
        border-radius: 5px;
        transition: background-color 0.3s ease;
    }

    /* Tooltip container */
    .custom-btn .custom-tooltip {
        visibility: hidden;
        width: 120px;
        background-color: #333;
        color: #fff;
        text-align: center;
        padding: 5px;
        border-radius: 5px;
        position: absolute;
        z-index: 1;
        bottom: 125%;
        /* Adjust this value to position the tooltip */
        left: 50%;
        transform: translateX(-50%);
        opacity: 0;
        transition: opacity 0.3s;
    }

    /* Tooltip arrow */
    .custom-btn .custom-tooltip::after {
        content: "";
        position: absolute;
        top: 100%;
        /* Tooltip arrow points upwards */
        left: 50%;
        transform: translateX(-50%);
        border-width: 5px;
        border-style: solid;
        border-color: #333 transparent transparent transparent;
    }

    /* Show the tooltip on hover */
    .custom-btn:hover .custom-tooltip {
        visibility: visible;
        opacity: 1;
    }


    .btn-explanation:hover {
        background-color: #234439;
        color: white;
    }

    .btn-hints {
        background-color: #0d6efd;
        color: white;
        border: none;
        padding: 5px;
        font-size: 0.9rem;
    }

    .btn-hints:hover {
        background-color: #0b5ed7;
        color: white;
    }

    .btn-report {
        background-color: #dc3545;
        color: white;
        border: none;
        padding: 5px;
        font-size: 0.9rem;
    }

    .btn-report:hover {
        background-color: #bb2d3b;
        color: white;
    }


    .merit-flex-parent {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }


    .merit-list-parent {
        padding-top: 150px;
    }

    ul.nav.marit-tab-parent.nav-pills.mb-3 {
        background-color: #dbd7d7;
        border-radius: 10px;
    }

    button.nav-link.merit-exam-active.active {
        background-color: #E7F760;
        color: #1A1A1A;
    }

    button.nav-link.merit-exam-active {
        padding: 10px 25px;
        color: #1A1A1A;
    }

    div#pills-profile {
        background-color: transparent;
    }

    h1.merit-exam-header-title {
        margin: 0;
        padding: 0;
        font-size: 43px;
        font-weight: 600;
        color: #1A1A1A;
    }


    .details-exam-card {
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        /* max-width: 800px; */
        margin: 20px auto;
        background: white;
    }

    .preview-section {
        position: relative;
        background: #f8f9fa;
        height: 550px;
        padding: 20px;
        overflow: hidden;
    }

    /* Video container styles */
    .video-container {
        position: relative;
        width: 100%;
        height: 100%;
        cursor: pointer;
    }

    .video-thumbnail {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .video-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .play-button {
        width: 80px;
        height: 80px;
        background: #ff4545;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: transform 0.2s;
    }

    .play-button:hover {
        transform: scale(1.1);
    }

    .play-button::after {
        content: '';
        width: 0;
        height: 0;
        border-top: 12px solid transparent;
        border-bottom: 12px solid transparent;
        border-left: 20px solid white;
        margin-left: 4px;
    }

    /* Hide video initially */
    .video-player {
        display: none;
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .pricing-paretn {
        display: flex;
        align-items: center;
        gap: 4px;
    }

    .exam-details {
        padding: 20px;
    }

    .exam-title-merit {
        color: #335B47;
        font-size: 28px;
        font-weight: 600;
    }

    .exam-subtitle {
        color: #335B47;
        font-size: 0.9rem;
        margin-bottom: 15px;
        font-weight: 600;
    }

    .price-tag {
        color: #335B47;
        font-size: 28px;
        font-weight: bold;
    }

    .duration {
        color: #335B47;
        font-size: 0.9rem;
        font-weight: 400;
    }

    .stats-container {
        background: #E7F760;
        padding: 10px;
        border-radius: 8px;
        margin: 15px 0;
        width: 100%;
        font-weight: 600;
    }

    .stat-item {
        text-align: center;
        padding: 5px;
        font-size: 0.85rem;
        color: #444;
        border-left: 1px solid #769184;
    }

    .stat-item:first-child {
        border-left: none;
    }

    .bup-merit-exam-parent {
        border-bottom: 1px solid #5C7C6C;
    }

    .action-buttons {
        display: flex;
        gap: 10px;
        margin-top: 15px;
    }

    .enroll-btn {
        /* flex: 1; */
        padding: 12px 50px;
        border: none;
        border-radius: 6px;
        background: #e2e2e2;
        color: #444;
        font-weight: 600;
    }

    .facebook-btn {
        /* flex: 1; */
        padding: 12px 50px;
        border: none;
        border-radius: 6px;
        background: #1877f2;
        color: white;
        font-weight: 600;
    }

    .detail-inner-continar {
        padding-top: 140px;
    }

    /* .dcr-header {
        padding: 15px 20px;
        background: #fff;
        border-bottom: 1px solid #eee;
    } */

    .live-class-searching {
        gap: 30px;
    }

    .dcr-view-routine {
        background-color: rgb(40, 90, 50);
        color: #E7F760;
        padding: 6px 12px;
        border-radius: 4px;
        font-size: 15px;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 5px;
    }

    .dcr-header h5 {
        font-size: 40px;
        font-weight: 600;
        color: black;
    }

    .live-class-searching h6 {
        color: #484848;
        font-size: 30px;
        font-weight: 600;
    }

    .dcr-search-bar {
        position: relative;
        width: 60%;
        /* max-width: 240px; */
    }

    .dcr-search-bar input {
        border-radius: 35px;
        padding: 6px 15px 6px 35px;
        font-size: 14px;
        border: 1px solid #ddd;
        background-color: #f5f5f5;
    }

    .dcr-search-bar i {
        position: absolute;
        left: 15px;
        top: 50%;
        transform: translateY(-50%);
        color: #666;
    }

    .dcr-video-container {
        background: #f8f9fa;
        border-radius: 16px;
        overflow: hidden;
        position: relative;
    }

    .dcr-item-wrapper {
        position: relative;
    }

    .dcr-timer-wrapper {
        background-color: black;
        font-size: 10px;
        font-weight: 700;
        padding: 3px;
        color: white;
        border-radius: 4px;
        position: absolute;
        bottom: 13px;
        left: 80px;
    }

    .dcr-video-controls {
        position: absolute;
        bottom: 25px;
        left: 25px;
        right: 25px;
        background: rgba(0, 0, 0, 0.7);
        padding: 20px 45px;
        color: white;
        border-radius: 13px;
        display: flex;
        align-items: center;
        gap: 50px;
    }

    .dcr-progress-bar {
        width: 100%;
        height: 3px;
        background: rgba(255, 255, 255, 0.3);
        margin: 5px 0;
        position: relative;
    }

    .dcr-progress {
        position: absolute;
        height: 100%;
        background: white;
        width: 30%;
    }

    .dcr-time {
        font-size: 12px;
        color: white;
    }

    .dcr-control-buttons {
        display: flex;
        gap: 15px;
        color: white;
        font-size: 14px;
    }

    .dcr-control-btn {
        background: none;
        border: none;
        color: white;
        padding: 5px;
        cursor: pointer;
    }

    .dcr-control-btn img {
        width: 30px;
        height: 30px;
    }

    .dcr-lesson-info {
        /* background: white; */
        /* border: 1px solid #eee; */
        border-radius: 4px;
        padding: 15px;
        /* margin: 20px 0; */
    }

    .dcr-action-buttons {
        display: flex;
        gap: 10px;
        margin-top: 10px;
    }

    .dcr-action-btn {
        padding: 4px 15px;
        border-radius: 15px;
        font-size: 15px;
        color: #484848;
        font-weight: 500;
        /* border: 1px solid #ddd; */
        background: #E8E8E8;
        display: flex;
        align-items: center;
        gap: 5px;
    }

    .dcr-action-btn-three {
        background: #E8E8E8;
        height: 36px;
        width: 36px;
        border-radius: 50%;

    }

    .academic-content {
        color: #335B47;
        font-size: 20px;
        font-weight: 400;
        margin-bottom: 10px;
    }

    .video-title-wrapper {
        color: #1A1A1A;
        font-size: 29px;
        font-weight: 700;
        line-height: 35px;
    }

    img.comment-user-image {
        height: 24px;
        width: 24px;
        border-radius: 50%;
    }

    .comment-user {
        display: flex;
        align-items: center;
        gap: 20px;
        margin-top: 20px;
    }

    .line-comment {
        width: 2px;
        height: 16px;
        background-color: #ddd;
    }

    span.user-name-comment {
        font-size: 16px;
        font-weight: 500;
        color: #030303;
    }

    small.text-muted {
        font-size: 16px;
        font-weight: 500;
        color: #030303;
    }

    .dcr-comments {
        margin-top: 20px;
    }


    .comment-container {
        display: flex;
        align-items: flex-start;
        gap: 12px;
        padding: 24px 30px;
        border: 1px solid #ddd;
        /* margin-bottom: 50px; */
        border-radius: 10px;
        margin-top: 25px;
    }

    .avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        /* background-color: #e0e0e0; */
        flex-shrink: 0;
        margin-top: 12px;
    }

    /* .comment-form {
        flex-grow: 1;
        display: flex;
        gap: 12px;
    } */

    form.comment-form {
        width: 100%;
    }

    .comment-input {
        flex-grow: 1;
        border: none;
        outline: none;
        font-size: 14px;
        color: #333;
        padding: 20px 10px;
        width: 100%;
        /* border-bottom: 1px solid #e0e0e0; */
        background-color: transparent;
    }

    .comment-input::placeholder {
        color: #999;
    }

    .post-button {
        padding: 8px 36px;
        background-color: #2d5a4c;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        display: flex;
        align-items: center;
        justify-content: end;
        margin-left: auto;
    }

    .post-button:hover {
        background-color: #234539;
    }






    .comments-section {
        margin-top: 20px;
    }

    .comments-count {
        font-size: 14px;
        color: #333;
        margin-bottom: 20px;
        font-weight: 700;
    }

    .comment-wrapper-video {
        display: flex;
        gap: 12px;
        margin-bottom: 24px;
        position: relative;
        padding: 15px;
        border: 1px solid #E8E8E8;
        border-radius: 6px;
    }

    .comment.highlighted {
        border: 1px solid #e0e0e0;
        padding: 16px;
        border-radius: 4px;
        background-color: #f8f9fa;
    }

    .avatar-wapper {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        flex-shrink: 0;
    }

    .comment-content {
        flex-grow: 1;
    }

    .user-name {
        font-weight: bold;
        font-size: 14px;
        margin-bottom: 4px;
        color: #000;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .timestamp {
        color: #666;
        font-size: 12px;
        font-weight: normal;
    }

    .comment-text {
        font-size: 14px;
        margin-bottom: 8px;
    }

    .comment-actions {
        display: flex;
        align-items: center;
        gap: 16px;
    }

    .action-button-reply {
        display: flex;
        align-items: center;
        gap: 4px;
        background: none;
        border: none;
        color: #666;
        font-size: 13px;
        font-weight: 700;
        cursor: pointer;
        padding: 4px;
    }

    .reply-count {
        color: #065fd4;
        font-size: 13px;
        margin-top: 8px;
        cursor: pointer;
        font-weight: 700;
    }

    .likes {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .like-count {
        color: #666;
        font-size: 13px;
        display: flex;
        align-items: center;
        gap: 7px;
    }

    .mine-user-parent {
        position: relative;
    }

    .mine-user-parent img {
        height: 15px;
        width: 15px;
        border-radius: 50%;
    }

    .love-react {
        position: absolute;
        left: 9px;
        bottom: 0;
        height: 10px !important;
        width: 10px !important;
    }



    .dcr-comment {
        display: flex;
        gap: 10px;
        padding: 15px 0;
        border-bottom: 1px solid #eee;
    }

    .dcr-comment-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
    }


    .playlist-video-title h2 {
        color: #1A1A1A;
        font-size: 16px;
        font-weight: 700;
    }

    .playlist-video-title p {
        font-size: 12px;
        font-weight: 400;
        color: #606060;
        display: flex;
        align-items: center;
        gap: 20px;
    }

    .verify-icon {
        height: 15px;
        width: 15px;
    }

    .dcr-playlist-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }

    .dcr-playlist-item {
        display: flex;
        gap: 10px;
        padding: 15px;
        border-radius: 10px;
        border: 1px solid #dad9d9;
        margin-bottom: 20px;
    }

    .dcr-thumbnail {
        width: 120px;
        height: 72px;
        border-radius: 4px;
        object-fit: cover;
    }

    .dcr-time-badge {
        /* background-color: #1a73e8; */
        color: #0F0F0F;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 12px;
        display: inline-block;
        font-weight: 600;
        display: flex;
        align-items: center;
        justify-content: end;
        font-size: 14px;
    }

    .dcr-sort-dropdown {
        font-size: 14px;
        padding: 4px 8px;
        border-radius: 4px;
        border: 1px solid #ddd;
    }

    .dcr-post-btn {
        background-color: rgb(40, 90, 50);
        color: white;
        border: none;
        padding: 5px 15px;
        border-radius: 4px;
        font-size: 14px;
    }

    .video-progress-container {
        /* background: rgba(0, 0, 0, 0.75); */
        padding: 10px 15px;
        width: 100%;
        max-width: 500px;
        border-radius: 8px;
    }

    .progress-bar-wrapper {
        display: flex;
        align-items: center;
        gap: 10px;
        color: white;
        font-family: Arial, sans-serif;
        font-size: 14px;
    }

    .progress-track {
        flex-grow: 1;
        height: 7px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 2px;
        position: relative;
        cursor: pointer;
    }

    .progress-fill {
        position: absolute;
        left: 0;
        top: 0;
        height: 100%;
        width: 70%;
        background: white;
        border-radius: 2px;
    }

    .progress-handle {
        width: 15px;
        height: 15px;
        background: white;
        border-radius: 50%;
        position: absolute;
        top: 50%;
        left: 70%;
        transform: translate(-50%, -50%);
        box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .samll-bg {
        width: 10px;
        height: 10px;
        background: #d4e157;
        border-radius: 50%;
        position: absolute;
        left: 3px;
        right: 0;
        bottom: 0;
        top: 3px;
    }

    .time {
        font-weight: 700;
        min-width: 35px;
    }



    .next-self-exam-card {
        background-color: #d4ebdf;
        border-radius: 12px;
        padding: 24px;
        margin-bottom: 20px;
        position: relative;
        overflow: hidden;
        transition: transform 0.3s ease;
    }

    .exam-card:hover {
        transform: translateY(-5px);
    }

    .exam-icon {
        font-size: 24px;
        color: #2d584d;
        margin-bottom: 10px;
    }

    .rapid-exam-title {
        font-size: 24px;
        color: #2d584d;
        font-weight: 600;
    }

    .rapid-exam-subtitle {
        font-size: 14px;
        color: #1A1A1A;
        font-weight: bold;
        margin-bottom: 20px;
    }

    .exam-btn-primary {
        background-color: #2d584d;
        color: #E7F760;
        border: none;
        padding: 8px 20px;
        border-radius: 6px;
        text-decoration: none;
        display: inline-block;
        transition: background-color 0.3s ease;
    }

    .exam-btn-primary:hover {
        background-color: #234539;
        color: #E7F760;
    }

    .exam-btn-secondary {
        background-color: #1976D2;
        color: white;
        border: none;
        padding: 8px 20px;
        border-radius: 6px;
        text-decoration: none;
        display: inline-block;
        margin-left: 10px;
        transition: background-color 0.3s ease;
    }

    .exam-btn-secondary:hover {
        background-color: #2980b9;
        color: white;
    }


    @media (max-width: 991px) {
        .row.main-flex-row {
            flex-direction: column;
        }

        .main-wrapper-sheet {
            display: none;
        }

        .question-wrapper-parent {
            width: 100% !important;
        }

        .question-wrapper-parent {
            width: 100% !important;
        }

        span.header-name {
            width: 50% !important;
        }

        span.header-marks {
            width: 35% !important;
        }

        .question-wrapper-parent {
            max-width: 100%;
        }

        .trigger {
            display: block !important;
        }

        .stats-container-next {
            grid-template-columns: 1fr 1fr 1fr !important;
        }

        .answer-sheet.col-md-4 {
            display: none;
        }

        .mcq-questions-section {
            background-color: transparent;
            padding: 0px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: unset;
            /* height: 100vh; */
            display: grid;
            /* grid-template-columns: 1fr 1fr; */
        }

        .mcq-question {
            background-color: #f5f5f5;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
            margin: 10px;
        }

        /* .mcq-header{
            flex-direction: column;
            gap: 10px;
        } */

    }

    @media (max-width: 768px) {
        .card-body {
            flex-direction: column;
            align-items: flex-start;
        }


        .stats-container-next {
            grid-template-columns: 1fr 1fr !important;
        }

        .prev-btn {
            display: block !important;
        }

        .start-exam {
            margin-top: 15px;
        }

        .exam-center-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 20px;
        }

        .exam-center-tabs {
            margin-top: 15px;
        }

        .bup-card-info {
            flex-direction: column;
            gap: 17px;
        }

        .mcq-answer-grid {
            grid-template-columns: repeat(3, 1fr);
        }

        .exam-result-wrapper {
            padding: 1rem;
            /* margin: 1rem; */
        }

        .result-card {
            padding: 0.8rem;
        }

        .result-value {
            font-size: 1.1rem;
        }

        .exam-score-circle {
            width: 80px;
            height: 80px;
            font-size: 20px;
        }

        .party-popper {
            font-size: 30px;
        }

        /* .score-report-wrapper {
            padding: 1rem;
            margin: 1rem;
        } */

        .grades-grid {
            grid-template-columns: 1fr;
        }

        .score-card {
            padding: 1rem;
        }

        .exam-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
        }

        .stats-container {
            flex-direction: column;
        }

        .timer-box,
        .progress-box,
        .remaining-box {
            width: 100%;
        }

        .action-buttons {
            grid-template-columns: 1fr;
        }

        .dcr-academic-inner-parent {
            flex-direction: column;
        }

        .dcr-action-buttons {
            width: 100%;
        }

        .academic-next-wrapper {
            width: 100%;
        }

        .remaining-box {
            text-align: left;
            min-width: unset !important;
        }

        .dcr-video-controls {
            gap: 10px;
            padding: 10px;
        }

        .video-progress-container {
            padding: 10px 0px;
        }

        .dcr-video-controls {
            left: 10px;
            right: 10px;
        }

        .dcr-header h5 {
            font-size: 30px;
            line-height: 34px;
            width: 100%;
        }

        .live-class-searching {
            flex-direction: column;
        }

        .dcr-search-bar {
            width: 100%;
        }

        .live-class-searching h6 {
            width: 100%;
        }

        .dcr-header {
            flex-direction: column;
            gap: 20px;
        }

        .dcr-view-routine {
            margin-right: auto;
        }

        .dcr-playlist {
            margin-left: 0;
            margin-top: 20px;
        }

        .exam-title {
            font-size: 20px;
        }
    }


    @media (max-width: 576px) {
        .exam-stats {
            grid-template-columns: repeat(2, 1fr);
            gap: 8px;
        }

        .exam-header {
            flex-direction: column;
            text-align: center;
        }

        .exam-score-circle {
            margin-right: 0;
            margin-bottom: 10px;
        }

        .exam-details-btn {
            position: relative;
            display: block;
            text-align: center;
            margin-top: 15px;
            top: -8px;
            right: 0;
        }

        .mcq-footer-text {
            font-size: 18px;
            line-height: 20px;
        }

        .stats-container-next {
            grid-template-columns: 1fr;
        }

        .ranking-list-wrapper {
            padding: 0.5rem;
            margin: 1rem;
        }

        .user-list-item {
            padding: 0.5rem 0.75rem;
        }

        .user-avatar {
            width: 32px;
            height: 32px;
        }

        .user-name {
            font-size: 0.9rem;
        }

        .user-score {
            margin: 0 0.75rem;
            font-size: 0.9rem;
            width: 36% !important;
        }

        .user-rank {
            font-size: 0.9rem;
        }

        .quiz-wrapper {
            padding: 0;
            margin: 0;
        }

        .quiz-title {
            margin-top: 20px !important;
        }

        .question-container {
            padding: 1rem;
        }

        .quiz-title {
            font-size: 1.25rem;
        }

        .quiz-info {
            flex-direction: column;
            align-items: center;
            text-align: center;
        }

        .stats-container {
            flex-direction: column;
        }

        .stat-item {
            width: 100%;
            margin: 5px 0;
        }

        .action-buttons {
            flex-direction: column;
        }

        .enroll-btn,
        .facebook-btn {
            width: 100%;
            margin: 5px 0;
        }

        .user-list-header span {
            font-size: 14px !important;
        }

        .stats-container-next {
            grid-template-columns: 1fr 1fr !important;
        }
    }


    @media (max-width: 480px) {
        .mcq-answer-grid {
            grid-template-columns: repeat(2, 1fr);
        }

        .parent-result {
            padding-top: 90px !important;
        }
    }

    .mcq-answer-omr-selected {
        background: #1A1A1A;
    }

    .stats-container-next {
        grid-template-columns: 1fr 1fr !important;
    }


    .exam-container {
        /* / max-width: 800px; / */
        margin: 20px auto;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        border-radius: 12px;
        background: white;
        margin-bottom: 30px;
    }

    .user-list-header span {
        color: #1A1A1A;
        font-size: 18px;
    }

    span.header-name {
        margin-left: 22px;
        width: 73%;
    }

    span.header-marks {
        width: 20%;
    }

    .exam-header {
        display: flex;
        /* justify-content: space-between; */
        align-items: center;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid #eee;
    }

    .exam-title {
        font-size: 1.25rem;
        font-weight: 500;
        color: #333;
    }

    .exam-info {
        display: flex;
        gap: 5px;
        color: #666;
        font-size: 0.9rem;
    }

    .exam-info span {
        font-size: 22px;
        font-weight: 600;
        color: #1A1A1A;
    }

    .stats-container-next {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr 1fr !important;
        gap: 15px;
        /* / margin-bottom: 20px; / */
        flex-wrap: wrap;
    }

    .timer-box {
        background: #1a1a1a;
        color: white;
        padding: 8px 80px;
        display: flex;
        align-items: center;
        gap: 3px;
        border-radius: 6px;
        /* / min-width: 160px; / */
    }

    .progress-box {
        background: #f5f5f5;
        padding: 8px 80px;
        display: flex;
        align-items: center;
        gap: 3px;
        border-radius: 6px;
        /* / min-width: 160px; / */
    }

    .remaining-box {
        background: #f5f5f5;
        padding: 8px 80px;
        display: flex;
        align-items: center;
        gap: 3px;
        border-radius: 6px;
        /* / min-width: 160px; / */
    }

    .stat-label {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 4px;
        color: inherit;
        opacity: 0.8;
    }

    .stat-value {
        font-size: 17px;
        line-height: 20px;
        font-weight: 600;
        color: inherit;
    }

    .prev-btn {
        padding: 14px;
        border: 1px solid #ddd;
        background: white;
        border-radius: 6px;
        color: #333;
        font-weight: 600;
        transition: all 0.3s ease;
        font-size: 13px;
        display: none;
    }

    .finish-btn {
        padding: 8px 80px;
        border: none;
        background: #f5f5f5;
        border-radius: 6px;
        color: #333;
        font-weight: 600;
        transition: all 0.3s ease;
        font-size: 16px;
    }

    .prev-btn:hover,
    .finish-btn:hover {
        background: #eee;
    }

    .xm-center-icon {
        width: 40px;
    }

    .large-title {
        font-size: 26px !important;
    }

    .blinking-item {
        display: flex;
        justify-content: center;
        align-items: center;

    }

    .blinking-dot {
        flex-basis: 12px;
        width: 10px;
        height: 10px;
        background-color: red;
        border-radius: 50%;
        animation: blink 1s infinite;
        vertical-align: middle;
        margin-right: 3px;
    }

    ul.exam-center-page-tab {
        flex-wrap: nowrap;
        gap: 4px;
        justify-content: flex-end;
    }

    ul.exam-center-page-tab li{
        flex-grow: 1;
        border: 0 !important;
        text-align: right;
    }

    ul.exam-center-page-tab li button {
        width: 100%;
        color: #E7F760 !important;
        background-color: #2d584d !important;
        padding-top: 12px;
        border: 1px solid #ddd !important;
        font-size: 12px;
    }

    .math-equation p{
        text-align: left;
    }

    .result-sheet-tab-parent{
        gap: 4px;
    }

    .result-sheet-tab-parent li{
        border: 1px solid #ddd;
        border-radius: 0.375rem;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    button.nav-link.result-sheet-button.active{
        padding: 0.5rem 1rem;
    }

    @keyframes blink {

        0%,
        100% {
            opacity: 1;
        }

        50% {
            opacity: 0;
        }
    }
