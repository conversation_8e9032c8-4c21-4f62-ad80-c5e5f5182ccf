.chat-users {
  height: 55vh;
  overflow-y: scroll;
}

.scroll-bar::-webkit-scrollbar {
  width: 5px;
}

.scroll-bar::-webkit-scrollbar-track {
  background-color: #f5f5f5;
}

.scroll-bar::-webkit-scrollbar-thumb {
  background-color: #ccc;
  border-radius: 20px;
}

.scroll-bar::-webkit-scrollbar-thumb:hover {
  background-color: #999;

}

.chat-users img {
  border-radius: 100%;
  height: 25px;
  width: 25px;
  margin-right: 10px;
}


.chat-user {
  padding: 5px;
  border-radius: 5px;
  margin: 10px;
  cursor: pointer;
  background-color: #F8F8F8;
  transition: all 1s;
}

.chat-box {
  background-color: #F8F8F8;
  border-radius: 10px;
}

.message-area {
  display: flex;
  flex-direction: column-reverse;
  height: 50vh;
  padding: 20px;
  overflow-y: scroll;
}

.selected-user {
  height: 50px;
  background-color: #f1f0f0;
  padding: 0 15px;

}

.selected-user #chat-user-image img {
  border-radius: 100%;
  height: 40px;
  width: 40px;
}

.chat-message {
  display: flex;
  flex-direction: column;
  margin: 10px 0;
}

.message-text {
  word-wrap: break-word;
  font-size: 14px;
}

.sender {
  justify-content: flex-end;
  align-items: flex-end;
}

.sender .message-text {
  background-color: blue;
  color: white;
  border-radius: 10px;
  padding: 10px;
  max-width: 70%;
  text-align: justify;
}

.receiver {
  justify-content: flex-start;
  align-items: flex-start;
}

.receiver .message-text {
  background-color: #f1f0f0;
  border-radius: 10px;
  padding: 10px;
  max-width: 70%;
  text-align: justify;
}

.no-message {
  height: 40vh;
  display: flex;
  justify-content: center;
  align-items: center;
}

.course-title .unseen-count {
  color: red;
  text-align: center;
  font-size: 12px;
  font-weight: 700;
}

#select-user h6 {
  height: 60vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #F8F8F8;
}

.course-list .course-user {
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  max-width: 100%;
  background-color: #eaeaea;
  border-radius: 5px;
  cursor: pointer;
  margin: 8px;
  padding: 5px;
}

.course-list li .course-title {
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 12px;
  background-color: #fff;
  padding: 5px 15px;
  border-radius: 5px;
  white-space: nowrap;
  word-wrap: normal;
  max-height: calc(14px * 2);
  line-height: 14px;
  transition: background-color 0.3s ease;
}

.course-list li .course-title.active {
  background-color: #848484;
  font-size: 12px;
  font-weight: bold;
  color: white;
}