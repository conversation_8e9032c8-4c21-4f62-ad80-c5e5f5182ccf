/*
-------------------------------------------------
  Theme Name: LMSZAI- LMS Online Courses and Education HTML5 Responsive Template
  Theme URL:
  Author: zainiktheme
  Author URL: https://themeforest.net/user/zainiktheme
  Creation Date: 04-10-2021
  Description:A default stylesheet for LMSZAI- LMS Online Courses and Education HTML5 Responsive Template
  Version: 1.00
  Primary use: lms, eEducation, learning, learn, education, course, online courses, eLearning, responsive, bootstrap, html5, clean, modern, creative etc.

  ---------------------------------------------------
  Developed By: zainiktheme
  Developer URL: https://themeforest.net/user/zainiktheme
  Developer: Suraiya <PERSON>ha

  ---------------------------------------------------

  --------------------------------------------------
  Table of Contents
  --------------------------------------------------
  1. General CSS
  2. Typography CSS
  3. Button CSS
  4. Navbar CSS
  5.Scroll To Top Area
  6.Preloader Area Start
  7. Header Area
  8. Special Feature Area
  9. Course Slider Area
  10. Video Area Start
  11. Top Categories Area
  12. Top Instructors Area Start
  13. Testimonial Area Start
  14. Achievement Area
  15. Course Instructor and Support Area
  16. FAQ Area
  17. Footer Area
  18. Course Single Area
  19. Course Watch Page Area
  20. Inner Page Banner/Header Area
  21. About Page Area
  22. Courses Page Area
  23. Become an instructor Page
  24. FAQ Page Area Page
  25. Contact Page Area Page
  26. Error Page Area Page
  27. Instructor Details / Instructor Single Page
  28. Blog Page Area
  29. Blog Details Page Area
  30. Cart Page Area
  31. Wishlist Page Area Page
  32. My Courses Page Area Page
  33. Student Profile Page Area Page
  34. Instructor Profile Page
  35. Message Page Area
  36. Instructor Dashboard Page
  37. Instructor Course Upload/ Form Multistep Page
  38. Instructor Analysis Page
  39. Checkout Page
  40. Sign Up Page
  41. Home All Instructor
  42. Responsive CSS

------------------------------------------------ */


/*-----------------------------------------------
    1. General Style
-------------------------------------------------*/

* {
    margin: 0;
    padding: 0;
    border: none;
    outline: none;
}

body,
html {
    height: 100%;
    margin: 0;
}

body {
    background: #fff none repeat scroll;
    font-size: 16px;
    font-weight: normal;
    font-family: var(--body-font-family);
    color: var(--body-font-color);
    overflow-x: hidden;
    scroll-behavior: smooth;
}

a {
    color: var(--heading-color);
}

a:hover {
    /* color: var(--theme-color); */
}

/* Theme All Transitions
------------------------------- */
a,
#mainNav,
.navbar .dropdown .dropdown-toggle::after,
.theme-btn,
.theme-button1,
.theme-button2,
button,
.owl-carousel button.owl-dot,
.instructor-support-item,
.instructor-social ul li a svg,
button,
.course-img-wrap img,
.course-img-wrap:before,
.course-item-hover-btns {
    transition: all .5s ease-in-out;
}

.fast-transition,
.message-user-item {
    transition: all .3s ease-in-out;
}

.slow-transition {
    transition: all .7s ease-in-out;
}

ul li {
    margin: 0;
    padding: 0;
    list-style-type: none;
}

ul {
    margin: 0;
    padding: 0;
}

a,
a>* {
    outline: none;
    cursor: pointer;
    text-decoration: none;
}

a:hover {
    text-decoration: none;
    /* color: var(--theme-color); */
    color: inherit;
}

img::selection {
    background: transparent;
}

.page-link:focus {
    box-shadow: none;
}

/*----Form Control Reset CSS----*/

.form-control,
.form-select {
    background-color: var(--white-color);
    height: 49px;
    color: var(--body-font-color);
    border: 1px solid var(--border-color);
    border-radius: 4px;
}

.form-control:focus {
    background-color: var(--white-color);
    box-shadow: none;
}

.form-select:focus {
    border-color: inherit;
    box-shadow: none;
    border-color: #86b7fe;
}

.form-control::placeholder,
.placeholder-color {
    color: #a0a3aa;
}

label {
    margin-bottom: 10px;
}

.form-check-input:checked {
    background-color: var(--theme-color);
    border-color: var(--theme-color);
}

textarea.form-control,
textarea {
    height: auto;
}

.form-check-input:focus {
    box-shadow: none;
    border: 1px solid var(--theme-color);
}

/*------------------------------------------
    2.  Typography
-------------------------------------------*/
h1,
h2,
h3,
h4,
h5,
h6 {
    font-weight: 500;
    margin: 0;
    color: var(--heading-color);
}

h3 {
    font-size: 41px;
    line-height: 44px;
}

h4 {
    font-size: 33px;
    line-height: 35px;
}

h5 {
    font-size: 26px;
    line-height: 28px;
}

h6 {
    font-size: 21px;
    line-height: 22px;
}

p {
    margin-bottom: 0;
    font-size: 16px;
    font-weight: 400;
    color: var(--body-font-color);
    line-height: 25px;
}

figure {
    margin: 0 0 0;
}

/*--------------------------------
            2.1 Theme Color
-----------------------------------*/
/* :root {
    --white-color: #fff;
    --theme-color: #5e3fd7;
    --light-purple: rgba(117, 79, 254, 0.1);
    --heading-color: #040453;
    --orange-color: #FC8068;
    --orange-deep: #FF3C16;
    --body-font-color: #52526C;
    --gray-color: #767588;
    --gray-color2: #929292;
    --disable-color: #B5B4BD;
    --color-green: #45C881;
    --color-light-green: rgba(69, 200, 129, 0.22);
    --color-yellow: #FFC014;
    --light-bg: #F9F8F6;
    --page-bg: #F8F6F0;
    --plyr-color-main: #5e3fd7;
    --border-color: rgba(0, 0, 0, 0.07);
    --border-color2: rgba(0, 0, 0, 0.09);
    --body-font-family: 'Jost', sans-serif;
} */

.color-heading,
.booking-history-past-table.table th {
    color: var(--heading-color);
}

.bg-theme {
    background-color: var(--theme-color);
}

.bg-green {
    background-color: var(--color-green);
}

.bg-orange {
    background-color: var(--orange-color);
}

.bg-light {
    background-color: var(--light-bg);
}

.bg-page {
    background-color: var(--page-bg);
}

.color-hover {
    color: var(--theme-color);
}

.bg-light-purple {
    background-color: var(--light-purple);
}

.bg-hover {
    background-color: var(--theme-color);
}

.color-para {
    color: var(--body-font-color);
}

.bg-disabled {
    background-color: var(--disable-color) !important;
}

/* default text color */
.para-color {
    color: var(--body-font-color);
}

/* nutral-1 */
.color-gray {
    color: var(--gray-color);
}

/* nutral-2 */
.color-gray2 {
    color: var(--gray-color2);
}

.color-light-green {
    color: var(--color-light-green);
}

.theme-border {
    border: 1px solid var(--border-color);
}

.border-bottom {
    border-bottom: 1px solid var(--border-color) !important;
}

.theme-font-color {
    color: var(--body-font-color);
}

.color-deep-orange {
    color: var(--orange-deep);
}

.bg-deep-orange {
    background-color: var(--orange-deep);
}

.color-orange {
    color: var(--orange-color);
}

.color-green {
    color: var(--color-green) !important;
}

.bg-green {
    background-color: var(--color-green);
}

.border-color {
    border-color: var(--border-color2);
}

.border-top {
    border-top: 1px solid
}

.border-bottom {
    border-bottom: 1px solid
}

.table-td-hover-color {
    background-color: #F9F9F9;
}

.border-1 {
    border: 1px solid #EBEBEB;
}

table tr {
    transition: .5s ease all;
}

th {
    max-width: 100%;
    min-width: 111px;
}

table tr:hover {
    background-color: #F9F9F9;
}

button.disabled:hover,
button.disabled.theme-button1:hover,
button.disabled.theme-button2:hover {
    color: var(--white-color) !important;
}

.status {
    padding: 6px 10px 7px;
    border-radius: 4px;
}

.status.active {
    background-color: #E1FAE1;
    color: #4CBF4C;
}

.status.blocked {
    background-color: #FFE6E6;
    color: #F96363;
}

.status.pending {
    background-color: #FFF4E4;
    color: #F0AD4E;
}

/*--------------------------------
    2.3 Theme padding, margin
-----------------------------------*/
section,
.section-b-space {
    padding-bottom: 40px;
}

.section-t-space {
    padding-top: 90px;
}

.section-b-85-space {
    padding-bottom: 85px;
}

.section-t-85-space {
    padding-top: 85px;
}

.section-p-t-b-90 {
    padding: 90px 0 90px;
}

.p-30 {
    padding: 30px;
}

.p-20 {
    padding: 20px;
}

.pb-15 {
    padding-bottom: 15px;
}

.pb-20 {
    padding-bottom: 20px;
}

.px-20 {
    padding-right: 20px;
    padding-left: 20px;
}

.pb-30 {
    padding-bottom: 30px;
}

.pt-30 {
    padding-top: 30px;
}

.mt-100 {
    margin-top: 100px;
}

.mb-15 {
    margin-bottom: 15px;
}

.mt-15 {
    margin-top: 15px;
}

.mt-25 {
    margin-top: 25px;
}

.mb-25 {
    margin-bottom: 25px;
}

.mb-20 {
    margin-bottom: 20px;
}

.mt-20 {
    margin-top: 20px;
}

.mt-30 {
    margin-top: 30px;
}

.mb-30 {
    margin-bottom: 30px;
}

.mr-30 {
    margin-right: 30px;
}

.mt-50 {
    margin-top: 50px;
}

.mt-45 {
    margin-top: 45px;
}

.mr-15 {
    margin-right: 15px;
}

.radius-95 {
    border-radius: 95px;
}

.radius-3 {
    border-radius: 3px;
}

.radius-4 {
    border-radius: 4px;
}

.radius-5 {
    border-radius: 5px;
}

.radius-8 {
    border-radius: 8px;
}

.radius-50 {
    border-radius: 50%;
}

/*--------------------------------
    2.3 Theme Font Family, Font Size
-----------------------------------*/
.font-normal {
    font-weight: 400;
}

.font-bold {
    font-weight: 700;
}

.font-semi-bold {
    font-weight: 600;
}

.font-medium {
    font-weight: 500;
}

.font-24 {
    font-size: 24px;
    line-height: 31px;
}

.font-20 {
    font-size: 20px;
    line-height: 31px;
}

.font-18 {
    font-size: 18px;
    line-height: 19px;
}

.font-17 {
    font-size: 17px;
}

.font-16 {
    font-size: 16px;
}

.font-15 {
    font-size: 15px;
    line-height: 23px;
}

.font-14 {
    font-size: 14px;
}

.font-13 {
    font-size: 13px;
    line-height: 18px;
}

.font-12 {
    font-size: 12px;
    line-height: 13px;
}

.font-11 {
    font-size: 11px;
    line-height: 12px;
}

/*--------------------------------
   2.4 Template Default CSS
-----------------------------------*/
section {
    position: relative;
    /* z-index: 9; */
}

.section-left-title-with-btn {
    width: 100%;
}

.section-left-title-with-btn .section-heading {
    width: 100%;
}

.section-title.section-title-left {
    width: calc(100% - 48%);
}

.section-heading {
    font-weight: 500;
    font-size: 41px;
    line-height: 106.5%;
    /* text-transform: capitalize; */
    margin-bottom: 18px;
    width: 60%;
}

.section-title.text-center .section-heading {
    margin: 0 auto 18px;
}

.section-heading-light,
.section-sub-heading-light {
    color: var(--white-color);
}

.section-sub-heading {
    font-weight: normal;
    /* font-size: 14px; */
    font-size: 18px;
    line-height: 154%;
    /* text-transform: uppercase; */
    margin-bottom: 44px;
}

.gradient-bg {
    background: var(--gradient-banner-bg);
    background-blend-mode: overlay, normal, overlay, color-burn, screen, overlay, difference, difference, normal;
    display: flex;
}

.footer-gradient-bg {
    background: var(--footer-gradient-bg);
    background-blend-mode: overlay, normal, overlay, color-burn, screen, overlay, difference, difference, normal;
    display: flex;
}

.section-overlay {
    /* display: none; */
    background: var(--gradient-overlay-bg);
    height: 100%;
    width: 100%;
}

.small-background-img-prop {
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
}

.cursor {
    cursor: pointer;
}

.fit-image {
    width: 100%;
    object-fit: cover;
    -o-object-fit: cover;
}

/*--------------------------------
   2.5 Template Common CSS
-----------------------------------*/
.testimonial-bottom-content .star-full .iconify,
.course-single-page-header .star-full .iconify,
.review-tab-count-box .rating-list .star-full .iconify {
    color: var(--color-yellow);
}

.faq-page-area .section-title {
    text-align: center;
}

.faq-page-area .section-heading {
    margin: 0 auto 18px;
}

table .course-item {
    background-color: transparent;
}

.table>:not(caption)>*>* {
    vertical-align: middle;
}

/*Pagination CSS*/
.pagination {
    margin-top: 25px;
}

.pagination li {
    margin-right: 14px;
    border-radius: 3px;
}

.page-item.active .page-link {
    color: var(--white-color);
    background-color: var(--theme-color);
    border-color: var(--theme-color);
}

.page-item .page-link .iconify {
    font-size: 19px;
    color: var(--body-font-color);
}

.pagination .page-link {
    color: var(--gray-color);
    font-size: 15px;
    line-height: 16px;
    background-color: var(--white-color);
    height: 45px;
    width: 45px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px !important;
    border-radius: 3px;
    border: 1px solid #F2F2F2;
    box-sizing: border-box;
    box-shadow: 0 6px 21px rgba(21, 3, 89, 0.08);
}

/* Common Text Hover color*/
/* .blog-sidebar-categories li a:hover, */
.forum-categories-filter-box button:hover {
    color: var(--theme-color);
}

/*------------------------------------------
    2. Typography
-------------------------------------------*/

/*--------------------------------------------
    3. Button Style
---------------------------------------------*/
.section-btn {
    margin-top: 42px;
}

.btn {
    padding: 0;
}

.btn.focus,
.btn:focus {
    outline: 0;
    box-shadow: none;
}

button {
    background-color: transparent;
}

button:focus {
    outline: 0;
}

.btn-close:focus {
    box-shadow: none;
}

/*-----Theme Button Style-----*/
.theme-btn>svg {
    height: 20px;
    margin-left: 3px;
    width: 17px;
}

.theme-button1,
.theme-button2,
.btn-violet-transparent {
    color: var(--white-color) !important;
    background-color: var(--theme-color);
    border: 2px solid var(--theme-color);
    display: inline-flex;
    align-items: center;
    cursor: pointer;
    outline: none;
    /* text-transform: capitalize; */
    z-index: 99;
    padding: 9.5px 24px !important;
    font-size: 15px !important;
    justify-content: center;
    border-radius: 4px;
    font-weight: 600 !important;
}

.theme-btn {
    display: inline-flex;
    align-items: center;
    cursor: pointer;
    outline: none;
    /* text-transform: capitalize; */
    z-index: 99;
    padding: 9.5px 24px !important;
    font-size: 15px !important;
    justify-content: center;
    border-radius: 4px;
    font-weight: 600 !important;
}

.theme-button1:hover {
    color: var(--theme-color) !important;
    background-color: var(--white-color);
    border: 2px solid var(--white-color);
}

.theme-button2 {
    background-color: var(--white-color) !important;
    color: var(--theme-color) !important;
    border: 2px solid var(--white-color);
}

.theme-button2:hover {
    color: var(--white-color) !important;
    background-color: var(--theme-color) !important;
    border: 2px solid var(--theme-color);
}

.theme-button3 {
    border: 2px solid var(--theme-color);
}

.theme-button3:hover {
    border: 2px solid var(--theme-color);
}

.btn-violet-transparent {
    background-color: rgba(117, 79, 254, 0.27);
    border: 2px solid rgba(117, 79, 254, 0.27) !important;
    color: var(--theme-color) !important;
}

.btn-violet-transparent:hover {
    background-color: var(--white-color);
    color: var(--theme-color) !important;
}

.btn-orange,
.orange-theme-btn {
    background-color: var(--orange-color);
    border: 2px solid var(--orange-color);
    color: var(--white-color);
}

.btn-orange:hover,
.orange-theme-btn:hover {
    background-color: var(--white-color);
    border: 2px solid var(--orange-color);
    color: var(--orange-color) !important;
}

.orange-theme-btn {
    border: 1px solid var(--orange-color) !important;
}

.green-theme-btn {
    background-color: var(--color-green);
    border-color: var(--color-green) !important;
    color: var(--white-color) !important;
}

.green-theme-btn .iconify,
.theme-btn.default-hover-btn .iconify {
    margin-right: 8px;
    margin-left: 0;
}

.green-theme-btn:hover {
    border-color: var(--color-green) !important;
    color: var(--color-green) !important;
}

.quiz-back-btn,
.default-back-btn {
    border: 1px solid #D8D8D8;
    background-color: var(--white-color);
    color: var(--body-font-color) !important;
    margin-right: 10px;
}

.quiz-list-page-top-right {
    text-align: right;
}

.quiz-back-btn:hover,
.default-back-btn:hover {
    color: var(--theme-color) !important;
}

/*Load More Button css*/
.load-more-btn {
    background-color: var(--theme-color) !important;
    border: 2px solid var(--theme-color) !important;
    color: var(--white-color) !important;
    height: 38px;
    padding: 18px !important;
    font-size: 14px !important;
}

.load-more-btn:hover {
    border: 2px solid var(--theme-color) !important;
}

.load-more-btn .iconify {
    margin-left: 7px;
}

.default-delete-btn-red {
    background-color: #FF1F1F;
    border: 1px solid #FF1F1F;
    color: var(--white-color);
    min-width: 93px !important;
}

.default-delete-btn-red .iconify {
    margin-right: 8px;
    margin-left: 0;
}

.default-delete-btn-red:hover {
    background-color: var(--white-color);
    border: 1px solid #FF1F1F !important;
    color: #FF1F1F !important;
}

.default-edit-btn-blue {
    background-color: #42B7FF;
    border: 1px solid #42B7FF;
    color: var(--white-color);
    min-width: 93px !important;
}

.default-edit-btn-blue .iconify {
    margin-right: 8px;
    margin-left: 0;
}

.default-edit-btn-blue:hover {
    background-color: var(--white-color);
    border: 1px solid #42B7FF;
    color: #42B7FF;
}

.light-purple-theme-btn {
    background-color: #A921FF;
    border: 1px solid #A921FF;
}

.light-purple-theme-btn:hover {
    background-color: var(--white-color);
    border: 1px solid #A921FF !important;
    color: #A921FF !important;
}

.disabled-btn,
.disabled-btn:hover {
    cursor: default;
    background-color: var(--disable-color) !important;
    color: var(--white-color) !important;
    border-color: var(--disable-color) !important;
}

/*-------------------------------------------
           4. Navbar Area CSS
-------------------------------------------*/
.menu-section-area {
    padding: 0;
    z-index: 999;
}

@media only screen and (min-width: 992px) {
    #mainNav {
        display: block;
    }

    .navbar-expand-lg .navbar-nav {
        align-items: center;
    }
}

@media only screen and (max-width: 991.98px) {
    .navbar-nav {
        width: 100%;
    }
}

/*.menu-section-area .navbar {*/
.navbar {
    padding: 0;
}

#mainNav {
    position: fixed;
    height: 100px;
    display: flex !important;
    transition: all ease .6s;
    background: transparent;
    padding: 21px 100px;
    align-items: center;
    width: 100%;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    background-color: var(--navbar-bg-color);

}

#mainNav.sticky {
    position: fixed;
    z-index: 99;
    width: 100%;
    top: 0;
    box-shadow: 0 0 2px rgba(0, 0, 0, 0.1);
    -webkit-animation: 500ms ease-in-out 0s normal none 1 running fadeInDown;
    animation: 500ms ease-in-out 0s normal none 1 running fadeInDown;
    -webkit-transition: 0.6s;
    transition: 0.6s;
    background-color: var(--navbar-bg-color);
}

#mainNav .navbar-brand {
    position: relative;
    display: flex;
    align-items: center;
    padding: 0;
    margin-right: 30px;
}

#mainNav .navbar-brand img {
    height: 40px;
}

#mainNav .navbar-nav .nav-item .nav-link {
    font-size: 15px;
    /* padding: 0.75em 15px; */
    padding: 0.75em 13px;
    position: relative;
    font-weight: 500;
    color: var(--white-color);
    display: flex;
}

#mainNav .navbar-nav .nav-item .nav-link:hover,
#mainNav .navbar-nav .nav-item .nav-link.active {
    /* color: var(--white-color); */
}

/*Menu Right Side round buttons Start*/
.menu-round-btn>a {
    height: 31px;
    width: 31px;
    display: inline-flex;
    border-radius: 50%;
    background-color: var(--white-color);
    margin-right: 18px;
    align-items: center;
    justify-content: center;
    color: var(--heading-color) !important;
    padding: 7px !important;
}

/*Menu Right Side round buttons End */

/*---- Dropdown Menu CSS ----*/
.dropdown-item.active,
.dropdown-item:active {
    color: var(--heading-color);
}

.dropdown,
.dropend,
.dropstart,
.dropup {
    position: relative;
}

.navbar .dropdown .dropdown-toggle:after {
    margin-left: .375rem;
    border-top: none;
    border-right: none;
    border-bottom: none;
    border-left: none;
    font-family: Feather !important;
    content: "\e92e";
    background-repeat: no-repeat;
    background-size: .625rem .625rem;
    float: right;
}

.navbar .dropdown-menu a::after {
    transform: rotate(-90deg);
    position: absolute;
    right: 1.7rem;
    color: var(--body-font-color);
}

.dropdown-toggle {
    display: flex;
    align-items: center;
}

#librariesDropdown {
    background-color: var(--white-color);
    border: none;
    color: var(--heading-color) !important;
    border-radius: 3px;
    padding: 6.5px 21px !important;
    font-weight: 500;
    margin: 0.75em 0;
}

#mainNav .navbar-nav .nav-item .nav-link.dropdown-toggle svg {
    height: 17px;
}

#mainNav .navbar-nav .nav-item .nav-link svg {
    height: 25px;
    width: 27px;
    padding-top: 2px;
}

#mainNav .navbar-nav .nav-item .nav-link.theme-button1 svg {
    margin-right: 6px;
}

#mainNav .navbar-nav .dropdown-menu a {
    color: var(--body-font-color);
    padding: 8px 45px 8px 30px;
    font-size: 15px;
    display: flex;
    align-items: center;
    white-space: nowrap;
    position: relative;
}

.navbar .dropdown-menu .dropdown-toggle::after {
    color: var(--body-font-color);
}

#mainNav .navbar-nav .dropdown-menu a:hover,
.navbar .dropdown-menu .dropdown-toggle:hover::after,
#mainNav .navbar-nav .dropdown-menu a:focus {
    /* color: var(--heading-color); */
    background-color: transparent;
}

/* Header Navbar Left Side */
.header-nav-left-side {
    position: relative;
    padding-left: 10px;
    align-items: center;
}

.header-nav-left-side:before {
    height: 103px;
    width: 1px;
    background-color: rgba(255, 255, 255, 0.1);
    content: "";
    left: 0;
    position: absolute;
    top: -25px;
}

/* search box */
.header-nav-left-side form {
    width: 648px;
    height: 50px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    box-sizing: border-box;
    border-radius: 126px;
    position: relative;
}

.header-nav-left-side form .form-control {
    background-color: transparent;
    height: 46px;
    color: #D8D8D8;
    border: none;
}

.header-nav-left-side form button {
    background-color: transparent;
    border: none;
}

/* Search Bar Suggestion Box */
.search-bar-suggestion-box {
    background: var(--white-color);
    padding: 15px 26px;
    border-radius: .25rem;
    z-index: 9;
    position: absolute;
    width: 100%;
    left: 0;
    box-shadow: 0 0 8px 0 rgb(1 0 64 / 7%);
    top: 61px;
    max-height: 400px;
    overflow-y: auto;
    z-index: 999;
}

.search-bar-result-item a,
.search-bar-result-item.no-search-result-found {
    padding: 5px 0;
    display: flex;
    align-items: center;
}

.search-bar-result-item a img {
    height: 40px;
    border-radius: 5px;
    margin-right: 10px;
}

.appendBlogSearchList .search-bar-result-item a img {
    height: 35px;
    max-width: 50px;
}

/* Search Bar Suggestion Box */

/* Header Right Navbar Right Side */
.header-right-nav ul {
    display: flex;
}

.header-right-nav li {
    margin-left: 10px;
    position: relative;
}

.header-right-nav .theme-button1,
.header-right-nav .theme-button2 {
    padding: 10px 22px;
}

.menu-language-btn.dropdown .dropdown-toggle:after {
    position: absolute;
    right: -17px;
    color: rgba(255, 255, 255, 0.47);
    display: none;
}

.menu-language-btn>a {
    margin-right: 18px;
    margin-left: 18px;
}

.menu-language-btn img {
    height: 27px;
    width: 27px;
}

.menu-language-btn .dropdown-menu .dropdown-item img {
    height: 20px;
    width: 20px;
    margin-right: 7px;
}

/*Notification dropdown tab css Start*/
.menu-notification-btn .dropdown-menu {
    min-width: 18.5rem;
}

.menu-notification-tab-content {
    height: 230px;
    overflow-y: auto;
}

.menu-notification-tab-content>.tab-pane {
    height: 230px;
}

.menu-notification-tab-content.tab-content>.active {
    min-height: 100%;
}

.no-notification-found-box {
    min-height: 100%;
    display: flex !important;
    flex-direction: column;
    justify-content: center;
}

.menu-notification-tab-list {
    background-color: #E3E4F2 !important;
    height: 44px;
    padding: 6px 8px;
    border-radius: 57px !important;
    display: flex;
    margin: 1rem 24px .5rem;
    justify-content: space-between;
}

.menu-notification-tab-list button {
    border-radius: 70px !important;
    font-size: 12px !important;
    max-height: 32px;
    padding: 7px 25px !important;
    color: var(--heading-color) !important;
    font-weight: 500 !important;
}

.menu-notification-tab-list .nav-link.active,
.menu-notification-tab-list .show>.nav-link {
    color: var(--heading-color);
    background-color: var(--white-color);
}

.menu-notification-tab-content .message-user-item {
    /* flex-wrap: wrap; */
    border-bottom: 1px solid #F0F0F0;
    margin-bottom: 10px;
    padding: .5rem 24px !important;
    white-space: normal !important;
}

.menu-notification-tab-content .tab-pane .message-user-item:last-child {
    border-bottom: none;
}

.menu-notification-tab-content .message-user-item .user-img-wrap,
.menu-notification-tab-content .message-user-item .user-img-wrap img {
    height: 40px;
    width: 40px;
    border-radius: 50%;
    overflow: hidden;
}

.menu-notification-tab-content .message-user-notification-box {
    background-color: #45C881;
    color: var(--white-color);
    font-size: 10px;
}

/*Notification dropdown tab css End*/

/* Dropdown User Info */
.dropdown-user-info .message-user-item {
    padding: 12px 24px;
}

/* Dropdown User Info */

/*Cart Btn CSS*/
.menu-cart-btn .badge {
    position: absolute;
    width: 16px;
    height: 16px;
    top: 5px !important;
    background: var(--theme-color) !important;
    left: 27px !important;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 8px;
}

.menu-notification-btn .badge {
    background: #45C881 !important;
}

.menu-message-btn .badge {
    background: #FF4444 !important;
}

/*Cart Btn CSS*/

/* Menu User Btn CSS Start*/
.menu-user-btn .dropdown-menu {
    min-width: 14.7rem;
}

.menu-user-btn a {
    margin-right: 0;
    overflow: hidden;
    padding: 0 !important;
}

.menu-user-btn a>img {
    height: auto;
    min-width: 31px;
    min-height: 31px;
}

.menu-user-btn img {
    height: 31px;
    width: 31px;
}

.user-dropdown-item-box {
    border-top: 1px solid #EEE9E9;
    padding-top: 10px;
    margin: 10px 24px 0;
}

.menu-user-btn .message-user-item:hover {
    background-color: transparent;
}

.menu-user-btn .dropdown-menu a {
    padding: 8px 45px 8px 0 !important;
}

.menu-user-btn .dropdown-menu a .iconify {
    margin-right: 15px;
    font-size: 18px;
}

/* Menu User Btn CSS End*/

/*User Login or Not Login Function for Menu Start*/
.menu-notification-btn,
.menu-message-btn,
.menu-user-btn,
.isLoginMenu .menu-sign-in-btn {
    display: none;
}

.isLoginMenu .menu-notification-btn,
.isLoginMenu .menu-message-btn,
.isLoginMenu .menu-user-btn {
    display: block;
}

/*User Login or Not Login Function for Menu End*/

/*Multilevel Dropdown Menu CSS Start*/
/* ============ desktop view ============ */
@media all and (min-width: 992px) {
    .navbar-nav .dropdown-menu {
        position: absolute !important;
    }

    .dropdown-menu li {
        position: relative;
    }

    .dropdown-menu .submenu {
        display: block;
        visibility: hidden;
        opacity: 0;
        position: absolute;
        left: 100%;
        top: 10px;
    }

    .dropdown-menu .submenu-left {
        right: 100%;
        left: auto;
    }

    .dropdown-menu>li:hover {
        background-color: transparent;
    }

    .dropdown-menu>li:hover>.submenu {
        visibility: visible;
        opacity: 1;
        top: -7px;
    }

    /* Show Dropdown Menu on Hover instead Click Start */
    .navbar-expand-lg .navbar-nav .dropdown-menu {
        position: absolute;
    }

    .navbar .dropdown-menu {
        display: block;
        visibility: hidden;
        opacity: 0;
        transform: translateY(20px);
        transition: all .3s ease-in;
    }

    .navbar .dropdown:hover>.dropdown-menu {
        transform: scaleY(1);
        opacity: 1;
        visibility: visible;
    }

    .navbar .dropdown:hover>.dropdown-menu:hover>.dropdown-menu {
        display: block;
        transform: scaleY(1);
        opacity: 1;
        visibility: visible;
    }

    .navbar .nav-item .dropdown-menu {
        display: block;
        visibility: hidden;
        opacity: 0;
        transform: translateY(20px);
        transition: all .3s ease-in;

        margin-top: 0;
        box-shadow: 0 0 8px 0 rgba(1, 0, 64, 0.07);
        border: 0;
    }

    .navbar .nav-item:hover>.dropdown-menu {
        display: block;
    }

    /* For Specific Dropdown Menu Extra Space for top */
    li.nav-item.dropdown.dropdown-top-space {
        padding: 0.75em 0;
    }

    li.nav-item.dropdown.dropdown-top-space .nav-link {
        margin-top: 0.75em;
        margin-bottom: 0.75em;
    }

    li.nav-item.dropdown.menu-round-btn.dropdown-top-space .nav-link {
        margin-top: 0;
        margin-bottom: 0;
    }

    /* Show Dropdown Menu on Hover instead Click End */
}

/*Menu Mobile Responsive CSS Start*/
.navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba(255, 255, 255, 1)' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

.navbar-toggler:focus {
    box-shadow: none;
}

/*Menu Mobile Responsive CSS End*/

/* ============ desktop view .end// ============ */

/* ============ small devices ============ */

.menu-show-user-avatar-desktop {
    /* display: none; */
}

.menu-show-user-avatar-mobile {
    /* display: none; */
}

@media (max-width: 991px) {

    .dropdown-menu .dropdown-menu {
        margin-left: 0.7rem;
        margin-right: 0.7rem;
        margin-bottom: .5rem;
    }

}

/* ============ small devices .end// ============ */
/*Multilevel Dropdown Menu CSS End*/

/*-------------------------------------------
            5. Scroll To Top Area
-------------------------------------------*/
#scroll {
    position: fixed;
    right: 10px;
    bottom: 40px;
    cursor: pointer;
    width: 20px;
    height: 20px;
    display: none;
    z-index: 10;
    font-size: 20px;
    text-align: center;
    color: var(--white-color);
    line-height: 48px;
}

/*-------------------------------------------
    5. Scroll To Top Area End
-------------------------------------------*/

/*-------------------------------------------
    6. Preloader Area Start
-------------------------------------------*/
#preloader {
    background-color: #fff;
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 9999999999999999;
}

#preloader_status,
#inner-status {
    width: 100%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    height: 100%;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
}

/*-------------------------------------------
    6. Preloader Area End
-------------------------------------------*/

/*-------------------------------------------
    7. Header Area
-------------------------------------------*/
.hero-area .section-overlay {
    min-height: 795px;
    padding: 40px 0 !important;
}
@media (max-width: 430px) {
    .hero-area .section-overlay {
        min-height: 795px;
        padding: 90px 0 !important;
    }
}
.hero-content {
    /* height: 100%; */
    display: flex;
    flex-direction: column;
    justify-content: center;
}

/* Come for learn */
.come-for-learn-text {
    font-weight: 600;
    font-size: 12px;
    line-height: 154.5%;
    letter-spacing: 0.89em;
    /* text-transform: uppercase; */
    color: rgba(255, 255, 255, 0.55);
    border-bottom: 1px solid rgba(255, 255, 255, 0.17);
    width: 59%;
    margin-bottom: 30px;
}

/* Ellipse 2 */
.come-for-learn-text span {
    position: relative;
    margin-right: 17px;
}

.come-for-learn-text span:after {
    position: absolute;
    content: "";
    width: 7px;
    height: 7px;
    background-color: var(--orange-color);
    border-radius: 50%;
    top: 5px;
    right: -15px;
    -webkit-animation: flicker 3s linear infinite;
    animation: flicker 3s linear infinite;
}

@-webkit-keyframes flicker {

    0%,
    19.999%,
    22%,
    62.999%,
    64%,
    64.999%,
    70%,
    100% {
        opacity: 0.99;
        text-shadow: -1px -1px 0 rgba(255, 255, 255, 0.4), 1px -1px 0 rgba(255, 255, 255, 0.4), -1px 1px 0 rgba(255, 255, 255, 0.4), 1px 1px 0 rgba(255, 255, 255, 0.4), 0 -2px 8px, 0 0 2px, 0 0 5px #ff7e00, 0 0 15px #ff4444, 0 0 2px #ff7e00, 0 2px 3px #000;
    }

    20%,
    21.999%,
    63%,
    63.999%,
    65%,
    69.999% {
        opacity: 0.4;
        text-shadow: none;
    }
}

@keyframes flicker {

    0%,
    19.999%,
    22%,
    62.999%,
    64%,
    64.999%,
    70%,
    100% {
        opacity: 0.99;
        text-shadow: -1px -1px 0 rgba(255, 255, 255, 0.4), 1px -1px 0 rgba(255, 255, 255, 0.4), -1px 1px 0 rgba(255, 255, 255, 0.4), 1px 1px 0 rgba(255, 255, 255, 0.4), 0 -2px 8px, 0 0 2px, 0 0 5px #ff7e00, 0 0 15px #ff4444, 0 0 2px #ff7e00, 0 2px 3px #000;
    }

    20%,
    21.999%,
    63%,
    63.999%,
    65%,
    69.999% {
        opacity: 0.4;
        text-shadow: none;
    }
}

/*Come for learn Text Glow effect*/

.hero-heading {
    font-weight: 300;
    font-size: 65px;
    line-height: 100.5%;
    /* text-transform: capitalize; */
    color: var(--white-color);
}

.hero-heading>span {
    /* text-transform: capitalize!important; */
    font-weight: 500;
}

.hero-content p {
    font-weight: normal;
    font-size: 18px;
    line-height: 154.5%;
    color: rgba(255, 255, 255, 0.72);
}

.hero-content p,
.hero-btns {
    margin-top: 30px;
}

.hero-btns .theme-btn {
    margin-right: 10px;
}

/*Hero Heading Text Effect Start*/
.word {
    position: absolute;
    opacity: 0;
    /* text-transform: capitalize; */
}

.main-middle-text {
    margin-right: 15px;
}

.letter {
    display: inline-block;
    position: relative;
    float: left;
    transform: translateZ(25px);
    transform-origin: 50% 50% 25px;
    text-transform: initial;
}

.letter.out {
    transform: rotateX(90deg);
    transition: transform 0.32s cubic-bezier(0.55, 0.055, 0.675, 0.19);
}

.letter.behind {
    transform: rotateX(-90deg);
}

.letter.in {
    transform: rotateX(0deg);
    transition: transform 0.38s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/*Hero Heading Text Effect End*/

/*-------------------------------------------
    7. Header Area End
-------------------------------------------*/

/*-------------------------------------------
    8. Special Feature Area Start
-------------------------------------------*/
.special-feature-area {
    margin-top: -50px;
}

.single-feature-item {
    background-color: var(--white-color);
    box-shadow: 0 6px 21px rgba(21, 3, 89, 0.08);
    border-radius: 3px;
    padding: 43px 30px;
}

.single-feature-item p {
    font-weight: normal;
    font-size: 16px;
    line-height: 154%;
    color: var(--gray-color);
}

.single-feature-item h6 {
    padding-bottom: 10px;
}

.top-categories-area .feature-img-wrap {
    height: 60px;
    width: 60px;
    background-color: #d5d5d5;
    border-radius: 50%;
}

/*-------------------------------------------
    8. Special Feature Area End
-------------------------------------------*/

/*-------------------------------------------
    9. Course Slider Area Start
-------------------------------------------*/
/*Course Item*/
.course-item {
    box-shadow: 0 6px 21px rgba(21, 3, 89, 0.08);
    padding: 10px;
    margin-bottom: 25px;
    min-height: 417px;
}

.course-item-upcoming {
    min-height: 380px !important;
}

.course-img-wrap img {
    max-width: 100%;
    transform: scale(1);
    min-height: 221px;
    min-width: auto;
}

.course-item:hover .course-img-wrap img {
    transform: scale(1.1);
}

.course-tag.badge {
    padding: 7px 13px;
    top: 30px;
    left: 30px;
    z-index: 9;
}

.course-item .card-body {
    flex: 1 1 auto;
    padding: 30px 20px 20px;
}

.course-rating {
    margin-bottom: 15px;
}

.rating-list li {
    margin: 0 3px;
    font-size: 13px;
    color: #E1E1E1;
}

.instructor-my-course-item .rating-list {
    margin-left: 5px;
}

.course-title {
    font-size: 19px;
    line-height: 24px;
    margin-bottom: 20px;
}

.instructor-name-certificate {
    line-height: 17px;
    color: #9696B0;
    margin-bottom: 11px;
}

.course-rating ul li svg {
    padding-bottom: 3px;
    height: 20px;
}

.star-full .iconify {
    color: var(--color-yellow);
}

/* Start Ratings CSS Start */
.star-ratings {
    unicode-bidi: bidi-override;
    color: #E1E1E1;
    font-size: 64px;
    position: relative;
    margin: 0;
    padding: 0;
    font-size: 20px;
    width: auto !important;
}

.star-ratings .fill-ratings {
    color: var(--color-yellow);
    padding: 0;
    position: absolute;
    z-index: 1;
    display: block;
    top: 0;
    left: 0;
    overflow: hidden;
}

.star-ratings .fill-ratings span {
    display: inline-block;
}

.star-ratings .empty-ratings {
    padding: 0;
    display: block;
    z-index: 0;
    font-size: 20px;
}

/* Start Ratings CSS End */

.course-item-hover-btns {
    right: 25px;
    top: 28px;
    z-index: 9;
    opacity: 0;
}

.course-item-hover-btns .course-item-btn {
    color: var(--white-color);
}

.course-item-hover-btns .course-item-btn svg {
    height: 18px;
}

.course-item:hover .course-item-hover-btns {
    opacity: 1;
}

.course-img-wrap {
    border-radius: 4px;
    min-height: 221px;
    width: 100%;
    position: relative;
}

.course-item .course-img-wrap:before {
    background-color: rgba(41, 25, 100, 0.5);
    z-index: 9;
    height: 100%;
    width: 100%;
    position: absolute;
    content: "";
    opacity: 0;
}

.course-item:hover .course-img-wrap:before {
    opacity: 1;
}

/* Tab navlist css */
.course-tab-nav-wrap {
    padding-bottom: 15px;
    margin-bottom: 30px;
}

.course-tab-nav-wrap .nav-tabs.tab-nav-list li {
    margin-right: 5px;
}

.course-tab-nav-wrap .tab-nav-list.nav-tabs .nav-link {
    margin-bottom: -1px;
    border: 1px solid transparent;
    font-size: 15px;
    line-height: 23px;
    /* text-transform: capitalize; */
    font-weight: 500;
    color: var(--body-font-color);
    position: relative;
    padding: 0 9px;
    margin-left: 8px;
}

.course-tab-nav-wrap .nav-tabs .nav-item.show .nav-link,
.course-tab-nav-wrap .nav-tabs .nav-link.active {
    color: #000 !important;
}

/* Tab navlist css */

/*Course Slider Navigation Start*/
.course-slider-items .owl-nav button {
    background: var(--white-color) !important;
    height: 50px;
    width: 50px;
    border-radius: 50% !important;
    color: var(--gray-color);
    top: 19%;
    box-shadow: 0 9px 15px 0 rgb(6 6 103 / 19%);
}

.course-slider-items .owl-nav button:hover {
    color: var(--theme-color) !important;
}

.course-slider-items button.owl-prev {
    position: absolute;
    left: -24px;
}

.course-slider-items button.owl-next {
    position: absolute;
    right: -24px;
}

/*Course Slider Navigation End*/

/*-------------------------------------------
    9. Course Slider Area End
-------------------------------------------*/

/*-------------------------------------------
    10. Video Area Start
-------------------------------------------*/
.video-area-right .section-heading {
    width: 100%;
}

.video-floating-img-wrap {
    padding-right: 55px;
}

.video-floating-img-wrap img {
    left: -85px;
    top: 0;
}

.video-area-right .theme-button1:hover {
    border-color: var(--theme-color) !important;
}

.video-instructor-small-img-list ul li {
    background-color: var(--white-color);
    height: 53px;
    width: 53px;
    border: 2px solid var(--white-color);
    margin-left: -8px;
}

.video-instructor-small-img-list ul li:first-child {
    margin-left: 0;
}

.video-instructor-small-img-list ul li:last-child {
    background-color: var(--orange-color);
}

.video-instructor-small-img-list ul li img {
    height: 53px;
    width: 100%;
    border-radius: 50%;
}

/*Video area Play btn css */
.video-area-left {
    padding-right: 22px;
}

.play-btn {
    height: 86px;
    width: 86px;
    border-radius: 50%;
    animation: shadow-pulse 3s infinite;
    background-color: var(--theme-color);
    display: flex;
    justify-content: center;
    align-items: center;
}

/*Video Play Button Effect */
@keyframes shadow-pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(117, 79, 254, 0.3);
    }

    100% {
        box-shadow: 0 0 0 25px rgba(117, 79, 254, 0);
    }
}

@keyframes shadow-pulse-big {
    0% {
        box-shadow: 0 0 0 0 rgba(117, 79, 254, 0.2);
    }

    100% {
        box-shadow: 0 0 0 50px rgba(117, 79, 254, 0);
    }
}

/*Video Play Button Effect */

/*-------------------------------------------
    10. Video Area End
-------------------------------------------*/

/*-------------------------------------------
    11. Top Categories Area Start
-------------------------------------------*/
.top-cat-item {
    padding: 39px 57px 39px 39px;
}

.section-heading-img {
    margin-bottom: 18px;
}

/*-------------------------------------------
    11. Top Categories Area End
-------------------------------------------*/

/*-------------------------------------------
    12. Top Instructors Area Start
-------------------------------------------*/
.top-instructor-area .section-heading-img,
.courses-area .section-heading-img,
.passionate-team-member-area .section-heading-img {
    margin-right: 15px;
}

.section-left-title-with-btn .theme-btn {
    margin-bottom: 40px;
}

/*Instructor Item start*/
.top-instructor-content-wrap .search-instructor-item,
.organization-single-tab-area .search-instructor-item {
    box-shadow: 0 6px 21px rgb(21 3 89 / 8%);
}

.card-title {
    margin-bottom: 13px;
}

.instructor-designation {
    font-size: 12px;
    line-height: 106.5%;
    color: #9696B0;
    margin-bottom: 15px;
}

.instructor-bottom-item {
    line-height: 15px;
    color: var(--heading-color);
}

.instructor-bottom-item img {
    padding-bottom: 3px;
    margin-right: 3px;
}

/*Instructor Item end*/
/*-------------------------------------------
    12. Top Instructors Area End
-------------------------------------------*/

/*-------------------------------------------
    13. Testimonial Area Start
-------------------------------------------*/
.customers-says-area .section-heading {
    margin-bottom: 80px !important;
}

/*Testimonial Item*/
.testimonial-item {
    padding-right: 46px;
}

.testimonial-top-content {
    border-bottom: 1px solid rgba(255, 255, 255, 0.13);
    margin-bottom: 35px;
    padding-bottom: 20px;
}

.testimonial-content h6 {
    line-height: 17px;
    color: rgba(255, 255, 255, 0.6);
    margin-bottom: 10px;
}

.testimonial-content p {
    line-height: 14px;
    letter-spacing: 0.38em;
    color: #6E6E96;
}

.testimonial-bottom-content p {
    line-height: 27px;
    color: rgba(255, 255, 255, 0.49);
    margin: 20px 0;
    font-weight: 300;
}

/*-------------------------------------------
    13. Testimonial Area End
-------------------------------------------*/

/*-------------------------------------------
    14. Achievement Area Start
-------------------------------------------*/
.achievement-area {
    padding: 90px 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.11);
    z-index: 99;
}

.achievement-item {
    padding: 0 35px 0 18px;
}

/*-------------------------------------------
    14. Achievement Area End
-------------------------------------------*/

/*-------------------------------------------
    15. Course Instructor and Support Area Start
-------------------------------------------*/
.instructor-support-item {
    box-shadow: 0 1px 15px rgba(2, 2, 43, 0.07);
    padding: 90px;
    transform: scale(1.00);
}

.instructor-support-item:hover {
    transform: scale(1.1);
    box-shadow: 0 1px 62px rgba(2, 2, 43, 0.07);
}

.instructor-support-img-wrap {
    height: 86px;
    width: 86px;
    overflow: hidden;
    margin: 0 auto 20px;
}

.instructor-support-img-wrap img {
    height: 86px;
    width: 86px;
    min-width: 100%;
}

.instructor-support-item p {
    margin: 20px 0 22px;
}

.course-instructor-support-wrap div:nth-child(2) .instructor-support-item .theme-btn {
    background-color: var(--orange-color);
    border-color: var(--orange-color);
}

.course-instructor-support-wrap div:nth-child(2) .instructor-support-item .theme-btn:hover {
    background-color: var(--white-color);
    color: var(--orange-color) !important;
}

/*Client Logo Area CSS*/
.client-logo-area {
    padding-top: 90px;
}

.client-logo-item {
    margin: 20px;
    height: 41px;
    overflow: hidden;
}

.client-logo-item img {
    max-height: 41px;
}

/*-------------------------------------------
    15. Course Instructor and Support Area End
-------------------------------------------*/

/*-------------------------------------------
    16. FAQ Area Start
-------------------------------------------*/
.home-page-faq-area .section-heading {
    width: 100%;
    text-align: center;
}

.section-title {
    text-align: center;
}

.faq-page-area .faq-tab-nav-item {
    text-align: center;
}

.faq-tab-nav-wrap .nav-tabs .nav-link.active::after {
    display: none;
}

/* Accordion CSS Start */
.faq-area .accordion-item {
    margin-bottom: 20px;
}

.faq-area .accordion-header {
    margin-bottom: 0;
}

.accordion-button {
    background-color: #F3F3F3;
    border-radius: 2px;
    line-height: 28px;
    color: var(--heading-color);
    /* text-transform: capitalize; */
}

.accordion-button:focus {
    z-index: 3;
    border-color: transparent;
    outline: 0;
    box-shadow: none;
}

.accordion-button:not(.collapsed) {
    color: var(--theme-color);
    background-color: #F3F3F3;
}

.home-page-faq-area .accordion-button:not(.collapsed) {
    color: var(--theme-color);
    background-color: #F3F3F3;
}

.accordion-button:not(.collapsed) {
    box-shadow: none;
}

.accordion-item {
    border: none;
}

.accordion-header {
    margin-bottom: 30px;
}

.accordion-collapse {
    box-shadow: 0 4px 7px rgba(31, 16, 87, 0.12);
}

.accordion-body {
    background: var(--white-color);
    border-radius: 2px;
    padding: 30px;
    line-height: 25px;
    color: var(--gray-color);
}

.accordion-button::after {
    flex-shrink: 0;
    width: 1.25rem;
    height: 1.25rem;
    margin-left: auto;
    /* Default Arrow Icon for Accordion */
    content: "\e92e";
    transition: transform .3s ease-in-out;
    border-left: none;
    font-family: Feather !important;
    background-image: none;
    background-repeat: no-repeat;
    background-size: 0.625rem 0.625rem;
    float: right;
}

.faq-tab-content .accordion-button::after {
    content: "\e90d";
}

.accordion-button:not(.collapsed)::after {
    background-image: none;
}

/* Accordion CSS End */

/*FAQ Right Side*/
.still-no-luck {
    width: 320px;
    padding: 40px;
    bottom: 60px;
    left: 60px;
    box-shadow: 0 6px 21px rgba(21, 3, 89, 0.08);
}

.still-no-luck h6 {
    margin-bottom: 13px;
}

.still-no-luck p {
    line-height: 25px;
}

/*FAQ Area shape*/
.faq-area-shape {
    position: absolute;
    width: 934px;
    height: 483px;
    left: 0;
    top: -180px;
    background: linear-gradient(180deg, rgba(255, 192, 20, 0.15) 0%, rgba(252, 128, 104, 0.15) 48.44%, rgba(147, 116, 251, 0.15) 100%);
    filter: blur(200px);
    backdrop-filter: blur(200px);
    z-index: -9;
    margin-right: auto;
    margin-left: auto;
    right: 0;
}

/* Support Tickets Page Area Start */
.support-tickets-page .accordion-button:not(.collapsed),
.support-tickets-page .accordion-button {
    background-color: var(--white-color);
}

.support-tickets-page .accordion-collapse {
    box-shadow: 0 4px 13px rgba(31, 16, 87, 0.05);
}

.support-tickets-page .accordion-body,
.faq-page-area .accordion-body {
    padding: 0 30px 30px;
}

.support-tickets-page .accordion-button:not(.collapsed)::after {
    background-image: none !important;
}

.support-tickets-page .accordion-button::after {
    content: "\e90d";
}

.is-that-helpful-btns .theme-btn {
    margin: 0 5px;
}

.is-that-helpful-btns {
    margin-top: 20px;
}

.create-tickets-btns .theme-btn {
    margin-right: 10px;
}

/* Support Tickets Page Area End */

/* Create Tickets Page Start */
.create-tickets-page .upload-img-btn-wrapper button {
    color: var(--white-color) !important;
}

#view_ticket .pagination {
    margin-top: 15px;
}

/* Create Tickets Page End */

/* Ticket Details Page */
.instructor-ticket-content-wrap,
.ticket-details-box {
    border: 1px solid #EBEBEB;
}

.ticket-replies-wrap .ticket-reply-item:not(:last-child) {
    margin-bottom: 15px;
}

.ticket-reply-item {
    padding: 20px;
    border-radius: 4px;
}

.ticket-reply-item.ticket-reply-item-student {
    background-color: #F5F5F5;
}

.ticket-reply-item.ticket-reply-item-staff {
    background-color: rgba(117, 79, 254, 0.05);
}

.ticket-reply-content p,
.ticket-info-content p {
    font-size: 15px;
    line-height: 22px;
}

/* ticket-details-reply-form-box */
.ticket-details-reply-form-box .upload-img-btn-wrapper button {
    color: var(--white-color) !important;
}

.ticket-status-box {
    padding: 4px 16px;
    margin-left: 10px;
}

.ticket-info-content p {
    margin: 8px 0 0;
}

.ticket-info-content {
    margin: 20px 0 0;
}

.ticket-info-bottom-box {
    border-top: 1px solid #E5E8EC;
    margin-top: 20px;
}

/* Ticket Details Page */

/*-------------------------------------------
    16. FAQ Area End
-------------------------------------------*/

/*-------------------------------------------
    42. Instructor Discussion Page Area Start
-------------------------------------------*/

/* Instructor Discussion Leftside start */
.instructor-disscussion-page-leftside {
    padding-bottom: 6px;
}

.instructor-disscussion-page-leftside .message-user-top-part {
    padding: 20px 18px 13px;
}

.instructor-disscussion-page-leftside .search-message-box .input-group-text {
    border: none;
}

.instructor-disscussion-page-leftside .search-message-box input {
    background-color: #F5F5F5;
    border: none;
    height: 40px;
}

.instructor-disscussion-page-leftside .search-message-box .form-control:focus {
    background-color: #F5F5F5;
    border: none;
}

.instructor-disscussion-page-leftside .search-message-box.input-group {
    border-radius: 4px;
    background-color: #F5F5F5;
}

.message-user-list-wrap.instructor-discussion-list-wrap {
    padding: 0 4px;
}

.instructor-discussion-course-item.message-user-item {
    padding: 12px 15px;
    border-radius: 4px;
}

.instructor-discussion-course-item.message-user-item:hover {
    background-color: rgba(117, 79, 254, 0.04);
}

.instructor-discussion-course-item.message-user-item.active {
    background-color: rgba(117, 79, 254, 0.08);
    border-left: none;
}

.instructor-discussion-course-item .user-img-wrap {
    width: 67px;
    height: 49px;
    overflow: hidden;
}

.instructor-discussion-course-item .user-img-wrap img {
    max-width: 67px;
    max-height: 49px;
    min-width: 67px;
    min-height: 49px;
    width: 100%;
}

.instructor-discussion-course-item .message-user-notification-box {
    background-color: rgba(255, 68, 68, 1);
    min-height: 10px;
    min-width: 10px;
}

.instructor-discussion-course-item .message-user-list-wrap {
    padding: 0 10px;
}

/* Instructor Discussion Leftside end */

/* Instructor Discussion Rightside start */
.instructor-discussion-comment-block {
    padding: 0 30px 0;
    /* width: 100%; */
    margin-bottom: 42px;
    max-height: 1200px;
    overflow-y: auto;
}

.discussion-reply-block.instructor-discussion-reply-block {
    margin-left: 0;
}

/* Instructor Discussion Rightside end */
/*-------------------------------------------
    42. Discussion Page Area End
-------------------------------------------*/

/*-------------------------------------------
    17. Footer Area Start
-------------------------------------------*/
/* Footer Widget */
.footer-about img,
.footer-widget>h6 {
    margin-bottom: 30px;
}

.footer-about img {
    height: 40px;
}

.footer-widget .footer-widget-title {
    color: var(--white-color);
}

.footer-widget.footer-about p {
    font-weight: normal;
    font-size: 16px;
    line-height: 25px;
    color: var(--white-color);
}

/*Footer social */
.footer-social ul li a {
    margin-right: 10px;
}

.footer-social ul li a .iconify {
    color: var(--white-color);
    font-size: 25px;
}

.footer-links li a,
.footer-links ul li {
    display: flex;
    font-weight: normal;
    font-size: 16px;
    line-height: 25px;
    color: var(--white-color);
}

.footer-links li a:hover,
.footer-bottom-nav ul li a:hover {
    color: var(--white-color);
    text-decoration-line: underline;
}

.footer-links ul li {
    padding: 0 0 20px;
}

.footer-links ul li:last-child {
    padding-bottom: 0;
}

.footer-contact-info ul li img {
    margin-right: 22px;
}

.footer-contact-info ul li .iconify {
    margin-right: 22px;
    font-size: 23px;
    position: relative;
    top: 2px;
    color: var(--theme-color);
}

.copyright-wrapper {
    padding: 27px 0;
    border-top: 1px solid rgba(255, 255, 255, 0.14);
}

.copyright-text p {
    line-height: 20px;
}

.footer-bottom-nav ul li a {
    font-weight: 500;
    font-size: 13px;
    line-height: 154%;
    /* text-transform: uppercase; */
    color: var(--white-color);
    padding: 0 10px;
}

/*-------------------------------------------
    17. Footer Area End
-------------------------------------------*/

/*-------------------------------------------
    18. Course Single Area Start
-------------------------------------------*/
.course-single-page-header .section-overlay {
    min-height: 630px;
    display: flex;
    align-items: center;
    padding: 230px 0 130px;
}

.course-single-banner-content {
    width: 80%;
}

.page-banner-heading {
    line-height: 44px;
    font-size: 41px;
}

.page-banner-sub-heading {
    color: rgba(255, 255, 255, 0.6);
}

/*Course Single Details Area CSS Start*/
.course-single-details-area .tab-pane {
    padding: 40px 40px;
}

/* Share This Course */
.share-course-list {
    display: flex;
    justify-content: center;
}

.share-course-list li a {
    display: inline-flex;
    height: 40px;
    width: 40px;
    border: 1px solid var(--heading-color);
    justify-content: center;
    align-items: center;
    margin-right: 10px;
    border-radius: 50%;
}

.share-course-list li a:hover {
    border: 1px solid var(--theme-color);
}

/* Share This Course */

/*Tab Nav List*/
.course-single-details-left-content {
    margin-top: -30px;
    border-radius: 3px 3px 0 0;
}

.course-details-tab-nav-wrap {
    border-bottom: 1px solid rgba(0, 0, 0, 0.12);
    padding: 18px 0;
    margin-bottom: 0;
}

.course-details-tab-nav-wrap .tab-nav-list.nav-tabs .nav-link {
    line-height: 20px;
    padding: 0 8px;
    margin-left: 22px;
}

.course-details-tab-nav-wrap .nav-tabs .nav-link.active::after {
    background: var(--theme-color);
    top: 37px;
}

.nav-tabs .nav-link.active::after {
    content: "";
    position: absolute;
    width: 100%;
    height: 2px;
    /* background: var(--orange-color); */
    left: 0;
    right: 0;
    top: 12px;
}

/*Overview tab Start*/
.what-you-will-learn {
    padding-bottom: 30px;
    padding-top: 30px;
}

.what-you-learn-list-wrap ul {
    padding-right: 25px;
}

.what-you-learn-list-wrap ul li {
    display: flex;
    align-items: center;
    padding-bottom: 15px;
}

.check-wrap {
    width: 25px;
    height: 25px;
    border-radius: 50%;
    background-color: var(--color-light-green);
    color: var(--color-green);
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
}

.what-you-learn-list-wrap ul li p {
    margin-left: 10px;
}

/*Overview tab End*/

/*curriculum Tab Start*/
.curriculum-content .accordion-button {
    background-color: transparent;
    line-height: 19px;
    font-size: 18px;
    padding: 0;
}

.curriculum-content .accordion-collapse {
    box-shadow: none;
}

.curriculum-content .accordion-body {
    padding: 0 0 20px;
}

.curriculum-content .accordion-button::after {
    content: "\e92e";
}

/* ---------Curriculum Playlist Item Start--------- */
.play-list-item {
    margin-bottom: 15px;
}

.play-list-item.venobox,
.play-list-item.preview-enabled {
    cursor: pointer;
}

.show-preview {
    display: none;
    margin-right: 30px;
}

.play-list-item.preview-enabled .show-preview {
    display: block;
}

.play-list-left img {
    margin-right: 18px;
    height: 18px;
}

.play-list-left p {
    padding-top: 2px;
}

.play-list-item.venobox .play-list-left p,
.play-list-item.preview-enabled .play-list-left p,
.show-preview {
    color: var(--heading-color);
    text-decoration: underline;
    transition: .5s ease all;
}

/* Custom Venobox CSS Start */
.vbox-backdrop {
    background-color: rgba(0, 0, 0, 0.5) !important;
}

.vbox-title {
    display: none !important;
}

.vbox-close {
    right: 30px !important;
    top: 30px !important;
    color: var(--white-color) !important;
    background: none !important;
    display: flex !important;
    font-size: 18px !important;
    z-index: 999 !important;
    border: 1px solid gray;
    border-radius: 50%;
    height: 30px;
    width: 30px;
    padding: 0;
    justify-content: center;
}

@media (max-width: 1199px) {
    .vbox-close {
        color: black !important;
        opacity: 1 !important;
        background-color: var(--white-color) !important;
    }
}

/* Custom Venobox CSS End */

/*Curriculum Playlist Item End*/

/* ----------curriculum Tab End---------- */

/* ----------Course Single Review Tab Start---------- */
.review-tab-count-box {
    background: var(--white-color);
    box-shadow: 0 6px 21px rgba(21, 3, 89, 0.08);
    padding: 32px;
}

.review-tab-count-box .rating-list {
    justify-content: center;
    margin: 5px;
}

/* Review Progress Bar */
.barras {
    width: 100%;
    margin: 0 auto 6px;
}

.barra {
    width: 400px;
    height: 7px;
    background: #EEEBEB;
    margin: 10px;
    border-radius: 44px;
}

.barra-nivel {
    height: 7px;
    background: var(--theme-color);
    width: 1px;
    -webkit-transition: width 1s ease;
    -moz-transition: width 1s ease;
    transition: width 1s ease;
    border-radius: 44px;
}

.valor-nivel {
    line-height: 30px;
    color: var(--white-color);
    margin-left: 10px;
    font-size: 12px;
}

/* -----Customer Review Item Start---- */
.review-tab-top-part {
    margin-bottom: 45px;
}

.customer-review-item {
    padding: 50px 0 20px;
    margin-top: 30px;
    border-top: 1px solid rgba(0, 0, 0, 0.08);
}

.customer-review-part .customer-review-item:last-child {
    border-top: none;
}

.customer-review-item-img-wrap {
    height: 76px;
    width: 76px;
}

.customer-review-item-img-wrap img {
    height: 76px;
    width: 100%;
}

.customer-review-item .rating-list {
    margin-bottom: 10px;
    margin-top: 5px;
}

.customer-review-load-more-btn .load-more-btn {
    margin-left: 90px;
}

/* -----Customer Review Item End----- */

/* -----Instructor Item Start----- */
.teacher-tag {
    display: inline-block;
    padding: 5px;
    min-width: 72px;
    height: 26px;
    text-align: center;
    line-height: 15px;
}

.meet-instructor-item-wrap div:last-child .meet-instructor-item {
    margin-bottom: 0;
}

.meet-instructor-top-title {
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
}

.meet-your-instructor-right {
    border: 1px solid #E5E8EC;
    border-radius: 6px;
    padding: 5px;
}

.meet-instructor-extra-info-item {
    padding: 11px 10px;
}

.meet-instructor-extra-info-item .iconify {
    margin-right: 7px;
    font-size: 20px;
}

.meet-instructor-img-wrap {
    height: 50px;
    width: 50px;
    margin-right: 13px;
    border-radius: 50%;
    overflow: hidden;
}

.meet-instructor-img-wrap img {
    min-width: 50px;
    min-height: 50px;
    max-width: 100%;
}

.meet-your-instructor-content-part p {
    margin: 11px 0 0;
}

/* -----Instructor Item End----- */

/* -----Discussion Item Start----- */
.before-login-purchase-course-details .discussion-top-block,
.before-login-purchase-course-details .discussion-reply-block {
    display: none !important;
}

.discussion-top-block {
    margin-bottom: 30px;
}

.discussion-top-block {
    border: 1px solid #E5E8EC;
    padding: 25px;
}

.discussion-left-img-wrap {
    height: 50px;
    width: 50px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 26px;
}

.discussion-left-img-wrap img {
    min-width: 50px;
    min-height: 50px;
    max-width: 50px;
    max-height: 50px;
}

.discussion-righ-wrap .editor-wrap {
    display: none;
}

.discussion-righ-wrap.show-editor .editor-wrap {
    display: block;
}

.discussion-righ-wrap.show-editor .start-conversation-btn-wrap button {
    background-color: var(--gray-color);
    color: var(--white-color);
}

.course-info-box-wishlist-btns {
    justify-content: center;
}

/* Editor css */
.editor-wrap textarea {
    width: 100%;
}

.editor-wrap .tox .tox-menubar,
.editor-wrap .tox .tox-statusbar {
    display: none;
}

.tox-notifications-container {
    display: none;
}

.editor-wrap .tox .tox-editor-container {
    flex-direction: column-reverse;
}

.editor-wrap .tox-tinymce {
    border: 1px solid #E5E8EC;
    height: 200px !important;
    border-bottom-right-radius: 5px;
    border-bottom-left-radius: 5px;
    border-top-right-radius: 0;
    border-top-left-radius: 0;
}

.tox .tox-menubar+.tox-toolbar,
.tox .tox-menubar+.tox-toolbar-overlord {
    border-top: 1px solid #E5E8EC !important;
}

.tox .tox-toolbar,
.tox .tox-toolbar__overflow,
.tox .tox-toolbar__primary {
    background-color: #fcfcfc !important;
}

.tox .tox-toolbar__group {
    background: var(--white-color);
    border: 1px solid transparent;
    border-radius: 0;
}

.tox:not([dir=rtl]) .tox-toolbar__group:not(:last-of-type) {
    border: 1px solid #E5E8EC !important;
    border-radius: 5px;
    height: 36px;
}

.tox .tox-toolbar__group {
    margin: 0 10px 0 0 !important;
    padding: 0 !important;
}

.tox-tbtn.tox-split-button__chevron {
    display: none !important;
}

.tox .tox-tbtn {
    color: #222f3e;
    height: 36px !important;
    margin: -1px !important;
    outline: 0;
    padding: 0;
    width: 60px !important;
    border-right: 0 !important;
    border: 1px solid #E5E8EC;
}

.tox-toolbar__group .tox-tbtn:not(:last-child) {
    border-right: 1px solid #E5E8EC !important;
    border-radius: 0 !important;
}

.tox-toolbar__group .tox-split-button:not(:last-child) {
    border-right: 1px solid #E5E8EC !important;
    border-radius: 0 !important;
}

.tox .tox-toolbar__primary {
    background-image: none !important;
    padding: 13px 15px !important;
}

.tox .tox-split-button {
    margin: -1px !important;
}

.tox:not(.tox-tinymce-inline) .tox-editor-header {
    padding: 0;
}

.tox .tox-toolbar__group {
    border: 1px solid #E5E8EC;
    border-radius: 0;
    border-radius: 4px;
}

.start-conversation-btn-wrap {
    border: 1px solid #E5E8EC;
    background-color: #FCFCFC;
    height: 75px;
    display: flex;
    padding: 20px;
    align-items: center;
    border-top-right-radius: 5px;
    border-top-left-radius: 5px;
}

.start-conversation-btn-wrap button {
    background-color: #E7E7E7;
    height: 44px;
    padding: 7px 20px;
    border-radius: 5px;
    color: var(--heading-color);
    display: flex;
    align-items: center;
}

.start-conversation-btn-wrap button .iconify {
    margin-right: 15px;
    font-size: 26px;
}

/* Editor End */

/* Discussion Comment Block Start */
.single-comment-box {
    margin-bottom: 30px;
    border-bottom: 1px solid #E5E8EC;
    padding-bottom: 15px;
}

.discussion-comment-block .single-comment-box:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.discussion-comment-item {
    margin-bottom: 25px;
}

.discussion-inner-comment-item {
    margin-left: 45px;
}

.comment-content-part .comment-content-part-top {
    background-color: #F5F5F5;
    padding: 20px;
    border-radius: 6px;
}

.extra-info-left button {
    margin-right: 20px;
}

.extra-info-right div {
    margin-left: 20px;
}

.extra-info-right div .iconify {
    margin-right: 5px;
}

.discussion-comment-block {
    border: 1px solid #E5E8EC;
    padding: 20px;
}

.discussion-comment-left-img-wrap {
    margin-right: 15px;
    height: 30px;
    width: 30px;
    overflow: hidden;
    border-radius: 50%;
}

.discussion-comment-left-img-wrap img {
    min-height: 30px;
    min-width: 30px;
    max-height: 30px;
    max-width: 30px;
}

.extra-info-right .iconify {
    font-size: 13px;
}

.teacher-badge {
    margin-left: 10px;
}

.discussion-reply-block {
    margin-left: 45px;
    border-top: 1px solid #E5E8EC;
    margin-top: 20px;
    padding-top: 20px;
}

.discussion-reply-block form .theme-btn {
    float: right;
    height: 34px;
}

.discussion-inner-comment-item-2 {
    margin-left: 90px;
}

/* Discussion Comment Block End */

/* -----Discussion Item End----- */

/* ----------Course Single Review Tab End---------- */

/* --------course-single-details-right-content css---------- */
.course-single-details-right-content {
    margin-top: -300px;
    border-radius: 3px 3px 0 0;
    overflow: hidden;
    margin-left: 26px;
}

.course-single-details-area .accordion-button:not(.collapsed) {
    background-color: transparent;
}

.course-info-box {
    padding: 11px;
}

.course-info-box .video-area-left {
    padding-right: 0;
}

.course-info-video-img img {
    max-height: 331px;
}

.course-info-box .play-btn {
    height: 66px;
    width: 66px;
}

.course-info-box .play-btn img {
    height: 23px;
}

.course-info-box {
    padding-bottom: 50px !important;
}

.price-off {
    min-width: 65px;
    height: 24px;
    background: rgba(117, 79, 254, 0.16);
    border-radius: 44px;
}

/*Course Includes css*/
.course-includes-box,
.course-price-box,
.course-info-box .theme-btn,
.money-back-guarantee {
    padding: 0 11px;
}

.course-info-box .theme-btn {
    height: 50px;
}

.course-includes-box ul li span {
    margin-left: 8px;
}

.course-includes-box ul li {
    padding-bottom: 12px;
}

.course-includes-box ul li:last-child {
    padding-bottom: 0;
}

.course-info-box-wishlist-btns .theme-btn {
    border: 1px solid rgba(0, 0, 0, 0.19);
    padding: 10px 16px !important;
    font-weight: 500 !important;
}

.addToWishlist,
.addToCart {
    cursor: pointer;
}

/*Share this article css*/
.share-this-course-box {
    height: 90px;
    padding: 0 20px;
    margin-top: 20px;
}

.course-info-box {
    padding-bottom: 50px !important;
}

.share-this-course-box button:hover {
    color: var(--theme-color);
}

.share-this-course-box svg {
    font-size: 24px;
    margin-right: 10px;
}

/*Course Single Details Area CSS End*/

/*-------------------------------------------
    18. Course Single Area End
-------------------------------------------*/

/*-------------------------------------------
    19. Course Watch Page Area Start
-------------------------------------------*/

/* New Customized Player Start */
video#player {
    width: 100%;
    min-width: 100%;
}

/* New Customized Player End */

/* New Video Player Modal Start */
#newVideoPlayerModal .modal-dialog,
.VideoTypeModal .modal-dialog {
    position: relative;
}

#newVideoPlayerModal .modal-header,
.VideoTypeModal .modal-header {
    position: absolute;
    top: 0;
    right: 0;
}

#newVideoPlayerModal .btn-close,
.VideoTypeModal .btn-close,
.venoBoxTypeModal .btn-close {
    right: 30px;
    top: 30px;
    color: var(--white-color);
    background: none;
    display: flex;
    font-size: 18px;
    z-index: 9;
}

#newVideoPlayerModal .modal-dialog .modal-body,
.VideoTypeModal .modal-dialog .modal-body {
    padding: 0;
}

@media (min-width: 576px) {

    #newVideoPlayerModal .modal-dialog,
    .VideoTypeModal .modal-dialog {
        max-width: 740px;
    }
}

@media (max-width: 1199px) {

    #newVideoPlayerModal .btn-close,
    .VideoTypeModal .btn-close,
    .venoBoxTypeModal .btn-close {
        color: black;
        opacity: 1;
        background-color: var(--white-color);
    }
}

/* Modal Appear Effect Start */
#newVideoPlayerModal.modal.fade .modal-content,
.VideoTypeModal.modal.fade .modal-content,
.venoBoxTypeModal.modal.fade .modal-content {
    -webkit-transform: scale(0.9);
    -moz-transform: scale(0.9);
    -ms-transform: scale(0.9);
    transform: scale(0.9);
    opacity: 0;

    transition: all 0.2s ease-in-out;
}

#newVideoPlayerModal.modal.fade.show .modal-content,
.VideoTypeModal.modal.fade.show .modal-content,
.venoBoxTypeModal.modal.fade.show .modal-content {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    transform: scale(1);
    opacity: 1;
}

.modal-backdrop {
    opacity: 0;
    transition: all 0.2s ease-in-out;
}

#newVideoPlayerModal.modal-backdrop.show,
.VideoTypeModal.modal-backdrop.show,
.venoBoxTypeModal.modal-backdrop.show {
    opacity: 1;
}

#newVideoPlayerModal.modal.fade .modal-dialog,
.VideoTypeModal.modal.fade .modal-dialog,
.venoBoxTypeModal.modal.fade .modal-dialog {
    transition: -webkit-transform .2s ease-in-out;
    transition: transform .2s ease-in-out;
    transition: transform .2s ease-in-out, -webkit-transform .2s ease-in-out;
    -webkit-transform: translate(0, 0);
    transform: translate(0, 0);
}

#newVideoPlayerModal.modal.show .modal-dialog,
.VideoTypeModal.modal.show .modal-dialog,
.venoBoxTypeModal.modal.show .modal-dialog {
    -webkit-transform: translate(0, 0);
    transform: translate(0, 0);
}

/* Modal Appear Effect End */

/* PDF Reader Modal Start */
.pdf-reader-frame {
    height: 400px;
    width: 100%;
}

/* PDF Reader Modal End */

/* PDF Reader in Course Watch Page Start */
.course-watch-page-area .pdf-reader-frame {
    height: 600px;
}

/* PDF Reader in Course Watch Page End */


/* New Video Player Modal End */

.course-watch-banner-items li {
    display: inline-flex;
    align-items: center;
    margin: 0 10px;
}

.course-watch-banner-items li .iconify {
    color: var(--orange-color);
    font-size: 18px;
}

.course-watch-page-area .container-fluid {
    padding: 0 100px;
}

.course-watch-page-area .course-watch-right-side .accordion-button::after {
    position: absolute;
    left: 0;
}

.course-watch-right-side .valor-nivel {
    display: none;
}

.course-single-details-right-content {
    padding: 0;
}

.course-single-details-right-content .accordion-header {
    background-color: #F5F5F5;
    padding: 13px;
    margin-bottom: 15px;
}

.course-single-details-right-content .curriculum-content .accordion-button {
    font-size: 15px;
    padding: 0 0 0 20px;
}

.course-watch-right-content {
    padding: 0 !important;
}

.course-watch-right-side {
    background-color: var(--white-color);
    padding: 25px 23px;
    border: 1px solid #E5E8EC;
}

.course-watch-right-side .accordion-header {
    background-color: transparent !important;
    border: 1px solid #E5E8EC;
    padding: 0;
    margin-bottom: 0;
}

.course-watch-right-side .accordion-body {
    background-color: #FDFDFD !important;
}

.course-watch-right-side .accordion-item {
    margin-bottom: 15px;
}

.course-watch-right-side .accordion-header .accordion-button {
    padding: 12px !important;
    padding-left: 25px !important;
}

.course-watch-right-side .accordion-header .accordion-button::after {
    position: absolute;
    left: 12px;
}

.course-watch-right-side .accordion-header .accordion-button:not(.collapsed)::after {
    left: 5px;
}

.course-watch-right-side .accordion-header .accordion-button:not(.collapsed) {
    background-color: #F5F5F5;
}

.course-single-details-right-content .curriculum-content .accordion-body {
    background-color: #FDFDFD;
    padding: 14px 18px;
    border: 1px solid #E5E8EC;
    margin-bottom: 15px;
}

.course-watch-page-area .course-single-details-left-content .accordion-item {
    background-color: transparent;
}

.course-watch-page-area .course-single-details-left-content .accordion-body {
    background-color: transparent;
}

.course-watch-page-area .course-details-tab-nav-wrap .tab-nav-list.nav-tabs .nav-link {
    background: transparent;
}

.course-watch-page-area .course-single-details-right-content {
    padding: 25px;
    margin-left: 0;
}

.course-watch-page-area .course-single-details-right-content .curriculum-content .accordion-button {
    font-size: 14px;
    padding: 0 0 0 14px;
}

.course-watch-page-area .course-watch-right-side .barra {
    width: 100%;
    margin: 4px 0 10px;
}

.course-watch-page-area .course-watch-right-side .barra-nivel {
    background: var(--color-green);
}

.course-watch-page-area .course-watch-right-side .play-list-item {
    margin-bottom: 4px;
}

/* Complete, finished, continue to watch */
.watchFinishedCourse p {
    color: #94949A;
}

.watchFinishedCourse .iconify path {
    fill: var(--color-green);
}

.watchContinuingCourse p {
    color: var(--heading-color) !important;
    font-weight: 500;
    font-size: 14px;
}

.watchContinuingCourse .iconify path {
    fill: var(--heading-color);
}

/* course-watch-inner-title-wrap */
.course-watch-inner-title-right-part button {
    border: 1px solid var(--gray-color);
}

.course-watch-enrolled-wrap ul {
    display: flex;
    align-items: center;
}

.course-watch-enrolled-wrap ul li {
    width: 30px;
    height: 30px;
    margin-left: -9px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.course-watch-enrolled-wrap ul li:last-child {
    background-color: var(--orange-color);
    color: var(--white-color);
    font-size: 10px;
    border: 2px solid var(--white-color);
    text-align: center;
}

.course-watch-enrolled-wrap ul li img {
    border-radius: 50%;
    height: 30px;
    width: 30px;
    border: 2px solid var(--white-color);
}

.course-watch-page-area .course-details-tab-nav-wrap .tab-nav-list.nav-tabs .nav-link {
    padding: 0;
    margin-left: 0;
    margin-right: 20px;
}

.course-watch-page-area.course-single-details-area .tab-pane {
    padding: 30px 0 30px 0 !important;
}

.course-watch-page-area.course-single-details-area .review-tab-top-part {
    justify-content: flex-start !important;
}

.course-watch-page-area.course-single-details-area .review-progress-bar-wrap {
    margin-left: 40px;
}

.course-watch-page-area.course-single-details-area .course-watch-right-side .review-progress-bar-wrap {
    margin-left: 0;
}

.course-watch-page-area.course-single-details-area .valor-nivel {
    color: var(--heading-color);
}

.after-purchase-course-watch-tab .course-watch-certificate-img img {
    border: 1px solid #E4E6EB;
}

/* Custom Scrollbar for course watch rightside start */
.course-watch-right-accordion-wrap {
    max-height: 517px;
    min-height: 200px;
    overflow-y: auto;
    overflow-x: hidden;
    padding-right: 4px;
}

.course-watch-right-accordion-wrap>.accordion {
    height: auto;
}

.course-watch-right-side .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: var(--theme-color) var(--theme-color);
}

.course-watch-right-side .custom-scrollbar::-webkit-scrollbar {
    width: 4px;
    height: 8px;
}

.course-watch-right-side .custom-scrollbar::-webkit-scrollbar-track {
    background-color: var(--white-color);
}

.course-watch-right-side .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: var(--theme-color) !important;
}

.course-watch-right-side .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: var(--theme-color) !important;
}

.course-watch-right-side .custom-scrollbar:-webkit-scrollbar-corner,
.course-watch-right-side .custom-scrollbar::-webkit-scrollbar-track {
    background-color: #f1f1f1 !important;
}

/* Custom Scrollbar for cours watch rightside end */

/*Watch Course Give Rating Modal CSS Start*/
#writeReviewModal .modal-body {
    padding: 2rem 3rem 1rem;
}

#writeReviewModal .modal-footer {
    padding: 1rem 3rem;
}

.give-rating-group label {
    flex: .05;
    color: #E1E1E1;
    font-size: 24px;
    margin: 0 2px;
}

.btn-check.active+.give-rating-star {
    color: var(--color-yellow);
}

#writeReviewModal textarea.form-control {
    height: 130px;
}

#writeReviewModal .theme-button3,
.modal-back-btn {
    border: 1px solid #D8D8D8;
    background-color: var(--white-color);
    color: var(--body-font-color);
    margin-right: 5px;
}

#writeReviewModal .theme-button1 {
    border: 1px solid var(--theme-color);
}

#writeReviewModal .theme-button3:hover,
#writeReviewModal .theme-button1:hover,
.modal-back-btn:hover {
    border: 1px solid var(--theme-color);
    background-color: var(--white-color);
    color: var(--theme-color);
}

/*Watch Course Give Rating Modal CSS End*/

/* after-purchase-course-watch-tab Start*/

/* Quiz */

/* Multiple choice quiz */
.course-watch-quiz-title-right-side p:not(:last-child) {
    border-right: 1px solid #B5B4BD;
    margin-right: 26px;
    padding-right: 26px;
}

.course-watch-quiz-top-bar {
    border-bottom: 1px solid #EEEBEB;
    margin-bottom: 40px;
    padding-bottom: 12px;
}

.quiz-answer-progress-bar {
    margin-top: 15px;
}

.quiz-answer-progress-bar .barra {
    width: 200px;
}

.quiz-answer-progress-bar .valor-nivel {
    display: none;
}

.quiz-answer-progress-bar .barra-nivel {
    background: var(--color-green);
}


.multiple-quiz-answer-list-box {
    margin-bottom: 30px;
}

.multiple-quiz-block .form-check {
    margin-bottom: .75rem;
}

.multiple-quiz-block .form-check-label {
    color: var(--gray-color2);
}

.multiple-quiz-block .form-check .iconify {
    display: none;
    margin-left: 5px;
}

.multiple-quiz-block .form-check .form-check-label {
    display: inline-flex;
    align-items: center;
}

.multiple-quiz-block .given-answer-wrong .wrong-iconify {
    display: block !important;
}

.multiple-quiz-block .form-check.given-answer-right .correct-iconify {
    display: block;
}

.multiple-quiz-block .form-check.correct-answer-was .correct-iconify {
    display: block;
}

.given-answer-wrong .form-check-label {
    color: var(--orange-color);
}

.given-answer-right .form-check-label {
    color: var(--color-green);
}

.course-watch-quiz-btn-wrap {
    border-top: 1px solid #EEF1F4;
    padding-top: 20px;
}

/* Multiple choice quiz */

/* Quiz Leatherboard Area */
.student-own-leatherboard {
    background-color: var(--theme-color);
}

.merit-list-leatherboard {
    background-color: #FFBC2D;
}

.merit-list-leatherboard .leatherboard-item,
.student-own-leatherboard .leatherboard-item {
    background-color: var(--white-color);
}

.merit-list-crown-img-wrap {
    margin-left: 30px;
}

.all-leatherboard-wrap .course-watch-leatherboard-area:not(:last-child) {
    margin-bottom: 30px;
}

.course-watch-leatherboard-area {
    padding: 20px 18px;
    border: 1px solid #E5E8EC;
    border-radius: 6px;
}

.leatherboard-item {
    background-color: #F6F6F6;
    border-radius: 6px;
    padding: 10px 20px;
    margin-bottom: 10px;
}

.leatherboard-left {
    width: 45%;
}

.leatherboard-right {
    width: 50%;
}

.student-img-wrap {
    margin: 0 16px;
    height: 40px;
    width: 40px;
    border-radius: 50%;
    overflow: hidden;
}

.student-img-wrap img {
    min-height: 40px;
    min-width: 40px;
}

/* Quiz Leatherboard Area */

/* Quiz */

/* Course Watch Tab Resourses */
.watch-course-tab-inside-top-heading {
    border-bottom: 1px solid #E5E8EC;
    margin-bottom: 30px;
    padding-bottom: 20px;
}

#Resources .resource-list-text {
    margin-bottom: 10px;
}

/* Course Watch Tab Resourses */

/* Course Watch Noticeboard */
.course-watch-notice-board-wrap {
    border: 4px solid #E7E6E6;
    padding: 20px 30px;
}

.course-watch-notice-board-wrap .row {
    background-color: rgba(246, 246, 246, 0.7);
    padding: 50px 20px;
}

.notice-board-box {
    background-color: #FFF5D8;
    padding: 20px 25px 25px;
    word-break: break-all;
}

.course-watch-notice-board-wrap .row>div:nth-child(2n+1) .notice-board-box {
    background-color: #FFEBC3;
}

.notice-board-box h6 {
    margin: 20px 0 25px;
}

/* Course Watch Noticeboard */

/* Course Watch Live Class */
.course-watch-live-class-topic {
    max-width: 200px;
}

.course-watch-meeting-link .iconify {
    cursor: pointer;
    position: relative;
    top: -2px;
    margin-left: 5px;
}

.course-watch-live-class-wrap .live-class-list-nav-tabs {
    margin-bottom: 0;
}

/* Course Watch Live Class */

/* Course Watch Assignment */
.course-watch-assignment-content-right {
    border: 1px solid #E5E8EC;
    padding: 25px;
}

.course-watch-assignment-top-text {
    border-bottom: 1px solid #E5E8EC;
    padding: 0 0 20px;
    margin-bottom: 20px;
}

.course-watch-assignment-files .resource-list-text {
    margin-bottom: 10px;
    margin-top: 15px;
}

.course-watch-assignment-result-notes {
    width: 362px;
}

.course-watch-certificate-download-btns .theme-btn.default-hover-btn {
    margin-right: 10px;
}

.course-watch-certificate-download-btns .theme-btn.default-hover-btn.green-theme-btn {
    margin-right: 0;
}

/* Course Watch Assignment */

/* Course Watch Certificate */

.course-watch-certificate-img img {
    width: 100%;
    border-radius: 15.8px;
}

/* Course Watch Certificate */

/* Instructor Certificate Template page */
.instructor-certificate-template-page .certificate-list {
    border: 1px solid #E4E6EB;
    border-radius: 15.8px;
    overflow: hidden;
    margin-bottom: 25px;
}

.instructor-certificate-template-page .certificate-list.certificate-selected {
    border: 1px solid var(--theme-color);
}

/* InstructorCertificate Template page*/

/* after-purchase-course-watch-tab End */

/*-------------------------------------------
    19. Course Watch Page Area End
-------------------------------------------*/

/*-------------------------------------------
    20. Inner Page Banner/Header Area Start
-------------------------------------------*/
.page-banner-header .section-overlay {
    min-height: 409px;
    padding: 230px 0 130px;
}

.page-banner-header .section-overlay {
    min-height: 409px;
    padding: 200px 0 100px;
}

.our-gallery-area .section-sub-heading,
.our-history-area .section-sub-heading {
    font-size: 16px;
    line-height: 25px;
}

.page-banner-heading {
    position: relative;
}

.page-banner-heading>img {
    position: absolute;
    margin-left: 13px;
}

/*----- Breadcrumb CSS ----*/
.breadcrumb-item,
.breadcrumb-item a {
    color: rgba(231, 231, 231, 0.79);
    line-height: 22px;
    /* text-transform: capitalize; */
}

.breadcrumb-item a:hover {
    color: var(--white-color);
}

.breadcrumb-item.active {
    color: rgba(231, 231, 231, 0.79);
    text-decoration: underline;
}

/* Blank Page Banner Header/ Student profile banner style start */
.blank-page-banner-header .section-overlay {
    min-height: auto !important;
    padding: 0 !important;
}

.blank-page-banner-wrap {
    padding: 90px 0 80px;
    background-color: var(--page-bg);
    margin-top: 100px;
    min-height: 200px;
}

.blank-page-banner-header .breadcrumb-item,
.blank-page-banner-header .breadcrumb-item a,
.blank-page-banner-header .breadcrumb-item.active {
    color: var(--gray-color);
}

/* Blank Page Banner Header/ Student profile banner style end */

/*-------------------------------------------
    20. Inner Page Banner/Header Area End
-------------------------------------------*/

/*-------------------------------------------
    21. About Page Area Start
-------------------------------------------*/

/*Gallery Area CSS Start*/
.our-gallery-area .section-title {
    width: 71%;
    margin: 0 auto;
}

.gallery-img-wrapper {
    padding: 0 112px;
}

/*Gallery Area CSS End*/

/*Timeline/ Our History CSS Start*/
.our-history-area .section-title {
    width: 60%;
    margin: 0 auto;
}

.our-history-area .section-title .section-heading {
    width: 100%;
}

.container-timeline {
    position: relative;
    padding: 0;
    border-top: 1px solid #333145;
}

.container-timeline ul {
    margin: 0;
    padding: 0;
    padding-left: 20px;
    display: inline-block;
}

.container-timeline ul li {
    position: relative;
    text-align: center;
}

.container-timeline .history-content {
    list-style: none;
    position: relative;
    box-shadow: 0 6px 21px rgba(21, 3, 89, 0.08);
    counter-increment: wa-process-counter;
    background-color: var(--white-color);
    padding: 30px 30px;
    margin: 20px 12px 12px 12px;
}

.history-year {
    position: relative;
    font-size: 21px;
    line-height: 22px;
    width: 100%;
}

.history-year:before {
    content: "";
    position: absolute;
    height: 27px;
    width: 1px;
    background-color: #333145;
    top: -44px;
    left: 50%;
}

.history-year:after {
    content: "";
    position: absolute;
    height: 5px;
    width: 5px;
    background-color: #333145;
    border-radius: 50%;
    top: -45px;
    left: 49.5%;
}

@media (min-width: 1200px) {
    .container-timeline ul {
        display: flex;
        padding-left: 0;
        padding-top: 42px;
    }
}

/*Timeline CSS End*/
.upgrade-your-skills-area .section-heading {
    width: 100%;
}

/*-------------------------------------------
    21. About Page Area End
-------------------------------------------*/

/*-------------------------------------------
    22. Courses Page Area Start
-------------------------------------------*/

/*Courses Sidebar Area Start*/
.courses-sidebar-area {
    border-right: 1px solid #E5E8EC;
}

/*Sidebar filter box css*/
.search-tag-list {
    background-color: rgba(117, 79, 254, 0.08);
    border-radius: 33px;
    padding: 4px 15px;
    display: block;
    margin: 5px 5px 5px 0;
}

.filter-bar-search-box input {
    width: 86%;
}

.accordion-item.sidebar-inner-accordion-item {
    border-bottom: 0 !important;
}

.accordion-item.sidebar-inner-accordion-item .accordion-button {
    padding: .25rem 1.25rem .25rem 0;
    font-weight: 500 !important;
}

.accordion-item.sidebar-inner-accordion-item .inner-accordion-body {
    padding: 10px 0 0 !important;
}

.courses-sidebar-area .accordion-button:not(.collapsed),
.accordion-item.sidebar-inner-accordion-item .accordion-button:not(.collapsed) {
    border-bottom: 0;
}

.course-sidebar-area .accordion:last-child .accordion-item {
    border-bottom: 0 !important;
}

.courses-sidebar-area .accordion-header {
    margin-bottom: 0;
}

.courses-sidebar-area .accordion-button {
    background-color: var(--page-bg);
    border-radius: 0;
    color: var(--heading-color);
    font-size: 17px;
    font-weight: 500;
    padding: .55rem 1.25rem .55rem 0;
}

.courses-sidebar-area .accordion-button:not(.collapsed) {
    border-bottom: 1px solid #E5E8EC;
}

.courses-sidebar-area .accordion-button::after {
    width: 24px;
    height: 24px;
    border: 1px solid var(--body-font-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
}

.sidebar-inner-title .accordion-button::after {
    content: "\e9b1";
    border: none;
}

.sidebar-inner-title .accordion-button:not(.collapsed)::after {
    content: "\e996";
    transform: rotate(-180deg);
}

.courses-sidebar-area .sidebar-inner-title .accordion-button {
    font-size: 14px;
    font-weight: 400;
}

.courses-sidebar-area .sidebar-inner-title .accordion-button .iconify {
    margin-right: 12px;
}

.courses-sidebar-area .accordion-body {
    padding: 15px 0 15px;
    background-color: var(--page-bg);
}

.courses-sidebar-area .accordion-item {
    border-bottom: 1px solid #E5E8EC;
    border-left: 0;
}

.course-sidebar-accordion-item {
    background-color: var(--page-bg);
}

.course-sidebar-accordion-item .accordion-collapse {
    box-shadow: none;
}

.accordion-button:not(.collapsed) {
    color: var(--heading-color);
    background-color: var(--page-bg);
}

.sidebar-radio-item {
    font-size: 14px;
    color: var(--body-font-color);
    display: flex;
    justify-content: space-between;
    padding-right: 20px;
}

.sidebar-radio-item label.form-check-label .iconify {
    font-size: 13px;
    color: var(--color-yellow);
    position: relative;
    top: -2px;
    margin-right: 5px;
}

.sidebar-radio-item label {
    margin-left: 5px;
}

.sidebar-radio-item .form-check .form-check-input {
    position: relative;
    top: 1px;
}

/*Price Range slider*/

.range-value-box {
    display: flex;
    flex-wrap: wrap;
}

.ui-slider-horizontal {
    height: 4px;
    border: 0 !important;
    background: rgba(196, 196, 196, 0.31);
    border-radius: 51px;
}

.ui-slider-horizontal {
    width: 92%;
    margin: 30px auto 30px;
}

.ui-widget-header {
    background: var(--theme-color);
}

.ui-widget-header .ui-icon {
    background-image: none;
}

/*.range-value-wrap, .price-range-field {*/
.range-value-wrap {
    color: var(--body-font-color);
    font-size: 14px;
    border: 1px solid var(--body-font-color);
    padding: 0 10px;
    margin-right: 10px;
    margin-bottom: 10px;
    width: 122px;
}

.range-value-wrap-go-btn {
    color: var(--white-color);
    margin-bottom: 10px;
    padding: 5px;
    background-color: var(--theme-color);
    font-size: 12px;
    height: 34px;
    width: 32px;
    border: 1px solid var(--theme-color);
    justify-content: center;
}

.range-value-wrap-go-btn i {
    color: var(--white-color);
}

.range-value-wrap label {
    margin-bottom: 0;
}

.price-range-field {
    border: 0;
    background: transparent;
    margin-left: 3px;
    height: 32px;
    max-width: 60px;
}

.ui-slider-horizontal .ui-slider-handle {
    top: -4px;
    background-color: var(--theme-color) !important;
    border-radius: 50%;
    box-shadow: 0 2px 4px 1px rgb(0 0 0 / 25%);
    border: 1px solid var(--theme-color);
}

.ui-slider .ui-slider-handle {
    width: 12px;
    height: 12px;
}

input[type="number"] {
    -webkit-appearance: textfield;
    -moz-appearance: textfield;
}

/* Instructor consult search by name start */
.consult-instructor-search-box {
    display: inline-flex;
    width: 100%;
}

.range-value-wrap.instructor-consult-search-by-name-wrap {
    width: calc(93% - 32px) !important;
    /* width: 100%!important; */
}

.instructor-consult-search-by-name-wrap input {
    min-width: 100% !important;
}

/* Instructor consult search by name end */

/*Courses Sidebar Area End*/

/* Show all Courses Area Start*/
.bang {
    display: none;
}

.courses-filter-bar {
    border-bottom: 1px solid #E5E8EC;
    padding-bottom: 30px;
}

.filter-box {
    border: 1px solid #E5E8EC;
    padding: 0 0 0 10px;
    border-radius: 4px;
    height: 40px;
    display: inline-flex;
}

.sidebar-filter-btn,
.filter-bar-search-box {
    border: 1px solid #E5E8EC;
    height: 40px;
    border-radius: 4px;
    padding: 5px 15px;
}

.sidebar-filter-btn:hover {
    border: 1px solid var(--theme-color);
    color: var(--heading-color);
}

.filter-box-short-icon p,
.filter-box .form-select {
    line-height: 22px;
}

.filter-box .form-select {
    height: 40px;
    padding-top: .25rem;
    padding-bottom: .25rem;
    padding-left: .5rem;
    font-size: 15px;
    min-width: 115px;
    width: 115px;
    max-width: 100%;
    background-color: transparent;
    border: transparent;
    line-height: 22px;
    color: var(--heading-color);
}

.filter-bar-search-box {
    width: 325px;
}

.show-all-course-area {
    margin-top: 40px;
}

.show-all-course-area-inner-tags {
    margin: 5px;
    display: inline-flex;
    align-items: center;
}

.show-all-course-area-inner-tags .iconify {
    border: 1px solid;
    border-radius: 50%;
    margin-right: 5px;
    font-size: 16px;
    padding: 1px;
}

.clear-all-tags {
    border: 1px solid var(--gray-color);
    border-radius: 31px;
    margin-right: 15px;
    padding: 3px 12px;
    font-size: 14px;
    display: flex;
    align-items: center;
}

.clear-all-tags .iconify {
    border: 1px solid var(--gray-color);
    border-radius: 50%;
    margin-right: 5px;
    background-color: var(--gray-color);
    color: var(--white-color);
}

/* Show all Courses Area End*/

/*My Courses Page CSS Start*/
.my-courses-page .filter-box {
    height: 45px;
}

.my-courses-page .filter-box-short-icon img {
    margin-right: 5px;
}

/*My Courses Page CSS End*/

/*-------------------------------------------
    22. Courses Page Area End
-------------------------------------------*/

/*-------------------------------------------
    23. Become an instructor Page Area Start
-------------------------------------------*/

/*  Become Instructor Feature area Start*/
.become-instructor-feature-item {
    min-height: 349px;
    padding: 66px 37px;
}

.become-instructor-feature-item h6 {
    padding: 10px 0 18px;
}

.counter-img-wrap img {
    border-radius: 50%;
}

/*  Become Instructor Feature area End*/

/*Become an instructor Procedures area Start*/
.become-an-instructor-procedure-item {
    padding: 0 90px 80px;
}

.become-an-instructor-procedure-item-right .section-heading {
    width: 100%;
    text-align: left;
    padding-right: 50%;
}

.become-an-instructor-procedure-item-right {
    margin-left: 25px;
}

.become-an-instructor-procedures-area .row:nth-child(even) {
    flex-direction: row-reverse;
}

.become-an-instructor-procedures-area .row:nth-child(even) .become-an-instructor-procedure-item-right {
    margin-left: 0;
    margin-right: 25px;
}

.become-an-instructor-procedures-area .row:nth-child(even) .become-an-instructor-procedure-item-right .section-heading {
    text-align: right;
    padding-right: 0;
    padding-left: 50%;
}

.become-an-instructor-procedures-area .row:nth-child(even) .become-an-instructor-procedure-item-right p {
    text-align: right;
}

.become-an-instructor-procedures-area .row:last-child {
    padding-bottom: 0;
}

.become-instructor-call-to-action {
    justify-content: center;
}

/*Become an instructor Procedures area End*/

/* Counter Area Start*/
.counter-content p {
    letter-spacing: 0.365em;
}

/* Counter Area End*/

/*Become an instructor Call to action area Start*/
.become-instructor-call-to-action .section-heading {
    margin: 0 auto 18px;
}

/*Become an instructor Call to action area End*/

/* Become an instructor Modal Start*/
@media only screen and (min-width: 992px) {
    #becomeAnInstructor .modal-dialog {
        max-width: 950px;
    }
}

/* Become an instructor Modal End */

/*-------------------------------------------
    23. Become an instructor Page Area End
-------------------------------------------*/

/*-------------------------------------------
    24. FAQ Page Area Start
-------------------------------------------*/

/*Faq Tab Nav List item css Start*/
.faq-tab-nav-wrap {
    margin-bottom: 70px;
}

.faq-tab-nav-wrap .nav-tabs .nav-link {
    border-radius: 0;
    min-height: 312px;
    padding: 43px;
    background-color: var(--white-color);
}

.faq-tab-nav-wrap .nav-tabs .nav-item.show .nav-link,
.faq-tab-nav-wrap .nav-tabs .nav-link.active {
    border: 1px solid rgba(117, 79, 254, 0.58);
    box-shadow: 0 11px 19px rgba(13, 5, 46, 0.1);
}

.faq-tab-nav-item .faq-tab-nav-img-wrap {
    margin: 18px 0 25px;
}

/*Faq Tab Nav List item css End*/

/*Faq accordion item css start*/
.faq-page-area .accordion-button:not(.collapsed) {
    color: var(--theme-color);
}

.faq-page-area .accordion-button,
.faq-page-area .accordion-button:not(.collapsed) {
    background-color: var(--white-color);
}

.faq-page-area .accordion-collapse {
    box-shadow: 0 4px 13px rgba(31, 16, 87, 0.05);
}

/*Faq accordion item css end*/

/*-------------------------------------------
    24. FAQ Page Area End
-------------------------------------------*/

/*-------------------------------------------
    25. Contact Page Area Start
-------------------------------------------*/
/*Contact page left side*/
.contact-page-left-side,
.contact-page-right,
.google-map-area .col-12 {
    padding: 0;
}

.contact-page-left-side-wrap {
    padding: 74px 142px 74px 40px;
}

.contact-info-item {
    width: 300px;
    margin-bottom: 25px;
}

/*Contact page right side*/
.contact-form-area {
    padding: 74px;
    border-left: 1px solid #EBE9E2;
}

.contact-form-title {
    margin-bottom: 35px;
}

.contact-form-area textarea.form-control {
    min-height: 134px;
}

/*Google Map area*/
.google-map-area iframe {
    width: 100%;
    height: 529px;
}

/*-------------------------------------------
    25. Contact Page Area End
-------------------------------------------*/

/*-------------------------------------------
    26. Error Page Area Start
-------------------------------------------*/
.error-page-area {
    background-color: #F7F8FC;
}

.error-page-area .row {
    min-height: 100vh;
    align-items: center;
}

.error-img-wrap {
    margin-bottom: 50px;
}

.error-content {
    padding: 0 28%;
}

.error-content p {
    padding: 22px 0 43px;
}

.no-course-found {
    min-height: 400px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

/*-------------------------------------------
    26. Error Page Area End
-------------------------------------------*/

/*-------------------------------------------
    27. Instructor Details / Instructor Single Page Area Start
-------------------------------------------*/

/* Instructor unavailable */
.instructor-temporary-unavailable {
    background-color: #F34D2D;
    margin-bottom: 50px;
}

/* Instructor unavailable */

/*instructor-details-left-content css*/
.instructor-details-left-inner-box {
    margin-bottom: 38px;
    padding: 55px 52px 60px;
    margin-right: 12px;
    border: 1px solid #F7F5FF;
}

.about-skills-box {
    padding-bottom: 50px;
}

/* Instructor skills */
.instructor-skills-tag-item {
    padding: 11px 25px;
    margin: 0 10px 10px 0;
}

/* Instructor skills */

/* .instructor-details-left-content .instructor-details-left-inner-box:last-child { */
.instructor-details-left-content .instructor-details-left-inner-box .my-others-courses {
    margin-bottom: 0;
    padding-bottom: 35px;
}

.instructor-details-inner-title {
    margin-bottom: 17px;
}

.certificate-awards-inner li {
    padding-bottom: 11px;
}

.certificate-awards-inner ul li:last-child {
    padding-bottom: 0;
}

.certificate-awards-inner ul li span {
    margin-right: 30px;
}

/*instructor-details-right-content*/
.instructor-details-right-img-box {
    /* margin-bottom: 32px; */
}

.instructor-details-area .instructor-info-box {
    border: 1px solid #F7F5FF;
}

.instructor-details-avatar-wrap {
    height: 172px;
    width: 172px;
    position: relative;
}

.instructor-details-avatar-wrap img {
    width: auto;
    height: 172px;
    position: absolute;
    transform: translateX(-50%);
    top: 0;
}

.instructor-info-box {
    padding: 50px 0 !important;
    /* padding: 50px 30px; */
}

.instructor-details-right-img-box,
.instructor-details-right-content .course-includes-box,
.instructor-details-right-content .instructor-social,
.instructor-details-right-content .instructor-bottom-item {
    padding: 0 40px !important;
}

.instructor-details-name {
    padding: 21px 0 10px;
}

.instructor-social ul li a {
    margin-right: 10px;
}

.instructor-social ul li a svg {
    color: rgba(0, 0, 0, 0.31);
    font-size: 20px;
}

.instructor-social ul li a:hover svg {
    /* color: var(--theme-color); */
}

/* Followers and Following  */
.follower-following-box {
    justify-content: space-between;
    display: flex;
}

.follower-item {
    border: 1px solid #ECE6D3;
    width: 50%;
    text-align: center;
    padding: 5px 5px;
}

/*-------------------------------------------
    27. Instructor Details / Instructor Single Page Area End
-------------------------------------------*/

/*-------------------------------------------
    28. Blog Page Area Start
-------------------------------------------*/
/* ----Blog Page Left Side css---- */

/* ----Blog Item CSS---- */
.blog-item {
    margin-bottom: 75px;
}

.blog-title {
    margin-bottom: 13px;
}

.blog-read-more-btn .theme-btn {
    padding-left: 0 !important;
}

.blog-page-left-content .blog-item:last-child {
    margin-bottom: 0;
}

.blog-item-img-wrap {
    margin-bottom: 30px;
}

.blog-content {
    margin-bottom: 19px;
}

.blog-author-name-publish-date {
    margin-bottom: 15px;
}

.blog-item-tag {
    top: 28px;
    left: 30px;
    padding: 9px 17px 7px;
    z-index: 99;
}

/* ----Blog Video Item Start---- */
.blog-slider-items-wrap {
    position: relative;
}

.blog-slider-item .blog-item-img-wrap {
    height: 506px;
    background-position: center;
    background-size: cover;
}

.blog-slider-item .blog-item-img-wrap img {
    opacity: 0;
    height: 100%;
}

.blog-slider-items-wrap .owl-nav button {
    background: var(--white-color) !important;
    height: 44px;
    width: 44px;
    border-radius: 50% !important;
    top: 31%;
    box-shadow: 0 4px 4px rgba(0, 0, 0, 0.25);
}

.blog-slider-items-wrap .owl-nav button .iconify {
    color: #000;
    font-size: 19px;
    padding-bottom: 1px;
}

.blog-slider-items-wrap .owl-nav button:hover .iconify {
    color: var(--theme-color);
}

.blog-slider-items-wrap button.owl-prev {
    position: absolute;
    left: 35px;
}

.blog-slider-items-wrap button.owl-next {
    position: absolute;
    right: 35px;
}

/* ----Blog Video Item End---- */

/* ----Blog Quote Item Start---- */
.blog-quote-item {
    background: rgba(255, 192, 20, 0.05);
    border: 1px solid rgba(226, 214, 182, 0.38);
    padding: 54px 73px 62px;
}

.blog-quote-item .blog-item-img-wrap {
    margin-bottom: -8px;
}

.blog-quote-item .blog-title {
    font-style: italic;
    font-weight: normal;
    font-size: 29px;
    line-height: 32px;
}

/* ----Blog Quote Item End---- */

/* ----Blog Item CSS---- */

/* -----Blog Sidebar Area Start css----- */
.blog-sidebar-box {
    margin-bottom: 45px;
}

/*Blog sidebar search box css*/
.blog-sidebar-box-title {
    font-size: 19px;
    line-height: 20px;
    margin-bottom: 1.4em;
}

.blog-sidebar-search-box .input-group {
    border: 1px solid #B9B9B9;
}

.blog-sidebar-search-box .input {
    color: var(--gray-color);
    font-size: 14px;
}

.blog-sidebar-search-box button {
    padding: 0 10px;
    font-size: 18px;
    color: #464646;
}

/*Blog sidebar blog item css*/
.blog-page-right-content .blog-sidebar-box:last-child {
    margin-bottom: 0;
}

.sidebar-blog-item {
    margin-bottom: 20px;
}

.blog-page-right-content {
    margin-left: 30px;
    padding: 45px;
}

.sidebar-blog-item-title {
    font-size: 15px;
    line-height: 18px;
    margin-bottom: 11px;
}

.sidebar-blog-item-img-wrap {
    max-height: 98px;
    width: 99px;
}

/*Blog categories css*/
.blog-sidebar-categories li {
    position: relative;
}

.blog-sidebar-categories li a {
    color: var(--body-font-color);
    display: block;
    margin-bottom: 8px;
    margin-left: 22px;
}

.blog-sidebar-categories li::before {
    background: #CCCCCC;
    height: 6px;
    width: 6px;
    content: "";
    position: absolute;
    left: 0;
    top: 8px;
    border-radius: 50%;
}

/*Blog tags css*/
.blog-sidebar-tags li {
    display: inline-flex;
}

.blog-sidebar-tags li a {
    background-color: #EEEEEE;
    font-size: 13px;
    line-height: 18px;
    /* text-transform: capitalize; */
    color: var(--body-font-color);
    font-weight: 500;
    margin: 0 7px 11px 0;
    padding: 10px;
}

.blog-sidebar-tags li a:hover {
    background-color: var(--theme-color);
    color: var(--white-color);
}

/*Blog Sidebar Area End*/

/*-------------------------------------------
    28. Blog Page Area End
-------------------------------------------*/

/*-------------------------------------------
    29. Blog Details Page Area Start
-------------------------------------------*/
.blog-details-content-quotation-part h4,
.blog-details-content-gallery-img-part h4 {
    margin-bottom: 20px;
}

.blog-details-content-gallery-img-part h4 {
    font-size: 26px;
    line-height: 28px;
}

.blog-details-page .blog-content {
    margin-bottom: 25px;
}

.blog-details-content-quotation-part,
.blog-details-quote-item {
    margin-bottom: 50px;
}

.blog-details-quote-item {
    background-color: #F4F1E5;
    border: 1px solid #EAEAE9;
    padding: 37px;
    margin-top: 45px;
}

.blog-details-content-quotation-part .blog-title {
    font-style: italic;
    font-weight: 300;
    font-size: 22px;
    line-height: 31px;
}

.blog-details-content-quotation-part .quote-item-img-wrap {
    margin-bottom: 0;
    margin-right: 33px;
}

.blog-details-content-quotation-part .quote-item-img-wrap img {
    height: 70px;
}

/*Blog Comment Area Start*/
.blog-comment-title {
    margin-bottom: 50px;
}

.author-details .writer-name {
    color: #18171C;
}

.author-details .writer-designation {
    letter-spacing: 0.185em;
}

.about-author {
    background-color: #F4F1E5;
    padding: 45px 50px;
    margin: 50px 0;
    display: inline-flex;
}

.author-img {
    height: 125px;
    width: 125px;
}

.author-img img {
    height: 125px;
    width: 100%;
}

.author-details {
    width: calc(100% - 125px);
}

.comment-date-time {
    margin: 10px 0 15px;
}

.blog-comment-item .comment-author-img {
    width: 100px;
    height: 100px;
    background-color: #C4C4C4;
}

.blog-comment-item .comment-author-img img {
    min-height: 100px;
    width: 100%;
}

.blog-comment-item .author-details {
    width: calc(100% - 100px);
}

/*--------------------*/
.share-article {
    margin-top: 43px;
}

.share-box {
    letter-spacing: 0.49em;
}

.social-share-box .social-share-btn {
    height: 40px;
    width: 40px;
    background-color: #B9B9B9;
    display: inline-flex;
    border-radius: 50%;
    justify-content: center;
    align-items: center;
    color: var(--white-color);
    margin-right: 6px;
}

.social-share-box .social-share-btn:hover {
    background-color: var(--theme-color);
    color: var(--white-color);
}

/*--------------------*/

/*--- 22.1 Block 3 Style--- */
.blog-comment-item {
    margin-bottom: 44px;
    display: flex;
}

.blog-comment-item.under-comment {
    padding-left: 60px;
}

.blog-comment-item.under-under-comment {
    padding-left: 120px;
}

/*--- 22.2 Leave a Comment Style --- */
.blog-reply-btn {
    letter-spacing: 0.135em;
    margin-top: 13px;
}

.leave-comment-area {
    padding: 58px 63px;
}

.leave-comment-area textarea.form-control {
    min-height: 170px;
}

/* Leave a comment under comment reply form Start */
.leave-comment-area.under-comment-reply-form {
    margin-left: 60px;
    margin-bottom: 44px;
}

/* Leave a comment under comment reply form End */

/* Comment reply Modal css */
#commentReplyModal textarea {
    height: 115px;
}

/* Comment reply Modal css */

/*Blog Comment Area End*/

/*-------------------------------------------
    29. Blog Details Page Area End
-------------------------------------------*/

/*-------------------------------------------
    30. Cart Page Area Start
-------------------------------------------*/
.cart-page-left-part,
.order-summary-box {
    border: 1px solid #EEEBEB;
}

.cart-page-left-part {
    padding: 40px;
}

.order-summary-box {
    padding: 40px 24px;
}

.price-symbol {
    margin-right: 5px;
}

/* Cart Right Order Summary css */
.order-summary-box-note {
    color: rgba(133, 133, 133, 1);
}

.order-summary-btns .theme-button1:hover {
    border: 2px solid var(--theme-color);
}

.order-summary-btns .load-more-btn {
    padding: 9.5px 24px !important;
    height: auto;
}

.order-summary-btns .load-more-btn:hover {
    background-color: var(--white-color) !important;
    border: 2px solid #A3A3A3 !important;
    color: #A3A3A3 !important;
}

/*-------------------------------------------
    30. Cart Page Area End
-------------------------------------------*/

/*-------------------------------------------
    31. Wishlist Page Area Start
-------------------------------------------*/
.table>:not(:first-child) {
    border-top: 1px solid currentColor;
}

/*Wishlist Item css*/
.wishlist-page-area td {
    vertical-align: middle;
    padding: 20px;
}

.wishlist-page-area th {
    padding: 20px;
}

.wishlist-add-to-cart-btn .theme-button {
    width: auto;
}

.wishlist-course-item {
    width: 540px;
}

.course-item.wishlist-item {
    box-shadow: none;
    padding: 0;
    margin-bottom: 0;
    min-height: auto;
    flex-direction: row;
    width: 394px;
}

.course-item.wishlist-item .course-tag.badge {
    padding: 4px 6px;
    top: 10px;
    left: 10px;
    font-size: 11px;
}

.course-item.wishlist-item .card-body {
    padding: 0 0 0 20px;
}

.wishlist-item .course-img-wrap {
    width: 198px;
    min-height: auto;
}

.wishlist-item .course-img-wrap img {
    min-height: auto;
}

.wishlist-item .course-title {
    font-size: 15px;
    line-height: 20px;
    margin-bottom: 11px;
}

.wishlist-item .instructor-name-certificate {
    line-height: 12px;
    font-size: 11px;
}

.wishlist-add-to-cart-btn button {
    font-size: 13px !important;
    padding: 7px 14px !important;
}

.wishlist-remove button {
    color: #B3B3B3;
    font-size: 31px;
}

/*-------------------------------------------
    31. Wishlist Page Area End
-------------------------------------------*/

/*-------------------------------------------
    32. My Courses Page Area Start
-------------------------------------------*/
.my-courses-page .barra {
    width: 158px;
    margin-left: 0;
}

.cart-page-left-part .theme-btn {
    font-size: 14px !important;
}

/*-------------------------------------------
    32. My Courses Page Area End
-------------------------------------------*/

/*-------------------------------------------
    33. Student Profile Page Area Start
-------------------------------------------*/
.student-profile-left-part {
    padding: 60px 0;
    border: 1px solid #F7F5FF;
    border-right: none;
    height: 100%;
}

.student-profile-right-part {
    border: 1px solid #F7F5FF;
    padding: 60px;
}

.student-profile-left-part h6,
.student-profile-right-part h6 {
    margin-bottom: 34px;
}

/* Student Profile Left Sidebar css */
.student-profile-left-part h6 {
    margin-left: 42px;
}

.student-profile-left-part ul li a {
    display: block;
    padding: 11px 42px;
    color: var(--gray-color);
}

.student-profile-left-part ul li a:hover,
.student-profile-left-part ul li a.active {
    background-color: #F8F7FD;
    /* color: var(--heading-color); */
}

/* Student Profile Right css */
.student-profile-right-part textarea {
    height: 149px;
}

.student-profile-right-part .profile-top .profile-image {
    position: relative;
    width: 100px;
    height: 100px;
    margin-right: 20px;
}

.student-profile-right-part .profile-top .profile-image img {
    width: auto;
    min-height: 100px;
    border-radius: 50%;
    max-width: 100px;
    max-height: 100px;
    height: 100px;
    min-width: 100px;
}

.student-profile-right-part .profile-top .profile-image .file-uplode-btn {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin: 0;
    position: absolute;
    bottom: 0;
    right: -5px;
    text-align: center;
    width: 36px;
    height: 36px;
    font-size: 21px;
    -webkit-box-shadow: 0 0 10px rgba(179, 168, 168, 0.25);
    box-shadow: 0 0 10px rgba(179, 168, 168, 0.25);
    cursor: pointer;
    -webkit-transition: all 0.5s linear;
    transition: all 0.5s linear;
}

.instructor-profile-left-part ul li a .iconify {
    font-size: 21px;
}

.student-profile-right-part .profile-top .profile-image #fileuplode {
    display: none;
}
.student-profile-right-part .profile-top .profile-cover-image #fileuplode1 {
    display: none;
}

/* Account Page css */
.account-connections-btn .theme-btn {
    border: 2px solid var(--heading-color);
    color: var(--heading-color);
}

/*-------------------------------------------
    33. Student Profile Page Area End
-------------------------------------------*/

/*-------------------------------------------
    34. Instructor Profile Page Area Start
-------------------------------------------*/
.instructor-only-dashboard-top-part>div:last-child .instructor-dashboard-top-part-item h5 {
    font-size: 13px;
    line-height: 19px;
}

.instructor-dashboard-two-part-join {
    border: 1px solid #EBEBEB;
}

.instructor-profile-left-part-wrap {
    border-right: 1px solid #EBEBEB;
}

.instructor-profile-right-part {
    padding: 30px;
}

.instructor-profile-left-part {
    border-radius: 8px;
    padding: 40px 30px;
}

/* Instructor profile right part */
.instructor-info-box-title {
    margin-bottom: 34px;
}

.instructor-profile-page textarea {
    height: 149px;
}

.instructor-profile-page .profile-top .profile-image {
    position: relative;
    width: 104px;
    height: 104px;
    margin-right: 20px;
}

.instructor-profile-page .profile-top .profile-image img {
    width: 104px;
    height: 104px;
    border-radius: 50%;
}

.instructor-profile-page .profile-top .profile-image .file-uplode-btn {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    margin: 0;
    position: absolute;
    bottom: 0;
    right: -5px;
    text-align: center;
    width: 36px;
    height: 36px;
    font-size: 21px;
    -webkit-box-shadow: 0 0 10px rgba(179, 168, 168, 0.25);
    box-shadow: 0 0 10px rgba(179, 168, 168, 0.25);
    cursor: pointer;
    -webkit-transition: all 0.5s linear;
    transition: all 0.5s linear;
}

.instructor-profile-page .profile-top .profile-image #fileuplode {
    display: none;
}
.instructor-profile-page .profile-top .profile-cover-image #fileuplode1 {
    display: none;
}
.instructor-profile-info-box {
    margin-bottom: 20px;
}

.instructor-add-extra-field-box .theme-btn {
    box-shadow: 3px 3px 8px 0 rgb(29 12 88 / 10%);
}

/* ------------------ */
.instructor-profile-left-part ul li {
    list-style: none;
    margin-bottom: 10px;
}

.instructor-profile-left-part ul li i {
    color: #333333;
    -webkit-transition: all 0.5s linear;
    transition: all 0.5s linear;
}

.instructor-profile-left-part ul li a {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    height: 45px;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    padding: 7px 15px;
    font-size: 15px;
    font-weight: 400;
    /* text-transform: capitalize; */
    color: #404065;
    -webkit-transition: all 0.5s linear;
    transition: all 0.5s linear;
    border-radius: 8px;
    border-bottom: 2px solid transparent;
}

.instructor-profile-left-part ul li a.active {
    background-color: #F6F6F6;
    color: var(--heading-color);
    border-bottom: 2px solid #EDEDED;
}

li.menu-has-children.current-menu-item.has-open a.active {
    background-color: #F6F6F6;
    border-bottom: 2px solid #EDEDED;
}

li.menu-has-children.current-menu-item.has-open ul li {
    margin-left: 19px;
}

.instructor-profile-left-part ul li .toggle-account-menu {
    position: absolute;
    top: 14px;
    right: 10px;
    cursor: pointer;
    font-size: 10px;
    width: 100%;
    text-align: right;
    z-index: 9;
}

.instructor-profile-left-part ul li.current-menu-item.has-open .toggle-account-menu {
    color: var(--heading-color);
}

.instructor-profile-left-part ul li.current-menu-item.has-open a {
    background-color: #F6F6F6;
    color: var(--heading-color);
    border-bottom: 2px solid #EDEDED;
}

.instructor-profile-left-part ul li.menu-has-children {
    position: relative;
}

.instructor-profile-left-part ul li.menu-has-children .account-sub-menu {
    margin-top: 20px;
    margin-bottom: 20px;
    padding: 0;
    display: none;
}

.instructor-profile-left-part ul li.menu-has-children .account-sub-menu li {
    list-style: none;
}

.instructor-profile-left-part ul li.menu-has-children .account-sub-menu li a {
    display: block;
    height: auto;
    background: transparent;
    font-size: 15px;
    /* text-transform: capitalize; */
    -webkit-transition: all 0.5s linear;
    transition: all 0.5s linear;
}

.instructor-profile-left-part ul li.menu-has-children.has-open .account-sub-menu {
    display: block;
    border-left: 1px solid #DFDFDF;
    margin-left: 20px;
}

.instructor-profile-left-part ul li.menu-has-children .account-sub-menu li a {
    position: relative;
    border-bottom: none;
    color: #404065;
}

.instructor-profile-left-part ul li.menu-has-children .account-sub-menu li a::before {
    content: "";
    height: 1px;
    width: 10px;
    background-color: #DFDFDF;
    position: absolute;
    left: -19px;
    top: 17px;
}

/* Withdraw History Page CSS Start */
.instructor-withdraw-history-box {
    border: 1px solid #EDEDED;
    padding: 40px 30px;
    border-radius: 8px;
    box-shadow: 0 2px 3px #ededed;
}

.instructor-withdraw-history-box tr td {
    color: var(--heading-color);
    font-size: 15px;
    min-width: 130px;
    width: 100%;
}

.instructor-withdraw-history-box tr td .iconify {
    font-size: 32px;
}

/* Withdraw History Page CSS End */

/* Instructor My Courses Page CSS Start */
.instructor-my-courses-title {
    border-bottom: 1px solid rgba(0, 0, 0, 0.11);
    margin-bottom: 30px;
    padding-bottom: 10px;
}

.instructor-my-course-item {
    box-shadow: none;
    padding: 0;
    border: 1px solid #E5E8EC !important;
    border-radius: 4px;
    display: flex;
    flex-direction: row;
    height: 190px;
    min-height: 190px;
}

.instructor-my-course-item .course-img-wrap {
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    width: 27%;
    min-height: auto;
    height: 188px;
    background-color: #d5d5d5;
}

.instructor-my-course-item .course-img-wrap img {
    min-height: 188px;
}

.instructor-my-course-item .card-body {
    /* flex: 1 1 auto; */
    padding: 19px 20px 20px;
}

.instructor-courses-info-duration-wrap {
    background-color: var(--white-color);
    border-radius: 26px;
    width: 80%;
    border: 1px solid #E5E8EC;
    margin-bottom: 26px;
}

.instructor-my-course-item .course-tag.badge {
    padding: 9px 13px;
    top: 15px;
    right: 15px;
    left: auto;
    z-index: 9;
    min-width: 67px;
    width: auto;
}

.instructor-my-course-item .course-tag.publish-badge,
.instructor-my-course-item .course-tag.unpublish-badge {
    left: 15px;
    right: auto;
    color: var(--white-color);
}

.instructor-my-course-item .course-tag.publish-badge {
    background-color: var(--color-green);
}

.instructor-my-course-item .course-tag.unpublish-badge {
    background-color: #17a2b8;
}

.instructor-courses-info-duration-wrap ul {
    border-radius: 26px;
    padding: 7px 12px;
}

.instructor-courses-info-duration-wrap ul li {
    display: flex;
    align-items: center;
}

.instructor-courses-info-duration-wrap ul li .iconify {
    margin-right: 6px;
}

.instructor-courses-info-duration-wrap-text {
    margin-left: .25rem;
}

.instructor-my-course-btns {
    padding: 19px 15px;
    display: block;
    width: 22%;
    text-align: right;
}

.instructor-my-course-btns .instructor-course-btn:not(:last-child) {
    margin-bottom: 15px;
}

.instructor-my-course-btns .instructor-course-btn:nth-child(1):hover {
    border: 2px solid var(--theme-color);
}

.instructor-my-course-btns .instructor-course-btn:nth-child(2) {
    background-color: #45C881;
    border: 2px solid #45C881;
}

.instructor-my-course-btns .instructor-course-btn:nth-child(2):hover {
    background-color: var(--white-color);
    border: 2px solid #45C881;
    color: #45C881 !important;
}

.instructor-my-course-btns .instructor-course-btn:nth-child(3) {
    background-color: #A921FF;
    border: 2px solid #A921FF;
}

.instructor-my-course-btns .instructor-course-btn:nth-child(3):hover {
    background-color: var(--white-color);
    border: 2px solid #A921FF;
    color: #A921FF !important;
}

.instructor-course-btn {
    height: 40px;
    min-width: 150px;
    padding: 9.5px 17px !important;
}

/* Instructor My Courses Page CSS End */

/* Instructor Dashboard Ranking Items */
.ranking-items-wrap .ranking-item:not(:last-child) {
    margin-bottom: 30px;
}

.disable-ranking-item {
    opacity: .4;
}

.ranking-item-right {
    position: relative;
    padding-left: 50px;
    width: 50%;
    justify-content: flex-end !important;
}

.ranking-item-right:after {
    position: absolute;
    z-index: 9;
    content: "";
    height: 65%;
    width: 1px;
    background-color: #E5E8EC;
    left: 0;
}

.ranking-item-left {
    width: 50%;
    position: relative;
}

.ranking-content-in-right {
    margin-left: 15px;
}

.ranking-item-right .ranking-content-in-right {
    margin-left: 50px;
}

.recently-added-courses-box .ranking-item-right .ranking-content-in-right {
    margin-left: 18px;
}

/* Instructor Dashboard Ranking Items */

/*-------------------------------------------
    34. Instructor Profile Page Area End
-------------------------------------------*/

/*-------------------------------------------
    35. Message Page Area Start
-------------------------------------------*/

/* Messenger User Left side */

/* Messenger user top part */
.message-user-top-part {
    padding: 30px 30px 15px;
}

.message-user-top-part-btns {
    margin-top: 20px;
}

.message-user-top-part-btns button {
    border: 1px solid #D4D4D4;
    border-radius: 46px;
    padding: 4px 20px;
}

.message-user-top-part-btns .active,
.message-user-top-part-btns button:hover {
    color: #754FFE;
    background-color: rgba(117, 79, 254, 0.2);
    border: 1px solid rgba(117, 79, 254, 0.06);
}

.search-message-box .input-group-text {
    border: 1px solid #E9E8E8;
    color: var(--gray-color);
    padding-right: 0;
}

.search-message-box input {
    height: 32px;
    border: 1px solid #E9E8E8;
    color: var(--gray-color);
    border-left: 0;
}

.search-message-box .form-control:focus {
    border-color: #E9E8E8;
    outline: 0;
    box-shadow: none;
}

/* Messenger user bottom part */
.message-user-sidebar {
    height: 790px;
    border: 1px solid #DCDCDC;
}

.message-user-list-wrap {
    max-height: 540px;
    overflow-y: auto;
}

.message-user-list-part-title {
    border-bottom: 1px solid #DCDCDC;
    padding-bottom: 20px;
}

.message-user-list-part-title h6 {
    padding: 0 30px;
}

.message-user-item {
    background-color: var(--white-color);
    padding: 12px 30px;
    margin-bottom: 2px;
}

.message-user-item.active,
.message-user-item:hover {
    background-color: #F8F5FA !important;
}

.message-user-item.active {
    border-left: 3px solid var(--theme-color);
}

.message-page .user-img-wrap {
    height: 42px;
    width: 42px;
    border-radius: 50%;
}

.message-page .user-img-wrap img {
    height: 42px;
    border-radius: 50%;
    width: 100%;
}

.message-user-item-left p {
    line-height: 16px;
}

.title-imoji-img {
    height: 17px;
    margin-left: 4px;
}

.online-offline-show {
    height: 12px;
    width: 12px;
    border-radius: 50%;
    border: 2px solid var(--white-color);
    right: 0;
    bottom: 4px;
    z-index: 9;
}

.online-offline-show.online {
    background-color: #45C881;
}

.online-offline-show.offline {
    background-color: #FFC014;
}

.message-user-notification-box {
    background-color: rgba(255, 68, 68, 0.16);
    color: #FF4444;
    min-height: 18px;
    min-width: 18px;
    border-radius: 32px;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: right;
    float: right;
    font-size: 10px;
    padding: 0 4px;
}

/* Message Page Chat Right side css */
.message-chat-right-part {
    height: 790px;
    border: 1px solid #DCDCDC;
}

/* Message Chat Top Part Start */
.message-chat-top-part {
    border-bottom: 1px solid rgba(220, 220, 220, 0.45);
    height: 88px;
    padding: 20px 22px;
}

.in-chat-mode-online-offline .yes-online {
    background-color: var(--color-green);
    color: var(--white-color);
}

.in-chat-mode-online-offline button {
    border-radius: 71px;
    padding: 3px 8px;
}

.message-chat-top-action-btn {
    height: 33px;
    width: 33px;
    font-size: 24px;
    color: var(--body-font-color);
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.message-chat-top-right .dropdown-toggle {
    display: inline-flex !important;
}

.message-chat-top-right .dropdown {
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.message-chat-top-right .dropdown-toggle::after {
    display: none;
}

.message-chat-top-right .dropdown-menu {
    min-width: 6rem;
}

.message-chat-top-right .dropdown-menu .dropdown-item {
    display: inline-flex;
    align-items: center;
}

.message-chat-top-right .dropdown-menu .dropdown-item .iconify {
    margin-right: 6px;
}

.message-chat-top-right .dropdown-menu li:nth-child(1) .dropdown-item .iconify {
    color: #FF4444;
}

.message-chat-top-right .dropdown-menu li:nth-child(2) .dropdown-item .iconify {
    color: #FFC014;
}

.message-chat-top-right .dropdown-menu li:nth-child(3) .dropdown-item .iconify {
    color: var(--theme-color);
}

/* Message Chat Top Part End */

/* Message Chat Bottom Part Start */
.message-chat-bottom-part {
    bottom: 0;
    width: 100%;
    padding: 30px;
    background-color: var(--white-color);
}

.message-chat-bottom-left .input-group {
    border: 1px solid rgba(46, 129, 255, 0.06);
    font-size: 14px;
    background-color: rgba(79, 128, 254, 0.06);
    color: var(--gray-color);
    width: 680px;
    border-radius: 53px;
    padding-left: 10px;
    padding-right: 10px;
}

.message-chat-bottom-action-btn {
    border: 1px solid rgba(46, 129, 255, 0.06);
    height: 48px;
    width: 48px;
    border-radius: 50%;
    background-color: rgba(79, 128, 254, 0.06);
    font-size: 19px;
    color: var(--body-font-color);
}

.send-chat-button {
    background-color: var(--theme-color);
    color: var(--white-color);
}

/* Message Chat Bottom Part End */

/* Message Chat Main Part Start */

/* Custom Scrollbar start */
.custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: #E6E3EB var(--white-color);
}

.custom-scrollbar::-webkit-scrollbar {
    width: 4px;
    height: 10px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background-color: var(--white-color);
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: #E6E3EB;
}

.custom-scrollbar::-webkit-scrollbar-track,
.custom-scrollbar::-webkit-scrollbar-thumb {
    border-radius: 5px;
}

/* Custom Scrollbar end */

.message-chat-main-part {
    padding: 0 30px 0;
    bottom: 52px;
    position: absolute;
    width: 100%;
    margin-bottom: 42px;
    max-height: 608px;
    overflow-y: auto;
}

/* Conversation Start Date CSS */
.conversation-start-date {
    margin: 40px 0 20px;
}

.conversation-start-date:before {
    content: "";
    background-color: #DCDCDC;
    width: 100%;
    height: 1px;
    left: 0;
    position: absolute;
}

.conversation-start-date span {
    top: -9px;
    padding: 0 8px;
}

/* Chat Item css */
.chat-item {
    max-width: 680px;
    width: 100%
}

/* Chat Item Dropdown css start */
.chat-item .dropdown {
    right: -25px;
    float: right;
}

.chat-item .dropdown-menu {
    min-width: 6rem;
    border: 1px solid #EEEDED;
    border-radius: 8px;
}

.chat-item .dropdown-toggle::after {
    display: none;
}

/* Chat Item Dropdown css end */
.chat-item.chat-item-right {
    float: right;
    width: 100%;
}

.chat-text-box {
    margin-right: 20px;
}

.chat-text-box p {
    background-color: #F0F0F0;
    color: #2F2F38;
    font-size: 14px;
    margin-bottom: 15px;
    padding: 18px 26px;
    line-height: 22px;
    border-radius: 8px;
    border-top-left-radius: 0;
}

/* Message Chat Main Part End */

/*-------------------------------------------
    35. Message Page Area End
-------------------------------------------*/

/*-------------------------------------------
    36. Instructor Dashboard Page Area Start
-------------------------------------------*/
.instructor-dashboard-top-part div:nth-child(n+2) .instructor-dashboard-top-part-item .instructor-dashboard-top-part-icon {
    background-color: rgba(103, 69, 200, 0.12);
    color: var(--theme-color);
    border: 1px solid rgba(103, 69, 200, 0.08);
}

.instructor-dashboard-top-part div:nth-child(n+3) .instructor-dashboard-top-part-item .instructor-dashboard-top-part-icon {
    background-color: rgba(255, 192, 20, 0.22);
    color: #E1AC1B;
    border: 1px solid rgba(255, 192, 20, 0.17);
}

.instructor-dashboard-top-part-item {
    border: 1px solid #EDEDED;
    padding: 30px;
    box-shadow: 0 2px #ededed;
    min-height: 118px;
}

.instructor-dashboard-top-part-icon {
    border: 1px solid rgba(69, 200, 129, 0.14);
    height: 56px;
    width: 56px;
    border-radius: 50%;
    background-color: rgba(69, 200, 129, 0.12);
    color: var(--color-green);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
}

.chart-wrap1 {
    height: 220px;
}

/* Recently added courses css start */
.recently-added-courses-box {
    border: 1px solid #EDEDED;
    /* padding: 30px; */
    box-shadow: 0 2px #ededed;
}

.recently-added-courses-title {
    padding: 20px 30px 8px;
}

.recently-added-courses-title a {
    border: 1px solid #BFBFBF;
    border-radius: 48px;
    padding: 5px 14px;
}

.recently-added-course-item-wrap,
.ranking-items-wrap {
    padding: 0 30px 30px 30px;
}

.your-rank-title {
    background-color: #F9F9F9;
    padding: 20px 30px;
}

.recently-added-course-item {
    margin-bottom: 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    padding-bottom: 20px;
}

.recently-added-courses-box .recently-added-course-item:last-child {
    margin-bottom: 0;
    border-bottom: transparent;
    padding-bottom: 0;
}

.recently-added-course-item .user-img-wrap {
    width: 64px;
    height: 64px;
    border-radius: 50%;
    overflow: hidden;
}

.recently-added-course-item .user-img-wrap img {
    min-width: 64px;
    width: 100%;
    min-height: 64px;
    /* border-radius: 50%; */
}

.ranking-item .user-img-wrap {
    width: 84px;
    height: 66px;
    border-radius: 50%;
    overflow: hidden;
}

.ranking-item .user-img-wrap img {
    min-width: 84px;
    min-height: 66px;
}

.recently-added-courses-title button {
    border: 1px solid #BFBFBF;
    padding: 3px 14px;
    border-radius: 48px;
}

.recently-added-course-item-right button {
    background-color: rgba(117, 79, 254, 0.1);
    color: var(--theme-color);
    padding: 5px 10px;
    border-radius: 36px;
}

/* Recently added courses css end */

/* Ranking Badge List Page */
.instructor-ranking-badge-page .ranking-items-wrap {
    padding: 0;
}

.ranking-badge-page-row .ranking-badge-page-side-box:last-child {
    border-right: none;
}

.instructor-ranking-badge-page .ranking-items-wrap .ranking-item:not(:last-child) {
    margin-bottom: 45px;
}

.ranking-badge-page-side-box {
    border-right: 1px solid #E5E8EC;
}

/* Ranking Badge List Page */

/* Upload your course today css start */
.upload-your-course-part {
    background-image: url('../img/instructor-img/upload-your-course-today-bg.jpg');
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    padding: 69px 50px 57px;
}

.upload-your-course-part h6 {
    margin-bottom: 15px;
}

.upload-your-course-part p {
    margin-bottom: 15px;
    width: 70%;
    color: rgba(255, 255, 255, 0.79);
    line-height: 20px;
}

.upload-your-course-today-btn {
    padding: 6px 20px;
    border-radius: 48px;
}

.upload-your-course-today-btn:hover {
    background-color: var(--white-color);
    color: var(--theme-color) !important;
}

/* Upload your course today css end */

/* Apex Chart Js css start*/
.instructor-dashboard-chart-box {
    border: 1px solid #EDEDED;
    padding: 3px 5px 0;
    box-shadow: 0 2px #ededed;
    /*max-height: 280px;*/
}

.apexcharts-area-series .apexcharts-area {
    stroke: #754FFE;
    background-image: linear-gradient(rgba(122, 85, 255, 0.29), rgba(238, 234, 255, 0));
}

.apexcharts-tooltip-series-group.apexcharts-active .apexcharts-tooltip-marker {
    background-color: #754FFE !important;
}

.apexcharts-tooltip-marker {
    background-color: #754FFE !important;
}

.apexcharts-toolbar {
    display: none !important;
}

/* Apex Chart Js css end */

/*Analysis Page Chart Start*/
.chart-wrap {
    height: 300px;
}

/*Analysis Page Chart End*/

/*-------------------------------------------
    36. Instructor Dashboard Page Area End
-------------------------------------------*/

/*-------------------------------------------
    37. Instructor Course Upload/ Form Multistep Page Area Start
-------------------------------------------*/
#msform {
    position: relative;
}

#msform .upload-course-step-item {
    background: var(--white-color);
    border: 0 none;
    border-radius: 0.5rem;
    box-sizing: border-box;
    width: 100%;
    margin: 0;
    padding-bottom: 20px;
    position: relative;
    text-align: left;
}

#msform .upload-course-step-item:not(:first-of-type) {
    display: none
}

#msform textarea {
    width: 100%;
}

#msform input:focus,
#msform textarea:focus {
    border: 1px solid var(--theme-color);
}

#msform .action-button-previous {
    background-color: var(--white-color);
    border: 1px solid #D8D8D8;
    color: var(--body-font-color);
    margin-right: 10px;
}

#msform .action-button-previous:hover,
#msform .action-button-previous:focus {
    border: 1px solid var(--theme-color);
    color: var(--theme-color);
}

.card {
    z-index: 0;
    border: none;
    position: relative
}

.fs-title {
    font-size: 25px;
    color: var(--theme-color);
    margin-bottom: 15px;
    font-weight: normal;
    text-align: left
}

.purple-text {
    color: var(--theme-color);
    font-weight: normal
}

.steps {
    font-size: 25px;
    color: gray;
    margin-bottom: 10px;
    font-weight: normal;
    text-align: right
}

#progressbar {
    margin-bottom: 30px;
    overflow: hidden;
    color: lightgrey;
    border: 1px solid #EDEDED;
    padding: 33px 0;
    box-shadow: 0 2px #ededed;
    border-radius: 8px;
}

#progressbar .active {
    color: var(--heading-color);
}

#progressbar li {
    list-style-type: none;
    font-size: 15px;
    width: 25%;
    float: left;
    position: relative;
    font-weight: 400;
    text-align: center;
}

#progressbar li:before {
    width: 35px;
    height: 35px;
    line-height: 45px;
    display: flex;
    font-size: 15px;
    color: var(--white-color);
    background: var(--white-color);
    border-radius: 50%;
    margin: 0 auto 10px auto;
    padding: 2px;
    border: 5px solid var(--theme-color);
    align-items: center;
    justify-content: center;
    top: 7px;
    position: relative;
    font-family: Feather !important;
    content: "\e92b"
}

#progressbar li:after {
    content: '';
    width: 100%;
    height: 2px;
    background: #DFDCEB;
    position: absolute;
    right: -118px;
    top: 25px;
    z-index: -1;
}

#progressbar li:last-child::after {
    display: none;
}

#progressbar li.active:before,
#progressbar li.active:after {
    background: var(--theme-color);
}

.progress {
    height: 20px;
}

.progress-bar {
    background-color: var(--theme-color);
}

.upload-course-item-block {
    border: 1px solid #EDEDED;
    padding: 30px 30px 0;
    box-shadow: 0 2px #ededed;
    margin-bottom: 30px;
}

.upload-course-item-block.course-overview-step1 .main-upload-video-processing-wrap:first-child {
    border-top: none;
}

.course-overview-step1 ul li .iconify {
    color: #B3AAD2;
    margin-right: 15px;
}

.course-overview-step1 ul li {
    margin-bottom: 12px;
}

/* Stepper Btns CSS start */
span.tooltip-show-btn {
    height: 15px;
    width: 15px;
    border-radius: 50%;
    background-color: #A3A3A9;
    font-size: 12px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    color: var(--white-color);
}

.stepper-action-btns button {
    margin-right: 10px;
}

.stepper-action-btns .theme-button1,
.action-button.theme-button1 {
    border: 1px solid var(--theme-color);
}

.stepper-action-btns .theme-button1:hover,
.action-button.theme-button1:hover {
    border: 1px solid var(--theme-color);
}

.stepper-action-btns .theme-button3,
.show-last-phase-back-btn {
    border: 1px solid #D8D8D8;
    background-color: var(--white-color);
    color: var(--body-font-color);
    margin-right: 10px;
}

.stepper-action-btns .theme-button3:hover,
.show-last-phase-back-btn:hover {
    border: 1px solid var(--theme-color);
    background-color: var(--white-color);
    color: var(--theme-color);
}

/* Show only when last form start */
.upload-course-overview-step-item .d-none+.show-last-phase-back-btn+.last-step-form-btn {
    display: none;
}

.upload-course-overview-step-item .d-none+.show-last-phase-back-btn {
    display: none;
}

.upload-course-video-step-item #upload-course-video-1+.previous,
.upload-course-video-step-item #upload-course-video-2+.previous,
.upload-course-video-step-item #upload-course-video-3+.previous,
.upload-course-video-step-item #upload-course-video-4+.previous,
.upload-course-video-step-item #upload-course-video-5+.previous,
.upload-course-video-step-item #upload-course-video-7+.previous {
    display: none;
}

.upload-course-video-step-item #upload-course-video-1+.previous+.next,
.upload-course-video-step-item #upload-course-video-2+.previous+.next,
.upload-course-video-step-item #upload-course-video-3+.previous+.next,
.upload-course-video-step-item #upload-course-video-4+.previous+.next,
.upload-course-video-step-item #upload-course-video-5+.previous+.next,
.upload-course-video-step-item #upload-course-video-7+.previous+.next {
    display: none;
}

.upload-course-video-step-item #upload-course-video-6.show-next-go-btn+div+.previous {
    display: inline-flex !important;
}

.upload-course-video-step-item #upload-course-video-6.show-next-go-btn+div+.previous+.next {
    display: inline-flex !important;
}

.upload-course-video-step-item #upload-course-video-6 .add-more-section-btn {
    position: absolute;
    right: 0;
    top: 0;
}

/* Show only when last form end */

/* Stepper Btns CSS End */

/* ----Upload Course Upload Video Steps Start---- */
.upload-video-introduction-box {
    background-color: rgba(196, 196, 196, 0.06);
    border: 1px solid #EFEFEF;
    margin-bottom: 30px;
}

.upload-introduction-title-box {
    background-color: rgba(196, 196, 196, 0.12);
    border: 1px solid #EFEFEF;
    padding: 13px 20px;
}

.upload-course-duration-text {
    margin-right: 1.5rem;
}

.upload-course-duration-text .iconify {
    margin-right: .5rem;
}

.popover {
    background-color: var(--white-color);
    border: 1px solid #F8EFEF;
    color: #615656;
    font-size: 13px;
    border-radius: 4px;
}

.upload-introduction-box-content {
    margin: 20px;
}

.upload-introduction-box-content-img {
    height: 76px;
}

.upload-introduction-box-content img {
    width: 76px;
}

.upload-course-video-edit-btn {
    display: inline-flex;
    align-items: center;
    margin-right: 1.5rem;
}

.upload-course-video-edit-btn .iconify {
    margin-right: .25rem;
}

.common-upload-lesson-btn,
.common-upload-video-btn {
    border: 1px solid rgba(0, 0, 0, 0.06);
    padding: 7px 16px;
    background-color: rgba(196, 196, 196, 0.06);
    border-radius: 4px;
    margin-left: 20px;
}

.common-upload-lesson-btn:hover,
.common-upload-video-btn:hover {
    border: 1px solid var(--theme-color);
    background-color: var(--theme-color);
    color: var(--white-color);
}

.common-upload-lesson-btn .iconify,
.common-upload-video-btn .iconify {
    font-size: 16px;
    margin-right: 5px;
}

.add-more-section-btn {
    border: 1px solid rgba(0, 0, 0, 0.06);
    box-shadow: 0 2px 4px 0 rgb(29 12 88 / 10%);
    background-color: rgba(255, 255, 255, 0.16);
    padding: 9.5px 17px !important;
    border-radius: 4px;
    display: inline-flex;
    align-items: center;
}

.add-more-section-btn:hover,
.upload-course-video-edit-btn:hover {
    color: var(--theme-color);
}

.add-more-section-btn .iconify {
    font-size: 18px;
    margin-right: .5rem;
}

.main-upload-video-processing-wrap {
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
    margin-bottom: 30px;
}

.upload-course-item-block .main-upload-video-processing-wrap:last-child {
    margin-bottom: 0;
    border-bottom: 0;
}

.main-upload-video-processing-item-btns .theme-btn {
    border-width: 1px;
    margin-right: 7px;
}

.upload-video-processing-item-save-btn {
    background-color: #45C881;
    border: 1px solid rgba(0, 0, 0, 0.06);
    color: var(--white-color);
}

.upload-video-processing-item-save-btn:hover {
    background-color: var(--white-color);
    border: 1px solid #45C881;
    color: #45C881;
}

.upload-video-processing-item-remove-btn {
    background-color: rgba(255, 0, 0, 0.16);
    border: 1px solid rgba(0, 0, 0, 0.06);
    color: #AA6262;
}

.upload-video-processing-item-remove-btn:hover {
    background-color: rgba(255, 0, 0, 0.66);
    border: 1px solid rgba(255, 0, 0, 0.66);
    color: var(--white-color);
}

.upload-video-processing-item-back-btn {
    border: 1px solid #D8D8D8;
    color: var(--body-font-color);
}

.upload-video-processing-item-update-btn:hover,
.upload-video-processing-item-back-btn:hover {
    border: 1px solid var(--theme-color);
    color: var(--theme-color);
}

.main-upload-video-progress-bar-wrap {
    margin-bottom: 40px;
}

.main-upload-video-processing-img-wrap {
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    height: 222px;
    width: 285px;
    background-color: #E3E3E3;
    margin-right: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.main-upload-video-processing-img-wrap img {
    height: 222px;
    width: auto;
    max-width: 285px;
    min-width: 285px;
}

.upload-img-btn-wrapper {
    position: relative;
    overflow: hidden;
    display: inline-block;
}

.upload-img-btn-wrapper button {
    border: 1px solid rgba(0, 0, 0, 0.17);
    color: var(--body-font-color) !important;
    padding: 9.5px 14px !important;
    font-size: 14px !important;
}

.create-assignment-upload-files .theme-btn {
    background-color: #E6E6E6;
    border: none;
}

.upload-img-btn-wrapper input[type=file] {
    font-size: 100px;
    position: absolute;
    left: 0;
    top: 0;
    opacity: 0;
}

.form-control[type=file] {
    overflow: hidden;
    border: 1px solid var(--border-color);
    height: auto;
    max-width: 298px;
    border-radius: 4px;
}

/* Course Upload Frontend Remove Button */
.frontend-remove-btn {
    width: 50px;
    height: 49px;
    padding: 10px !important;
    min-width: 50px !important;
}

.frontend-remove-btn .iconify {
    margin: 0 !important;
}

.btn-check:active+.btn-danger:focus,
.btn-check:checked+.btn-danger:focus,
.btn-danger.active:focus,
.btn-danger:active:focus,
.show>.btn-danger.dropdown-toggle:focus {
    box-shadow: none;
}

/* Course Upload Frontend Remove Button */

/*Edit Course Section Start*/
.stepper-action-btns {
    position: relative;
}

#upload-course-video-7 .stepper-action-btns>div,
#upload-course-video-7 .stepper-action-btns button {
    display: none;
}

/* Edit Lecture */
.lecture-edit-title,
.see-preview-video {
    margin-left: 10px;
}

.see-preview-video {
    min-width: 110px;
    display: inline-block;
}

.lecture-edit-upload-btn {
    border-top: 1px solid rgba(0, 0, 0, 0.11);
    padding-top: 22px;
}

.uploaded-course-edit-video {
    max-width: 300px;
    width: 100%;
    height: 240px;
}

/* Edit Lecture*/

/*Edit Course Section End*/

/*video-upload-done-phase*/
.main-upload-video-processing-item {
    border-top: 1px solid rgba(0, 0, 0, 0.11);
    margin-top: 20px;
    padding-top: 20px;
}

.video-upload-final-item .accordion-button:not(.collapsed) {
    background-color: var(--heading-color);
}

.video-upload-final-item .accordion-button:not(.collapsed) h6,
.video-upload-final-item .accordion-button:not(.collapsed).upload-introduction-title-box,
.video-upload-final-item .accordion-button:not(.collapsed) .color-para,
.video-upload-final-item .accordion-button:not(.collapsed).upload-introduction-title-box span.color-heading {
    color: var(--white-color);
}

.video-upload-final-item .accordion-body {
    padding: 30px 0;
}

.video-upload-final-item .accordion-body .main-upload-video-processing-item:first-child {
    border-top: none;
    margin-top: 0;
    padding-top: 0;
}

.video-upload-final-item .accordion-collapse {
    box-shadow: none;
}

.video-upload-final-item .accordion-button::after {
    position: absolute;
    left: 16px;
    display: inline-flex;
    align-items: center;
    width: 20px;
    height: 20px;
    flex-flow: column;
    justify-content: center;
}

.video-upload-done-phase-action-btns {
    box-shadow: 0 0 7px 0 rgb(4 10 66 / 18%);
    background-color: rgba(255, 255, 255, 0.06);
    border-radius: 9px;
    margin-top: 15px;
    padding: 1px 5px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.video-upload-final-item .main-upload-video-processing-img-wrap {
    height: 146px;
    width: 161px;
    margin-right: 0;
}

.video-upload-final-item .common-upload-video-btn {
    background-color: rgba(117, 79, 254, 0.08);
}

.video-upload-final-item .common-upload-video-btn:hover {
    color: var(--theme-color);
}

/* ----Upload Course Upload Video Steps End---- */

/* ----Upload Course Submit Process Steps Start---- */
.form-last-step {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    margin: 0 auto;
    height: 380px;
    padding: 20px 0;
    text-align: center;
    margin-bottom: 30px;
}

#upload-course-overview-2 .main-upload-video-processing-img-wrap {
    height: 353px;
    width: 100%;
    margin-right: 0;
}

/* ----Upload Course Submit Process Steps End---- */

/* Upload Course Category & Tags Select2 textarea style */
.upload-course-overview-step-item .select2-container--default .select2-search--inline .select2-search__field {
    opacity: 0;
}

.upload-course-overview-step-item .select2-container--default.select2-container--focus .select2-selection--multiple {
    border: solid #86b7fe 1px;
}

.upload-course-overview-step-item .select2-container--default .select2-selection--multiple {
    border: 1px solid var(--border-color);
}

/* Upload Course Category & Tags Select2 textarea style */

/*-------------------------------------------
    37. Instructor Course Upload/ Form Multistep Page Area End
-------------------------------------------*/

/*-------------------------------------------
    38. Instructor Analysis Page Start
-------------------------------------------*/
.instructor-withdrawal-money-box {
    background-image: url('../img/instructor-img/upload-your-course-today-bg.jpg');
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    padding: 37px 20px;
    box-shadow: none;
}

.chart-title {
    padding: 15px 16px 0;
}

.instructor-dashboard-menu .dropdown-toggle::after {
    display: none;
}

.instructor-dashboard-menu.dropdown button {
    position: relative;
    right: -12px;
}

.instructor-dashboard-menu .dropdown-menu {
    border: 1px solid #EEEDED;
    padding: 0;
    min-width: 7rem;
}

.instructor-dashboard-menu .dropdown-item {
    padding: .35rem 1rem;
    border-bottom: 1px solid #EEEDED;
}

.instructor-dashboard-top-part div:nth-child(n+4) .instructor-dashboard-top-part-item .instructor-dashboard-top-part-icon {
    background-color: rgba(127, 28, 160, 0.11);
    color: #7F1CA0;
    border: 1px solid rgba(127, 28, 160, 0.08);
}

.instructor-dashboard-top-part div:nth-child(n+5) .instructor-dashboard-top-part-item .instructor-dashboard-top-part-icon {
    background-color: rgba(252, 128, 104, 0.2);
    color: #FC8068;
    border: 1px solid rgba(252, 128, 104, 0.08);
}

.instructor-dashboard-top-part div:nth-child(n+6) .instructor-dashboard-top-part-item .instructor-dashboard-top-part-icon {
    background-color: rgba(69, 200, 129, 0.12);
    color: var(--color-green);
    border: 1px solid rgba(69, 200, 129, 0.14);
}

/*Common Modal Start*/
@media (min-width: 576px) {
    .modal-dialog {
        max-width: 648px;
    }
}

.modal-header {
    padding: 1rem 2rem;
}

.modal-body {
    padding: 2rem;
}

.modal-footer {
    padding: 1rem 2rem;
}

.btn-close {
    border-radius: 50%;
    border: 1px solid gray;
    padding: 8px;
    font-size: 13px;
    position: absolute;
    right: 38px;
}

/*Common Modal End*/

/*Withdrawal Modal Start*/

#withdrawalModal .modal-body {
    padding: 3rem;
}

#withdrawalModal .modal-header {
    z-index: 9;
}

#withdrawalModal .btn-close {
    right: 20px;
    top: 20px;
}

.withdrawal-modal-title {
    margin-bottom: 40px;
}

.withdrawal-modal-title p {
    color: #A2A2B4;
    letter-spacing: 2px;
    margin-bottom: 5px;
}

.withdrawal-radio-item-wrap {
    margin-bottom: 15px;
}

.withdrawal-radio-item {
    position: relative;
    top: 6px;
}

.withdrawal-radio-item-wrap .form-check {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/*Withdrawal Modal End*/

/*-------------------------------------------
    38. Instructor Analysis Page End
-------------------------------------------*/

/*-------------------------------------------
    39. Checkout Page Start
-------------------------------------------*/
.billing-address-box,
.payment-method-box {
    box-shadow: 0 0 1px 1px rgb(0 0 0 / 3%);
    border: 1px solid #EEEBEB;
    padding: 40px;
    margin-bottom: 30px;
    border-radius: 4px;
}

.payment-method-card-box {
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 20px !important;
}

.payment-method-card-box label {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.paypal-box label {
    margin-bottom: 0;
}

.payment-method-card-box .form-check-input {
    margin-right: 10px;
    margin-left: 0;
    position: relative;
    top: 3px;
}

.payment-method-card-box #razorpayPayment,
.payment-method-card-box #sslcommerzPayment {
    top: 0;
}

/*Checkout Page Rightside css*/
.checkout-table tr:last-child td {
    border-bottom: 0;
}

.checkout-right-side-box .accordion-button:not(.collapsed),
.checkout-right-side-box .accordion-button {
    background-color: var(--white-color);
    font-weight: 500;
    padding: 15px 0 15px;
}

.checkout-right-side-box {
    box-shadow: 0 0 1px 1px rgb(0 0 0 / 3%);
    margin-bottom: 30px;
    border: 1px solid #EEEBEB;
    padding: 0 30px;
    background-color: var(--white-color);
}

.checkout-right-side-box .accordion-collapse {
    box-shadow: none;
}

.checkout-right-side-box .accordion-body {
    padding: 0;
}

.checkout-right-side-box .accordion-header {
    margin-bottom: 0;
}

/* .checkout-course-item .course-item.wishlist-item {
    width: 234px;
} */
.checkout-course-item .course-item.wishlist-item {
    width: 260px;
}

.checkout-course-item .wishlist-item .course-img-wrap {
    width: 60px;
    max-height: 60px;
}

.checkout-course-item .course-img-wrap img {
    width: 60px;
    max-height: 60px;
    height: auto;
}

.checkout-course-item {
    padding: .5rem .3rem !important;
}

.checkout-course-item .course-item.wishlist-item .card-body {
    padding: 0 0 0 10px;
}

.checkout-table .wishlist-remove {
    margin-bottom: 20px;
}

.checkout-table .wishlist-remove button {
    font-size: 21px !important;
}

.checkout-we-protect-content .iconify {
    width: 28px;
    height: 28px;
    border: 1px solid;
    border-radius: 50%;
    padding: 4px;
}

.checkout-course-item .wishlist-item .course-title {
    font-size: 14px;
}

/*Billing Summary Table*/
.table.billing-summary-table td {
    border-bottom: 0;
    padding-bottom: 10px;
}

.table.billing-summary-table tr td:last-child {
    font-weight: 500;
    text-align: right;
}

.billing-summary-table tbody tr:last-child th {
    border-top-width: 1px;
    border-bottom: 0;
    padding-top: 13px;
    font-weight: 500;
}

.billing-summary-table tbody tr:last-child th:last-child {
    text-align: right;
}

/*-------------------------------------------
    39. Checkout Page End
-------------------------------------------*/
/*-------------------------------------------
    40. Sign Up Page Start
-------------------------------------------*/
.sign-up-page {
    overflow-x: hidden;
}

.sign-up-page .row .col-md-5,
.sign-up-page .row .col-md-7 {
    padding: 0;
}

.sign-up-left-content {
    background-image: url("../img/sign-up-page-bg.png");
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    padding: 30px 100px;
}

.sign-up-bottom-img {
    margin-top: 130px;
}

.sign-up-bottom-img img {
    height: auto;
    width: 561px;
}

.sign-up-left-content,
.sign-up-right-content {
    min-height: 100vh;
    height: 100%;
}

.sign-up-right-content {
    align-items: center;
    display: flex;
}

.sign-up-right-content form {
    width: 479px;
    margin: 0 auto;
}

.sign-up-left-content p {
    font-size: 26px;
    line-height: 137%;
    color: rgba(255, 255, 255, 0.86);
    margin-top: 100px;
    padding-right: 115px;
}

/*Show/Hide Password*/
.sign-up-right-content .pass-icon {
    font-size: 13px;
    float: right;
    top: 18px;
    position: absolute;
    right: 10px;
}

.google-login {
    background-color: #DB4437 !important;
    border-color: #DB4437 !important;
}

.google-login:hover {
    background-color: var(--white-color) !important;
    color: #DB4437 !important;
    border-color: #DB4437 !important;
}

.facebook-login {
    background-color: #3b5998 !important;
    border-color: #3b5998 !important;
}

.facebook-login:hover {
    background-color: var(--white-color) !important;
    color: #3b5998 !important;
    border-color: #3b5998 !important;
}

.twitter-login {
    background-color: #55acee !important;
    border-color: #55acee !important;
}

.twitter-login:hover {
    background-color: var(--white-color) !important;
    color: #55acee !important;
    border-color: #55acee !important;
}

/*-------------------------------------------
    40. Sign Up Page End
-------------------------------------------*/

/*-------------------------------------------
    41. Home All Instructor Area Page Start
-------------------------------------------*/
.instructor-item {
    margin-top: 10px;
}

.instructor-img-wrap {
    min-height: 228px;
    width: 100%;
}

.instructor-img-wrap img {
    min-width: 100%;
    min-height: 228px;
    width: auto;
}

.instructor-my-courses-btns .iconify {
    margin-right: 4px;
}

.instructor-my-courses-btns button,
.instructor-my-courses-btns a {
    margin-right: 13px;
}

/* Quiz List Page Start */
.quiz-list-page-top {
    background-image: url('../img/quiz-img/quiz-list-top-banner.jpg');
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
}

.quiz-list-page-top {
    background-image: url('../img/quiz-img/quiz-list-top-banner.jpg');
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    padding: 30px 20px;
    border-radius: 10px;
}

.create-new-quiz-btn {
    background-color: var(--white-color);
    border-radius: 3px;
    padding: 11px 26px;
    display: inline-block;
    color: var(--theme-color);
}

.instructor-quiz-list-page table {
    border: 1px solid #EEEBEB;
}

.instructor-quiz-list-page table thead tr:first-child th {
    border-top-left-radius: 3px;
}

.instructor-quiz-list-page table thead tr:last-child th {
    border-top-right-radius: 3px;
}

.instructor-quiz-list-page table th {
    font-size: 15px;
    line-height: 18px;
    font-weight: 600;
    color: var(--heading-color);
    padding: 20px;
}

.instructor-quiz-list-page table td {
    color: var(--gray-color);
    padding: 20px;
    min-width: 100px;
    max-width: 100%;
}

.instructor-quiz-list-page table .theme-btn {
    padding: 7px 18px !important;
    font-weight: 500 !important;
    min-width: 111px;
    margin-top: 2px;
    margin-bottom: 2px;
}

.quiz-status {
    background-color: #D0FFDB;
    padding: 5px 8px;
    text-align: center;
    color: #0A800E;
    border-radius: 5px;
}

.quiz-status.unpublish {
    background-color: #FFEEC8;
    color: #AB7501;
}

.add-question-btn {
    padding: 7px 18px !important;
    color: var(--white-color);
    border-radius: 3px;
    display: inline-block;
    font-size: 15px;
    min-width: 131px !important;
}

.instructor-quiz-list-page .dropdown button {
    color: var(--gray-color);
    font-size: 20px;
}

.instructor-quiz-list-page .dropdown-menu {
    min-width: 8rem;
    border-color: #F1F1F1;
}

.instructor-quiz-list-page .dropdown li a {
    color: var(--body-font-color);
    font-size: 15px;
    line-height: 22px;
}

.instructor-quiz-list-page .dropdown li a .iconify {
    font-size: 17px;
    margin-right: 10px;
}

/* Quiz List Page End */

/* Create New Quiz page Start */
.instructor-create-new-quiz-page .theme-btn {
    margin-right: 10px;
    border-width: 1px;
}

.instructor-create-new-quiz-page .quiz-back-btn {
    border: 1px solid #D8D8D8;
    background-color: var(--white-color);
    color: var(--body-font-color);
}

.instructor-create-new-quiz-page .theme-btn:hover {
    border: 1px solid var(--theme-color);
    background-color: var(--white-color);
    color: var(--theme-color);
}

/* Create New Quiz page End */

/* Add Question Page Start */
.instructor-add-question-page .instructor-my-courses-title .theme-btn:hover {
    border-color: var(--theme-color);
}

.instructor-add-question-page .openion-item .form-check {
    min-width: 134px;
    margin-left: 20px;
}

.question-openion-box {
    border-top: 1px solid #EBEBEB;
    padding: 30px 0 0;
}

.question-openion-btn-wrap {
    border-top: 1px solid #EBEBEB;
    padding-top: 30px;
}

.add-question-save-btns .theme-btn {
    margin-right: 10px;
    margin-bottom: 10px;
    border-width: 1px;
}

.add-question-save-btns .theme-btn:hover {
    border-color: var(--theme-color);
}

.delete-question-btn .iconify {
    color: var(--gray-color);
    font-size: 29px;
}

/* True/False Question Start */
.instructor-add-true-false-question .true-false-item-wrap .form-check {
    min-width: 82px;
}

/* True/False Question End */

/* Add Question Page End */

/* Quiz Details Page Start */
.instructor-quiz-details-page table td>.iconify {
    color: #45C881;
    font-size: 20px;
    margin-right: 5px;
}

.quiz-details-action-btns .quiz-details-btn {
    font-size: 20px;
    color: var(--body-font-color);
    margin: 0 3px;
}

.quiz-details-action-btns .quiz-details-btn:hover {
    color: #FF3913;
}

.default-hover-btn {
    border-width: 1px;
}

.default-hover-btn:hover {
    border-color: var(--theme-color);
    border-width: 1px;
}

/* Quiz Details Page End */

/* Create Assignment Page Start */
.create-assignment-upload-files {
    background-color: #FAFAFA;
    box-shadow: 0 2px #EDEDED;
    border: 1px solid #EDEDED;
    height: 259px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border-radius: 4px;
}

.create-assignment-upload-files button .iconify {
    margin-right: 8px;
}

/* Create Assignment Page End */

/* Assignment Assessment Page Start */
.instructor-assignment-assessment-page .instructor-my-courses-title {
    border-bottom: none;
}

.assignment-nav-tabs {
    border-bottom: 1px solid #E5E8EC;
}

.assignment-nav-tabs .nav-link {
    font-size: 20px;
    font-weight: 500;
    color: var(--gray-color);
    border-radius: 0;
    border-width: 2px;
    border: none;
    padding: 0 0 6px;
    margin-right: 30px;
}

.assignment-nav-tabs .nav-link.active::after {
    display: none;
}

.assignment-nav-tabs .nav-item.show .nav-link,
.assignment-nav-tabs .nav-link.active {
    color: var(--heading-color);
    border-bottom: 2px solid var(--theme-color) !important;
}

.assignment-assessment-item {
    background-color: #FCFCFC;
    border: 1px solid #E5E8EC;
    padding: 20px;
    margin-top: 25px;
}

.assignment-img-wrap {
    height: 70px;
    width: 70px;
    border-radius: 50px;
    overflow: hidden;
}

.assignment-img-wrap img {
    min-width: 70px;
    min-height: 70px;
    height: 70px;
}

.assignment-btn-group {
    flex-wrap: wrap;
}

.assignment-btn-group .theme-btn {
    height: 40px;
    margin-right: 11px;
    padding: 9.5px 15px !important;
}

.assignment-btn-group .theme-btn .iconify {
    font-size: 76px;
    margin-right: 7px;
}

.assignment-btn-group input {
    width: 113px;
    border: 1px solid #E5E8EC;
    border-radius: 4px;
    padding: 6px 15px;
    height: 40px;
}

.assignment-assessment-item textarea {
    height: 83px;
    font-size: 12px;
    margin-bottom: 15px;
}

/* Done Assignment css */
.done-assignment-item .assignment-notes-text {
    margin-bottom: 15px;
    line-height: 18px;
    font-size: 13px;
}

.done-assignment-item .assignment-edit-btn {
    background-color: #42B7FF;
    border: 1px solid #42B7FF;
    color: var(--white-color);
}

.done-assignment-item .assignment-edit-btn:hover {
    background-color: var(--white-color);
    border: 1px solid #42B7FF;
    color: #42B7FF;
}

#assignmentEditModal textarea {
    height: 150px;
}

.done-assignment-item .assignment-btn-group input {
    background-color: #F2F3F4;
}

#assignmentEditModal .card-title {
    /* text-transform: capitalize; */
}

.assignment-edit-modal-box {
    border: 1px solid #E5E8EC;
    background-color: #FCFCFC;
    padding: 21px;
}

.assignment-edit-modal-box input {
    width: auto;
}

/* Done Assignment css */

/* Assignment Assessment Page End */

/* Empty Data value css */
.empty-data {
    text-align: center;
    border: 1px solid #E5E8EC;
    padding: 80px 30px;
    width: 100%;
}

.empty-data p {
    width: 77%;
    margin: auto;
    line-height: 28px;
}

/* Empty Data value css */

/* Instructor Resources Page */
.instructor-resources-page .empty-data {
    margin-bottom: 15px;
}

.resource-list-text a {
    margin: 0 10px 0 8px;
    color: var(--theme-color);
}

.add-resources-btn {
    margin-top: 15px;
}

.iconify.resource-list-text-remove {
    color: #000;
    margin-left: 15px;
}

.iconify.resource-list-text-remove:hover {
    color: #FF3913;
}

.instructor-edit-resource-page .resource-list-text {
    margin-bottom: 30px;
}

/* Instructor Resources Page */

/* Instructor Notice Board Page */
.notice-board-action-btns .theme-btn {
    margin-right: 5px;
}

.instructor-notice-board-page table th {
    width: 160px;
}

.red-blue-action-btns .theme-btn {
    margin-right: 9px;
}

.red-blue-action-btns .green-theme-btn {
    min-width: 93px !important;
}

.instructor-notice-details-page p {
    color: var(--gray-color);
}

/* Instructor Notice Board Page */

/* Instructor Live Class Page */
.live-class-list-nav-tabs {
    margin-bottom: 30px;
}

.instructor-live-class-list-page table .theme-btn {
    min-width: auto;
}

.instructor-live-class-page .notice-board-action-btns {
    min-width: 277px;
}

.table-data-img-wrap {
    height: 40px;
    width: 50px;
    border-radius: 4px;
    overflow: hidden;
}

.table-data-img-wrap img {
    min-height: 40px;
    min-width: 50px;
}

.table-data-course-name {
    margin-left: 10px;
}

/* Live Class View Meeting Modal */
#viewMeetingModal textarea {
    height: auto !important;
    min-height: 146px;
}

#viewMeetingModal .form-control {
    color: var(--gray-color);
    border: 1px solid #CED3DB;
}

.copy-text-btn {
    right: 10px;
    bottom: 4px;
    color: var(--gray-color);
    font-size: 18px;
}

@media only screen and (min-width: 992px) {
    #viewMeetingModal .modal-dialog {
        max-width: 871px;
    }
}

/* Live Class View Meeting Modal */

.instructor-live-class-list-page .instructor-my-courses-title {
    border-bottom: none;
}

/* Instructor Live Class Page */

/* Instructor All Student Page */
.all-student-filter-dropdown-btn {
    color: var(--heading-color) !important;
    font-size: 18px !important;
    line-height: 18px;
    border: 1px solid #E5E8EC;
    border-radius: 5px;
    height: 43px;
    padding: 8px 18px;
}

.all-student-filter-dropdown-btn .iconify {
    margin-right: 5px;
}

.instructor-all-student-page .dropdown-menu {
    min-width: 13.5rem;
    padding: 16px;
}

.instructor-all-student-page .dropdown-menu .form-control,
.instructor-all-student-page .dropdown-menu .form-select,
.instructor-all-student-page .dropdown-menu .theme-btn {
    height: 36px;
    font-size: 14px !important;
    width: 100%;
}

.all-student-img-wrap {
    height: 35px;
    width: 35px;
    border-radius: 50px;
    overflow: hidden;
}

.all-student-img-wrap img {
    min-height: 35px;
    min-width: 35px;
}

/* All student Modal */
.all-student-modal-img .all-student-img-wrap {
    height: 40px;
    width: 40px;
}

.all-student-modal-img .all-student-img-wrap img {
    min-height: 40px;
    min-width: 40px;
}

.all-student-modal-img span {
    margin-left: 15px;
}

.all-student-info-title span {
    float: right;
}

.all-student-modal-inner {
    margin-top: 20px;
}

.all-student-info-title {
    width: 175px;
}

/* All student Modal */

/* Instructor All Student Page */

/* Instructor Certificate Page */
.instructor-certificate-course-img {
    height: 40px;
    max-width: 50px;
    border-radius: 5px;
    overflow: hidden;
}

.instructor-certificate-course-list-wrap span {
    margin-left: 15px;
}

.inner-next-btn-wrap .theme-btn {
    float: right;
}

/* Instructor Certificate Page */

/* Instructor Payment Settings Page */
.add-payment-settings-title {
    background-color: #F6F7FA;
    height: 46px;
    padding: 10px 20px;
}

.payment-settings-box form {
    border-top: 1px solid #E5E8EC;
    padding: 20px;
}

.payment-settings-box {
    border: 1px solid #E5E8EC;
}

/* Instructor Payment Settings Page */
/*-------------------------------------------
    41. Home All Instructor Area Page End
-------------------------------------------*/

/*-------------------------------------------
    42. Student My Learning Area Page Start
-------------------------------------------*/
.my-learning-give-review .iconify {
    font-size: 13px;
    top: -1px;
    position: relative;
}

.my-learning-invoice:hover {
    color: var(--gray-color2);
}

.wishlist-course-item .card-body .my-learning-invoice img {
    height: 14px;
}

/* Thank you Page CSS Start */
.min-h-auto {
    min-height: auto;
}

.thankyou-course-list-area .wishlist-item .course-img-wrap {
    width: 100px;
}

.thankyou-course-list-area .table>:not(caption)>*>* {
    padding: 1rem 1.75rem;
}

/* Thank you Page CSS End */

/*-------------------------------------------
    42. Student My Learning Area Page End
-------------------------------------------*/

/*-------------------------------------------
    43. Sweet Alert Area Page Start
-------------------------------------------*/
.swal2-icon {
    width: 2.75em !important;
    height: 2.75em !important;
}

.swal2-icon .swal2-icon-content {
    font-size: 1.75em !important;
}

.swal2-styled.swal2-confirm {
    background-color: var(--theme-color) !important;
}

.swal2-title {
    font-size: 1.5em !important;
    color: var(--theme-color) !important;
    font-weight: 500 !important;
}

.swal2-styled {
    padding: .500em 1em !important;
    box-shadow: none !important;
}

/*Error Alert*/
.swal2-icon.swal2-error [class^=swal2-x-mark-line] {
    top: 2.3125em !important;
    width: 2.5em !important;
}

.swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=left] {
    left: 3px !important;
    top: 19px !important;
}

.swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=right] {
    right: 3px !important;
    top: 19px !important;
}

/*Error Alert*/
/*-------------------------------------------
    43. Sweet Alert Area Page End
-------------------------------------------*/

/*-------------------------------------------
    44. Cookie Concent Area Page Start
-------------------------------------------*/
.cookie-consent {
    position: fixed;
    bottom: 5px;
    left: 12px;
    width: 296px;
    padding-top: 7px;
    min-height: 140px;
    color: #000 !important;
    line-height: 26px;
    padding-left: 23px;
    padding-right: 33px;
    font-size: 16px;
    background: #f4f4f4;
    z-index: 9999;
    cursor: pointer;
    border-radius: 3px;
    border: 1px solid #e9e9e9;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.main-cookie-content .primary-btn {
    display: inline-block;
    padding: .3rem 1.1rem;
    /* text-transform: capitalize; */
    color: var(--white-color);
    background-color: var(--theme-color);
    -webkit-transition: all .3s linear;
    transition: all .3s linear;
    border: none;
    border-radius: 5px;
    margin-top: 10px;
}

.front-close-btn.btn-close {
    top: 3px;
    right: 3px;
    font-size: 10px;
    padding: 6px;
}

/*-------------------------------------------
    44. Cookie Concent Area Page End
-------------------------------------------*/

/*-------------------------------------------
    45. Instructor Consultation Page Area Start
-------------------------------------------*/
.consultation-instructor-item .course-img-wrap a {
    display: block;
}

.consultation-instructor-item .course-img-wrap:before {
    display: none;
}

.one-to-one-slider-items .search-instructor-img-wrap img {
    margin-left: auto;
    margin-right: auto;
}

.consultation-tag {
    background-image: url('../img/consultation-img/consultation-tag.png');
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    right: 6px;
    top: 10px;
    font-size: 14px;
    padding: 5px 30px 5px 12px;
    z-index: 9;
}

/* Hide Calendar Icon In Chrome */
input[type="date"]::-webkit-inner-spin-button,
input[type="date"]::-webkit-calendar-picker-indicator {
    -webkit-appearance: none;
    color: var(--border-color);
}

#consultationBookingModal .modal-dialog {
    max-width: 760px;
}

#consultationBookingModal .modal-body {
    padding: 2rem 2rem .75rem;
    border-top: var(--bs-modal-header-border-width) solid var(--bs-modal-header-border-color);
}

#consultationBookingModal .input-group {
    align-items: center;
    width: auto;
    justify-content: space-between;
}

#consultationBookingModal .input-group>:not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback) {
    align-items: center;
    height: 40px;
    border-radius: 4px;
}

#consultationBookingModal .consultantion-hours-box input {
    background-color: #E5E8EC;
    text-align: center;
}

#consultationBookingModal .consultantion-hours-box {
    min-width: 310px !important;
}

#consultationBookingModal .consultantion-calendar-box label {
    margin-right: 3px;
}

#consultationBookingModal .consultantion-hours-box label {
    margin-right: 13px;
}

.btn-outline-primary {
    color: var(--theme-color);
    border-color: var(--theme-color);
    transition: all .5s ease-in-out;
}

.time-slot-item label {
    margin: 0 0 25px;
    padding: 7px 5px;
    width: 100%;
    font-weight: 500;
    transition: all .5s ease-in-out;
}

.time-slot-item .btn-outline-primary:hover,
.time-slot-item .btn-check:active+.btn-outline-primary,
.time-slot-item .btn-check:checked+.btn-outline-primary,
.btn-outline-primary.active,
.btn-outline-primary.dropdown-toggle.show,
.btn-outline-primary:active,
.time-slot-item .btn-check:focus+.btn,
.time-slot-item .btn:focus {
    color: var(--white-color);
    background-color: var(--theme-color);
    border-color: var(--theme-color);
}

.time-slot-item .btn-check:focus+.btn,
.time-slot-item .btn:focus {
    box-shadow: none;
    transition: all .5s ease-in-out;
}

/* Consultation Offday css */
.offDayDeactive.disabled-btn {
    cursor: pointer;
}

/* Consultation Offday css */

/* New design update consultation booking */
.booking-header-row {
    padding: 1rem 2rem;
}

.consultation-select-date-hour {
    justify-content: space-between;
}

/* New design update consultation booking */

/* Custom Datepicker Calendar Start */
.ui-datepicker-div label {
    font-size: 0.75rem;
    font-weight: 400;
    display: block;
    margin-bottom: 0.5rem;
    color: #B0BEC5;
    border: 1px solid #ECEFF1;
    padding: 0.5rem 0.75rem;
    border-radius: 0.5rem;
}

.ui-datepicker-div input {
    font-family: 'Roboto', sans-serif;
    display: block;
    border: none;
    border-radius: 0.25rem;
    border: 1px solid transparent;
    line-height: 1.5rem;
    padding: 0;
    font-size: 1rem;
    color: #607D8B;
    width: 100%;
    margin-top: 0.5rem;
}

.ui-datepicker-div input:focus {
    outline: none;
}

.ui-datepicker-div #ui-datepicker-div {
    display: none;
    background-color: var(--white-color);
    box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.1);
    margin-top: 0.25rem;
    border-radius: 0.5rem;
    padding: 0.5rem;
}

.ui-datepicker-div table {
    border-collapse: collapse;
    border-spacing: 0;
}

.ui-datepicker-div .ui-datepicker-calendar thead th {
    padding: 0.25rem 0;
    text-align: center;
    font-size: 0.75rem;
    font-weight: 400;
    color: #78909C;
}

.ui-datepicker-div .ui-datepicker-calendar tbody td {
    width: 2.5rem;
    text-align: center;
    padding: 0;
}

.ui-datepicker-div .ui-datepicker-calendar tbody td a {
    display: block;
    border-radius: 0.25rem;
    line-height: 2rem;
    transition: 0.3s all;
    color: #546E7A;
    font-size: 0.875rem;
    text-decoration: none;
}

.ui-datepicker-div .ui-datepicker-calendar tbody td a:hover {
    background-color: #E0F2F1;
}

.ui-datepicker-div .ui-datepicker-calendar tbody td a.ui-state-active {
    background-color: #009688;
    color: var(--white-color);
}

.ui-datepicker-div .ui-datepicker-header a.ui-corner-all {
    cursor: pointer;
    position: absolute;
    top: 0;
    width: 2rem;
    height: 2rem;
    margin: 0.5rem;
    border-radius: 0.25rem;
    transition: 0.3s all;
}

.ui-datepicker-div .ui-datepicker-header a.ui-corner-all:hover {
    background-color: #ECEFF1;
}

.ui-datepicker-div .ui-datepicker-header a.ui-datepicker-prev {
    left: 0;
    background: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMyIgaGVpZ2h0PSIxMyIgdmlld0JveD0iMCAwIDEzIDEzIj48cGF0aCBmaWxsPSIjNDI0NzcwIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik03LjI4OCA2LjI5NkwzLjIwMiAyLjIxYS43MS43MSAwIDAgMSAuMDA3LS45OTljLjI4LS4yOC43MjUtLjI4Ljk5OS0uMDA3TDguODAzIDUuOGEuNjk1LjY5NSAwIDAgMSAuMjAyLjQ5Ni42OTUuNjk1IDAgMCAxLS4yMDIuNDk3bC00LjU5NSA0LjU5NWEuNzA0LjcwNCAwIDAgMS0xLS4wMDcuNzEuNzEgMCAwIDEtLjAwNi0uOTk5bDQuMDg2LTQuMDg2eiIvPjwvc3ZnPg==");
    background-repeat: no-repeat;
    background-size: 0.5rem;
    background-position: 50%;
    transform: rotate(180deg);
}

.ui-datepicker-div .ui-datepicker-header a.ui-datepicker-next {
    right: 0;
    background: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMyIgaGVpZ2h0PSIxMyIgdmlld0JveD0iMCAwIDEzIDEzIj48cGF0aCBmaWxsPSIjNDI0NzcwIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik03LjI4OCA2LjI5NkwzLjIwMiAyLjIxYS43MS43MSAwIDAgMSAuMDA3LS45OTljLjI4LS4yOC43MjUtLjI4Ljk5OS0uMDA3TDguODAzIDUuOGEuNjk1LjY5NSAwIDAgMSAuMjAyLjQ5Ni42OTUuNjk1IDAgMCAxLS4yMDIuNDk3bC00LjU5NSA0LjU5NWEuNzA0LjcwNCAwIDAgMS0xLS4wMDcuNzEuNzEgMCAwIDEtLjAwNi0uOTk5bDQuMDg2LTQuMDg2eiIvPjwvc3ZnPg==');
    background-repeat: no-repeat;
    background-size: 10px;
    background-position: 50%;
}

.ui-datepicker-div .ui-datepicker-header a>span {
    display: none;
}

.ui-datepicker-div .ui-datepicker-title {
    text-align: center;
    line-height: 2rem;
    margin-bottom: 0.25rem;
    font-size: 0.875rem;
    font-weight: 500;
    padding-bottom: 0.25rem;
}

.ui-datepicker-div .ui-datepicker-week-col {
    color: #78909C;
    font-weight: 400;
    font-size: 0.75rem;
}

.ui-datepicker-calendar tbody tr {
    display: flex !important;
}

.ui-datepicker-calendar thead tr {
    display: flex !important;
}

.ui-datepicker-calendar thead tr th {
    width: 37px !important;
    min-width: 37px !important;
    font-weight: 400;
}

.ui-datepicker-calendar tbody tr td,
.ui-datepicker-next.ui-corner-all {
    width: 37px !important;
}

#ui-datepicker-div .ui-widget-header {
    background: transparent;
    border: none;
}

#ui-datepicker-div {
    /* display: none; */
    background-color: var(--white-color);
    box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.1);
    border-radius: 0.5rem;
    border: 1px solid transparent;
}

#ui-datepicker-div .ui-state-default,
#ui-datepicker-div .ui-widget-content .ui-state-default,
#ui-datepicker-div .ui-widget-header .ui-state-default,
#ui-datepicker-div .ui-button {
    border: 1px solid transparent !important;
    text-align: center !important;
    border-radius: 5px !important;
}

#ui-datepicker-div .ui-datepicker-header a.ui-datepicker-prev,
#ui-datepicker-div .ui-datepicker-header a.ui-datepicker-next {
    border: 1px solid transparent;
}

#ui-datepicker-div .ui-datepicker-header a.ui-datepicker-next {
    right: 0;
    background: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMyIgaGVpZ2h0PSIxMyIgdmlld0JveD0iMCAwIDEzIDEzIj48cGF0aCBmaWxsPSIjNDI0NzcwIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik03LjI4OCA2LjI5NkwzLjIwMiAyLjIxYS43MS43MSAwIDAgMSAuMDA3LS45OTljLjI4LS4yOC43MjUtLjI4Ljk5OS0uMDA3TDguODAzIDUuOGEuNjk1LjY5NSAwIDAgMSAuMjAyLjQ5Ni42OTUuNjk1IDAgMCAxLS4yMDIuNDk3bC00LjU5NSA0LjU5NWEuNzA0LjcwNCAwIDAgMS0xLS4wMDcuNzEuNzEgMCAwIDEtLjAwNi0uOTk5bDQuMDg2LTQuMDg2eiIvPjwvc3ZnPg==');
    background-repeat: no-repeat;
    background-size: 10px;
    background-position: 50%;
}

#ui-datepicker-div .ui-datepicker-header a.ui-datepicker-prev {
    left: 0;
    background: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMyIgaGVpZ2h0PSIxMyIgdmlld0JveD0iMCAwIDEzIDEzIj48cGF0aCBmaWxsPSIjNDI0NzcwIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik03LjI4OCA2LjI5NkwzLjIwMiAyLjIxYS43MS43MSAwIDAgMSAuMDA3LS45OTljLjI4LS4yOC43MjUtLjI4Ljk5OS0uMDA3TDguODAzIDUuOGEuNjk1LjY5NSAwIDAgMSAuMjAyLjQ5Ni42OTUuNjk1IDAgMCAxLS4yMDIuNDk3bC00LjU5NSA0LjU5NWEuNzA0LjcwNCAwIDAgMS0xLS4wMDcuNzEuNzEgMCAwIDEtLjAwNi0uOTk5bDQuMDg2LTQuMDg2eiIvPjwvc3ZnPg==");
    background-repeat: no-repeat;
    background-size: 0.5rem;
    background-position: 50%;
    transform: rotate(180deg);
}

#ui-datepicker-div .ui-icon.ui-icon-circle-triangle-e {
    height: 37px;
    width: 37px;
}

#ui-datepicker-div .ui-datepicker .ui-datepicker-title {
    font-weight: 400;
}

.book-schedule-calendar-wrap #datepicker {
    border: 1px solid #E5E8EC;
    padding: 5px 14px;
    border-radius: 4px;
    position: relative;
    z-index: 9999;
    background-color: transparent;
    height: 40px;
}

.book-schedule-calendar-wrap .iconify {
    right: 11px;
    position: absolute;
    top: 11px;
    z-index: 1;
}

/* Custom Datepicker Calendar End */

/*-------------------------------------------
    45. Instructor Consultation Page Area End
-------------------------------------------*/

/*-------------------------------------------------------------
    46. Instructor Panel Consultation Dashboard Page Area Start
--------------------------------------------------------------*/
.are-you-available-box {
    border: 1px solid rgba(0, 0, 0, 0.11);
    padding: 30px;
}

.instructor-panel-hourly-rate-box label {
    width: 123px;
}

@media only screen and (min-width: 1400px) {
    .time-slot-list-wrap .table {
        width: 58%;
    }
}

/* Add Slot Modal start */
.add-slot-day-item .input-group-text .iconify {
    color: var(--orange-color);
    border: 1px solid var(--orange-color);
    padding: 4px;
    font-size: 26px;
    border-radius: 4px;
}

.add-slot-day-item input {
    border-radius: 4px !important;
    height: 40px;
}

.add-slot-day-item input.time_added_field {
    border: 1px solid var(--theme-color);
    color: var(--theme-color);
}

/* Add Slot Modal end */

/* Instructor Panel Consultation List Page Start */
.instructor-student-img-wrap {
    height: 40px;
    width: 40px;
    overflow: hidden;
}

.instructor-student-img-wrap img {
    height: 100%;
}

.instructor-consultation-list-page table .theme-btn,
.instructor-consultation-list-page .red-blue-action-btns .green-theme-btn,
.time-slot-list-wrap .theme-btn {
    padding: 7px 13px !important;
    min-width: 86px !important;
}

/* Instructor Panel Consultation List Page End */

/* Instructor Panel Booking History Page Start */
.booking-history-tabs table th {
    width: 91px;
}

.booking-history-right {
    width: 40%;
    text-align: right;
}

.booking-table-detail-btn button .iconify {
    color: #627183;
    margin-left: 10px;
    -webkit-transition: .5s ease all;
    transition: .5s ease all;
    -webkit-transform: rotate(-180deg);
    transform: rotate(-180deg);
}

.booking-table-detail-btn button.collapsed .iconify {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
}

.booking-history-past-table.table th {
    width: auto;
}

/* Instructor Panel Booking History Page End */

/*-------------------------------------------------------------
    46. Instructor Panel Consultation Dashboard Page Area End
--------------------------------------------------------------*/

/*-------------------------------------------------------------
    47. courses-bundles-single Page Area Start
--------------------------------------------------------------*/
.courses-bundles-courses-wrap .col-sm-12:last-child .courses-bundles-course-item {
    margin-bottom: 0;
}

.courses-bundles-course-item {
    flex-direction: row;
    min-height: 150px;
    border: 1px solid #E5E8EC;
}

.courses-bundles-course-item .card-body {
    padding: 27px 20px 20px;
}

.courses-bundles-course-item .course-img-wrap {
    width: 30%;
    min-height: 130px;
}

.courses-bundles-course-item .course-img-wrap img {
    min-height: 130px;
}

/* Create Bundles Page style */
.create-bundles-courses-check-btn {
    width: 6%;
    display: flex;
}

.create-bundles-courses-check-btn .form-check-input {
    width: 1.5rem;
    height: 1.5em;
}

.create-bundles-courses-page .pagination {
    margin-bottom: 32px;
    margin-top: 10px;
    border-bottom: 1px solid #E5E8EC;
    padding-bottom: 32px;
}

/* Create Bundles Page style */

/*-------------------------------------------------------------
    47. courses-bundles-single Page Area End
--------------------------------------------------------------*/

/*-------------------------------------------------------------
    48. Forum Page Area Start
--------------------------------------------------------------*/
.forum-banner-content .page-banner-sub-heading {
    color: rgba(255, 255, 255, 0.6);
    width: 67%;
}

.forum-banner-search-ask-wrap .input-group {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid #E5E8EC;
    border-radius: 4px;
    width: 447px;
}

.forum-banner-search-ask-wrap button {
    width: 50px;
    color: var(--white-color);
    font-size: 19px;
}

.forum-banner-search-ask-wrap .theme-button1 {
    height: 50px;
}

.forum-banner-search-ask-wrap .form-control {
    color: var(--white-color);
}

/* Forum Categories Area */
.forum-categories-area .section-heading,
.community-blog-articles-area .section-heading {
    margin-bottom: 44px !important;
    width: 100%;
}

.forum-categories-area .single-feature-item {
    margin-bottom: 30px;
}

/* Forum Categories Area */

/* forum countdown area */
.forum-countdown-area .single-feature-item p {
    color: #E5E8EC;
}

.forum-countdown-area .feature-img-wrap {
    width: 64px;
    height: 64px;
    background: rgba(255, 255, 255, 0.2);
    z-index: 9;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* forum countdown area */

/* Forum Community blog articles area */
.community-blog-articles-area .blog-item {
    margin-bottom: 30px;
}

/* Forum Community blog articles area */

/*-------------------------------------------------------------
    48. Forum Page Area End
--------------------------------------------------------------*/

/*------------------------------------
    49. Ask a question Page Area Start
--------------------------------------*/
.blank-page-banner-wrap.banner-less-header-wrap {
    padding: 50px 0 30px;
    background-color: var(--white-color);
    min-height: auto;
}

.banner-less-header-wrap .breadcrumb-item a .iconify {
    position: relative;
    top: -2px;
}

.banner-less-header-wrap .breadcrumb-item,
.banner-less-header-wrap .breadcrumb-item a {
    font-weight: 500;
}

.breadcrumb-item.active {
    color: var(--white-color);
}

.ask-question-form {
    border: 1px solid #E5E8EC;
}

/*------------------------------------
    49. Ask a question Page Area End
--------------------------------------*/

/*------------------------------------
    50. Summer Note Area Start
--------------------------------------*/
.note-modal-footer {
    height: auto !important;
    padding: 8px 10px 25px !important;
}

.note-modal-footer .note-btn {
    float: initial !important;
}

.note-editor .note-toolbar,
.note-popover .popover-content {
    padding: 10px 0 10px 10px !important;
}

.note-editor.note-airframe,
.note-editor.note-frame {
    border: 1px solid var(--border-color) !important;
}

.note-toolbar {
    background-color: #FCFCFC !important;
    border-color: var(--border-color) !important;
}

.note-editor.note-airframe .note-statusbar,
.note-editor.note-frame .note-statusbar {
    background-color: #FCFCFC !important;
    border-top: 1px solid var(--border-color) !important;
}

.note-editor .note-editing-area {
    min-height: 170px !important;
}

.btn-fullscreen {
    display: none !important;
}

/*------------------------------------
    50. Summer Note Area End
--------------------------------------*/

/*------------------------------------
    51. Forum Categories Single Page Start
--------------------------------------*/

/* forum-categories-left start */
.forum-categories-filter-box {
    margin-bottom: 30px;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 30px;
}

.forum-categories-filter-box select {
    width: 220px;
}

.forum-categories-filter-box button {
    margin-left: 20px;
}

.forum-categories-wrap .forum-category-single-item:last-child {
    margin-bottom: 0;
}

.forum-category-single-item {
    padding: 20px;
    margin-bottom: 25px;
}

.forum-category-single-item-img-wrap {
    width: 50px;
    height: 50px;
}

.forum-category-single-item-bottom {
    border-top: 1px solid var(--border-color);
    padding-top: 15px;
    margin-top: 15px;
}

.forum-category-single-item-bottom-left li {
    margin-right: 20px;
    font-size: 14px;
}

.forum-category-single-item-bottom-left li .iconify,
.forum-category-single-item-bottom-right li .iconify {
    margin-right: 5px;
}

.forum-category-single-item-bottom-left-name {
    background-color: #F2F6FB;
    padding: 4px 10px;
}

.forum-category-single-item-bottom-right li {
    margin-left: 20px;
    font-size: 14px;
}

.forum-category-single-item-bottom-left li .iconify,
.forum-category-single-item-bottom-right li .iconify {
    position: relative;
    top: -1px;
}

/* forum-categories-left end */

/* forum-categories-right */
.forum-ask-question-btn {
    padding: 15px 27px !important;
}

.forum-link-box-title {
    background-color: #F5F9FD;
    border-radius: 3px 3px 0px 0px;
    padding: 16px 27px !important;
    border-top: 0 !important;
}

.forum-link-box-title .iconify {
    position: relative;
    top: -2px;
}

.forum-link-box li {
    border-top: 1px solid var(--border-color);
    padding: 16px 27px;
}

.forum-link-box .forum-author-item {
    border: none;
    padding: 0;
    border-radius: 0;
    margin-bottom: 0;
}

/* forum-categories-right */

/*------------------------------------
    51. Forum Categories Single Page End
--------------------------------------*/

/*------------------------------------
    52. Forum Likes Leaderboard Page Start
--------------------------------------*/

/* Likes leaderboard tabs start */
.forum-likes-tabs .nav-pills {
    border-top: 1px solid var(--border-color);
    border-bottom: 1px solid var(--border-color);
}

.forum-nav-list-title {
    padding: 18px 20px 18px 0;
}

.forum-likes-tabs .nav-pills li {
    font-size: 18px;
    font-weight: 500;
}

.forum-likes-tabs .nav-pills .nav-link {
    color: var(--gray-color);
    font-weight: 500;
    padding: 18px 20px;
    border-radius: 0;
}

.forum-likes-tabs .nav-pills .nav-link.active,
.forum-likes-tabs .nav-pills .show>.nav-link {
    color: var(--gray-color);
    background-color: #F2F2F2;
}

/* Likes leaderboard tabs end */

.forum-author-item {
    border: 1px solid var(--border-color);
    padding: 20px 25px;
    border-radius: 4px;
    margin-bottom: 25px;
}

.forum-author-img-wrap {
    display: block;
    height: 40px;
    width: 40px;
}

.forum-author-img-wrap img {
    height: 40px;
    width: auto;
    min-width: 100%;
}

.author-item-right .iconify {
    position: relative;
    top: -2px;
    margin: 0 4px;
}

.forum-likes-leaderboard-area .pagination {
    margin-top: 5px;
    border-top: 1px solid var(--border-color);
    padding-top: 30px;
}

/*------------------------------------
    52. Forum Likes Leaderboard Page End
--------------------------------------*/

/*------------------------------------
    53. Forum Details Page Start
--------------------------------------*/
.forum-details-item-top {
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 15px;
    margin-bottom: 20px;
    align-items: flex-end;
}

.forum-details-top-left .forum-category-single-item-img-wrap {
    width: 60px;
    height: 60px;
}

.forum-details-item .forum-category-single-item-bottom-right li,
.forum-details-item .forum-category-single-item-bottom-left li {
    font-size: 16px;
    font-weight: 500;
}

.forum-details-item .forum-category-single-item-bottom {
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 15px;
    margin-top: 20px;
}

.forum-details-bottom .forum-category-single-item-bottom-right li {
    margin-left: 11px;
}

.forum-details-item .forum-category-single-item-bottom-left li .iconify,
.forum-details-item .forum-category-single-item-bottom-right li .iconify {
    font-size: 20px;
}

.forum-details-bottom .forum-category-single-item-img-wrap {
    width: 32px;
    height: 32px;
}

.forum-details-bottom .reply-btn {
    margin-left: 28px;
}

/* Forum Details Replies Box css */
.replies-wrap {
    margin-top: 60px;
}

.replies-box {
    margin-top: 30px;
}

.forum-details-item.reply-item {
    padding: 30px 30px 0;
    border: 1px solid #EBEBEB;
    margin: 30px;
    border-radius: 4px;
}

.forum-details-item.reply-item .forum-category-single-item-bottom {
    border-bottom: none;
}

.forum-details-item.reply-item.reply-item-inner {
    margin-left: 55px;
}

/* Forum Details Replies Box css */

/* Suggested Topic Area css */
.suggested-topic-area h3 {
    margin-top: -8px;
}

/* Suggested Topic Area css */

/* Bootstrap Scrollspy Added */
.sticky-top {
    z-index: 9;
}

.forum-details-rightside.sticky-top {
    padding-left: 100px;
    top: calc(100px + 3em);
}

.forum-details-rightside {
    padding-left: 0;
    margin: 10px 0;
    border-radius: 0;
    -webkit-transform: translate3d(0, 0, 0);
}

.timeline-topic-publish-date {
    top: -28px;
}

.timeline-topic-end-date {
    bottom: -28px;
}

.forum-timeline-menu {
    position: relative;
    transition: 0.2s ease-in;
    touch-action: none;
}

.forum-timeline-menu {
    position: relative;
    -webkit-transform: translate3d(0, 0, 0);
    height: auto;
    min-height: 80px;
    max-height: 100%;
}

.forum-timeline-menu:before {
    content: "";
    background-color: #E5E8EC;
    height: 100%;
    width: 5px;
    position: absolute;
    top: 0;
    left: 0;
    border-top-right-radius: 10px;
    border-top-left-radius: 10px;
    border-bottom-right-radius: 10px;
    border-bottom-left-radius: 10px;
}

.list-group-item {
    opacity: 0;
    background-color: transparent;
    border-color: transparent;
    padding: 0 0 0 15px;
    line-height: 16px;
    transition: 0.15s ease-out;
    cursor: pointer;
    height: 0;
}

.timeline-comment-count {
    color: var(--heading-color);
    display: block;
    font-weight: 500;
}

.list-group-item.active {
    opacity: 1;
}

.list-group-item.active {
    background-color: transparent;
    border-color: transparent;
    color: #767588;
}

.list-group-item.active {
    height: auto;
    min-height: 30px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    cursor: ns-resize;
    align-items: center;
}

.list-group-item.active::after {
    content: "";
    left: -1px;
    background-color: #B5B4BD;
    height: 100%;
    width: 5px;
    position: absolute;
    top: -1px;
    border-top-right-radius: 10px;
    border-top-left-radius: 10px;
    border-bottom-right-radius: 10px;
    border-bottom-left-radius: 10px;
}

/* Custom Timeline Scrollbar start */
.forum-details-rightside .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: #E6E3EB var(--white-color);
}

.forum-details-rightside .custom-scrollbar::-webkit-scrollbar {
    width: 4px;
    height: 10px;
}

.forum-details-rightside .custom-scrollbar::-webkit-scrollbar-track {
    background-color: var(--white-color);
}

.forum-details-rightside .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: #E6E3EB;
}

.forum-details-rightside .custom-scrollbar::-webkit-scrollbar-track,
.forum-details-rightside .custom-scrollbar::-webkit-scrollbar-thumb {
    border-radius: 5px;
}

/* Custom Timeline Scrollbar end */

/* Bootstrap Scrollspy Added */

/*------------------------------------
    53. Forum Details Page End
--------------------------------------*/

/*------------------------------------
    54. Affiliate Page Start
--------------------------------------*/

/* Become an affiliator page */
.affiliator-dashboard-wrap {
    padding: 50px;
}

.affiliator-dashboard-wrap .create-assignment-upload-files {
    padding: 20px;
}

/* Become an affiliator page */

/* Your application Page */
.affiliator-dashboard-wrap table {
    border: 1px solid var(--border-color2);
}

/* Your application Page */

/* Affiliate Dashboard Page */
.affiliate-dashboard-item {
    background-color: #f7f5ff;
}

.affiliate-dashboard-item-title {
    word-wrap: break-word;
    font-size: 29px;
}

.affiliate-top-title-btns .theme-btn {
    margin: 5px 0 5px 5px;
}

.affiliate-export-btns {
    margin-top: 25px;
}

.affiliate-tbl-action-btns {
    font-size: 19px;
}

.affiliate-tbl-action-btns .delete-btn:hover {
    color: #FF1F1F;
}

.affiliate-history-tbl-course-title {
    max-width: 230px;
}

/* Affiliate Dashboard Page */

/* Affiliate Link Box in course page */
.course-info-box-affiliate-link-copy input {
    background-color: var(--page-bg);
    padding: 15px 40px 15px 15px;
    height: 50px;
}

.course-info-box-affiliate-link-copy input:focus {
    background-color: var(--page-bg);
}

.affiliate-copy-btn {
    right: 12px;
    top: 9px;
    color: var(--heading-color);
    padding: 5px;
    z-index: 9 !important;
}

.affiliate-copy-btn:hover {
    color: var(--theme-color);
}

/* Affiliate Link Box in course page */

/*------------------------------------
    54. Affiliate Page End
--------------------------------------*/

/*------------------------------------
    55. Withdrawal Data table Page Start
--------------------------------------*/
.dataTables_info {
    position: absolute;
}

/*------------------------------------
    55. Withdrawal Data table Page End
--------------------------------------*/

/* Session Onlin Show CSS Start */
.session-online {
    background-color: #45C881;
    height: 8px;
    width: 8px;
    border-radius: 50%;
    display: inline-flex;
    position: relative;
    top: -1px;
    margin-right: 5px;
}

/* Session Onlin Show CSS Start */
/*------------------------------------
    56. Instructor Search Page Start
--------------------------------------*/
.instructors-filter-bar {
    padding-bottom: 20px;
}

.show-all-instructors-area {
    margin-top: 15px;
}

/* Search Instructor Item CSS */
.search-instructor-item {
    box-shadow: 0px 4px 25px rgba(217, 217, 217, 0.25);
    margin-top: 0;
    height: 100%;
}

.search-instructor-item .card-body {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
}

.search-instructor-img-wrap img {
    height: 118px;
    width: 118px !important;
}

.search-instructor-award-img {
    margin-bottom: 10px;
}

.search-instructor-award-img img {
    height: 30px;
    width: 30px !important;
    margin: 0 4px 4px;
}

.instructor-price-cutoff.badge {
    padding: 8px 10px 7px;
    top: 20px;
    right: 20px;
}

/* Search Instructor Item CSS */

/* ----Instructor Search Map Area---- */
#map {
    height: 600px;
}

.leaflet-popup-content {
    padding: 0 !important;
    max-width: 180px;
}

.leaflet-popup-content .search-instructor-item {
    padding: 13px 5px;
    margin-bottom: 0;
}

.leaflet-popup-content .search-instructor-img-wrap img {
    height: 50px;
    width: 50px !important;
}

.leaflet-popup-content .search-instructor-item .card-title,
.leaflet-popup-content .search-instructor-item .course-rating ul li .iconify {
    font-size: 12px;
}

.leaflet-popup-content .search-instructor-item .card-title a {
    color: var(--heading-color) !important;
}

.leaflet-popup-content .search-instructor-item .star-ratings,
.leaflet-popup-content .search-instructor-item .star-ratings .empty-ratings {
    font-size: 14px;
    padding-bottom: 2px;
}

.leaflet-popup-content .search-instructor-item .instructor-designation,
.leaflet-popup-content .search-instructor-item .search-instructor-rating {
    font-size: 11px;
}

.leaflet-popup-content .search-instructor-item .search-instructor-award-img img {
    height: 20px;
    width: 20px !important;
}

.leaflet-popup-content .search-instructor-item .green-theme-btn {
    padding: 2.5px 4px !important;
    font-size: 11px !important;
    font-weight: 500 !important;
}

.leaflet-popup-content .search-instructor-price,
.leaflet-popup-content .search-instructor-award-img,
.leaflet-popup-content .search-instructor-rating,
.leaflet-popup-content .instructor-designation,
.leaflet-popup-content .search-instructor-item .card-title,
.leaflet-popup-content .search-instructor-img-wrap {
    margin-bottom: 5px;
}

#map .leaflet-popup-close-button {
    right: 0px !important;
    top: -1px !important;
    font-size: 18px !important;
}

#map .leaflet-container img.leaflet-marker-icon {
    object-fit: cover;
}

.leaflet-popup-content .search-instructor-item .instructor-price-cutoff.badge {
    padding: 5px 10px 4px;
    top: 18px;
    right: 7px;
    font-size: 11px;
}

#map .leaflet-bottom,
#map .leaflet-top {
    z-index: 9;
}

/* ----Instructor Search Map Area---- */

/*------------------------------------
    56. Instructor Search Page End
--------------------------------------*/

/*------------------------------------
    57. Organization Single Page Start
--------------------------------------*/
.organization-single-tab-area {
    padding: 10px 0 25px;
}

.organization-single-tab-area .tab-nav-list {
    margin: 0 18px;
}

.organization-single-tab-area .tab-content {
    padding: 40px;
}

/* Organization dashboard */
.status.form-select {
    min-width: 113px;
    padding: 6px 27px 7px 15px;
}

#donut .apexcharts-canvas {
    position: absolute;
    left: -5px;
}

/* Organization Student profile Start */
.organization-stu-personal-info-tbl .table,
.organization-stu-profile-right-certificate .table {
    border: 0;
}

.organization-stu-personal-info-tbl .table td,
.organization-stu-profile-right-certificate .table td {
    padding: 10px 4px;
    color: var(--body-font-color);
    border-color: var(--border-color);
}

.organization-stu-profile-right-certificate .table td {
    padding: 10px 20px;
    color: var(--body-font-color);
    border-color: var(--border-color);
}

.organization-stu-profile-right-certificate .table tr:last-child td {
    border-color: transparent;
    padding-bottom: 0;
}

.organization-social-box ul,
.organization-stu-skill-box ul {
    display: inline-flex;
}

.organization-social-box ul li a {
    font-size: 26px;
    margin-right: 5px;
    display: inline-block;
}

.organization-stu-skill-box ul li a {
    margin-right: 10px;
    margin-bottom: 10px;
    color: var(--body-font-color);
    background-color: #F9F9F9;
    display: inline-block;
}

.organization-social-box ul li a:hover,
.organization-stu-skill-box ul li a:hover {
    color: var(--theme-color);
}

.instructor-dashboard-top-part div:nth-child(1) .instructor-dashboard-top-part-item .instructor-dashboard-top-part-icon {
    background-color: rgba(103, 69, 200, 0.12);
    color: var(--theme-color);
    border: 1px solid rgba(103, 69, 200, 0.08);
}

.organization-stu-profile-right-top-part div:nth-child(2) .instructor-dashboard-top-part-item .instructor-dashboard-top-part-icon {
    background-color: rgba(252, 128, 104, 0.2);
    color: #FC8068;
    border: 1px solid rgba(252, 128, 104, 0.08);
}

.organization-stu-profile-right-top-part div:nth-child(3) .instructor-dashboard-top-part-item .instructor-dashboard-top-part-icon {
    background-color: rgba(69, 200, 129, 0.12);
    color: var(--color-green);
    border: 1px solid rgba(69, 200, 129, 0.14);
}

.organization-stu-profile-right-top-part div:nth-child(4) .instructor-dashboard-top-part-item .instructor-dashboard-top-part-icon {
    background-color: rgba(127, 28, 160, 0.11);
    color: #7F1CA0;
    border: 1px solid rgba(127, 28, 160, 0.08);
}

/* Organization Student profile End */

/* Organization dashboard */

/*------------------------------------
    57. Organization Single Page End
--------------------------------------*/

/*------------------------------------
    58. Subscription Plan Page Start
--------------------------------------*/
.subscription-plan-area .section-sub-heading {
    width: 68%;
    margin-left: auto;
    margin-right: auto;
}

.pricing-tab-nav {
    border-radius: 59px;
    text-align: center;
    margin-bottom: 50px;
}

.pricing-tab-nav .nav-link {
    background-color: #E8E4F7;
    border-radius: 59px;
    border: 0;
    height: 35px;
    width: 39px;
}

.pricing-tab-nav .nav-link.active {
    background-color: var(--theme-color);
}

.pricing-tab-nav .nav-link.active::after {
    display: none;
}

.pricing-tab-nav .price-tab-slide {
    position: relative;
    z-index: 1;
    background-color: #E8E4F7;
    width: 74px;
    height: 35px;
    border-radius: 59px;
}

.price-tab-slide label.nav {
    display: flex !important;
    flex-wrap: nowrap;
    padding: 0;
}

.pricing-tab-nav .price-tab-slide:hover {
    cursor: pointer;
}

.pricing-tab-nav .price-tab-slide label:hover {
    cursor: pointer;
}

.pricing-tab-nav .price-tab-slide #price-tab-check,
.pricing-tab-nav .price-tab-slide #price-tab-check1,
.pricing-tab-nav .price-tab-slide #price-tab-check2,
.pricing-tab-nav .price-tab-slide #price-tab-check3,
.pricing-tab-nav .price-tab-slide #price-tab-check4,
.pricing-tab-nav .price-tab-slide #price-tab-check5 {
    display: none;
}

.pricing-tab-nav .price-tab-slide #price-tab-check:checked+i,
.pricing-tab-nav .price-tab-slide #price-tab-check1:checked+i,
.pricing-tab-nav .price-tab-slide #price-tab-check2:checked+i,
.pricing-tab-nav .price-tab-slide #price-tab-check3:checked+i,
.pricing-tab-nav .price-tab-slide #price-tab-check5:checked+i {
    left: 0;
    width: 39px;
}

.pricing-tab-nav .price-tab-slide i {
    position: absolute;
    top: 0;
    right: 0;
    width: 39px;
    height: 35px;
    background-color: var(--theme-color);
    box-shadow: 0px 1px 4px rgba(0, 20, 90, 0.1);
    border-radius: 59px;
    -webkit-transform: translate3d(0, 0);
    transform: translate3d(0, 0);
    -webkit-transition: 0.2s cubic-bezier(0.25, 1, 0.5, 1);
    -o-transition: 0.2s cubic-bezier(0.25, 1, 0.5, 1);
    transition: 0.2s cubic-bezier(0.25, 1, 0.5, 1);
    z-index: 1;
}

/* Subscription plan item table css */
.check-icon-wrap {
    height: 18px;
    width: 18px;
    background-color: var(--color-green);
    color: var(--white-color);
}

/* Pricing Plan Item auto Height */
.subscription-slider-items .owl-stage {
    display: flex;
}

.subscription-slider-items .owl-item {
    display: flex;
    flex: 1 0 auto;
}

.pricing-content-box {
    flex-grow: 1
}

/* Pricing Plan Item auto Height */

.pricing-item {
    padding: 40px;
    margin-top: 30px;
    background-color: var(--white-color);
    width: 100%;
    display: flex;
    flex-direction: column;
    margin-bottom: 18px;
}

.pricing-icon img {
    width: 80px !important;
    margin: 0 auto;
}

.pricing-time-duration {
    padding: 30px 0;
    height: 80px;
    flex-direction: column;
    justify-content: center;
}

.pricing-time-duration p {
    color: #9E9EAC;
}

.pricing-feature ul li {
    display: flex;
    align-items: center;
}

.pricing-feature ul li:not(:last-child) {
    margin-bottom: 25px;
}

/* Popular Pricing Plan */
.pricing-item.most-popular-plan {
    background-color: var(--white-color);
    border: 1px solid var(--theme-color);
    border-top: 1px solid transparent;
    padding-top: 70px;
    transform: scale(1.02);
}

.most-popular-content {
    background-color: var(--theme-color);
    border: 1px solid var(--theme-color);
    left: 0;
    top: 0;
    padding: 5px;
    border-top-right-radius: 5px;
    border-top-left-radius: 5px;
}

/* Popular Pricing Plan */

/* Subscription slider dots */
.subscription-slider-items .owl-dots .owl-dot span {
    width: 15px !important;
    height: 15px !important;
    background: transparent !important;
    border: 1px solid var(--border-color2) !important;
    transition: all .5s ease-in-out !important;
}

.subscription-slider-items .owl-dots .owl-dot.active span,
.subscription-slider-items .owl-dots .owl-dot:hover span {
    background: var(--theme-color) !important;
}

/* Subscription slider dots */

/* Subscription plan table css */

/* Saas Subscription Plan */
.saas-plan-instructor-organization-nav {
    background-color: #290055;
    width: 332px;
    margin-left: auto;
    margin-right: auto;
    padding: 3px;
}

.saas-plan-instructor-organization-nav .nav-link {
    width: 162px;
    color: var(--white-color);
}

.saas-plan-instructor-organization-nav .nav-link.active,
.saas-plan-instructor-organization-nav .show>.nav-link {
    color: #290055;
    background-color: var(--white-color);
}

/* Saas Subscription Plan */

/*------------------------------------
    58. Subscription Plan Page End
--------------------------------------*/

/*------------------------------------
    59. Certificate Verification Page Start
--------------------------------------*/
.certificate-verify-result-right {
    /* border-left: 1px solid var(--border-color); */
}

.certificate-verify-result-right img {
    /* height: 300px; */
}

.certificate-verify-result-left .table {
    border: transparent;
}

.certificate-verify-result-left table tr:hover {
    background-color: transparent;
}

.certificate-result-box {
    border-top: 1px solid var(--border-color);
}

.certificate-result-inner-box {
    width: 50%;
}

/*------------------------------------
    59. Certificate Verification Page End
--------------------------------------*/

/* Dev styles start */

.select2-container .select2-selection--single {
    height: 40px !important;
    padding-top: 5px !important;
}

.select2-container .select2-selection--multiple {
    height: 50px !important;
    padding-top: 5px !important;
}

.stu-consult-tbl tbody tr td:nth-child(2) .course-item.wishlist-item {
    width: 220px !important;
}

.stu-consult-tbl tbody tr td:nth-child(1) .wishlist-item .course-img-wrap {
    width: 140px !important;
}

.stu-consult-tbl tbody tr td:nth-child(1) .course-item.wishlist-item {
    width: 350px !important;
}

.active {
    background-color: #F6F6F6;
    /* a nice blue color */
    color: #ffffff;
    /* white text color */
    border-left: 5px solid #2196F3;
    /* add a left border */
    /* padding-left: 10px; */
    /* add some padding */
    border-radius: 3px;
}

.active:hover {
    background-color: #F6F6F6;
    /* a darker blue color on hover */
    color: #ffffff;
}
