
/* Create Certificate Page */
.create-certificate-sidebar .accordion {
    border: 1px solid #E5E8EC;
    padding: 14px;
    border-radius: 10px;
}
.create-certificate-sidebar .accordion-body {
    background: #FAFAFA;
    border-radius: 5px;
    padding: 14px;
    border: 1px solid #E5E8EC;
}
.create-certificate-sidebar .accordion-button:not(.collapsed) {
    background-color: var(--white-color);
    font-size: 16px;
    font-weight: 500;
}
.create-certificate-sidebar .course-sidebar-accordion-item {
    background-color: transparent;
    margin-bottom: 20px;
}
.create-certificate-sidebar .form-control {
    font-size: 14px;
}
.create-certificate-sidebar .accordion-button {
    padding: 0;
    width: auto;
    max-width: 100%;
    line-height: 22px;
    font-weight: 500;
    background-color: transparent;
    color: var(--heading-color);
}

.create-certificate-sidebar textarea.form-control {
    resize: none;
    overflow: hidden;
    height: auto;
}
    /* Color Picker Start */
    [type='color']{
        appearance: none;
        padding: 0;
        width: 15px;
        height: 15px;
        border: none;
    }
    [type='color']::-webkit-color-swatch-wrapper{
        padding: 0;
    }
    [type='color']::-webkit-color-swatch{
        border: none;
    }
    .color-picker, .color-picker1, .color-picker2, .color-picker3, .color-picker4, .color-picker5, .color-picker6, .color-picker7 {
        padding: 5px 6px;
        border-radius: 4px;
        border: 1px solid var(--border-color);
        background: var(--white-color);
        display: flex;
        height: 42px;
        font-size: 14px;
    }
    .only-frontend-create-certificate-page .color-picker,
    .only-frontend-create-certificate-page .color-picker1,
    .only-frontend-create-certificate-page .color-picker2,
    .only-frontend-create-certificate-page .color-picker3,
    .only-frontend-create-certificate-page .color-picker4,
    .only-frontend-create-certificate-page .color-picker5,
    .only-frontend-create-certificate-page .color-picker6,
    .only-frontend-create-certificate-page .color-picker7 {
        height: 49px;
    }
    .color-picker label {
        display: inline-flex;
        align-items: center;
    }
    .color-picker label > span {
        margin-left: 5px;
    }
    /* Color Picker Start
 */
/* Create Certificate Page */

/* Extra Accordion css for Admin Start */
    .create-certificate-sidebar .accordion-item {
        border: none;
    }
    .create-certificate-sidebar .accordion-button:not(.collapsed) {
        color: var(--heading-color);
    }
    .create-certificate-sidebar .accordion-button:not(.collapsed) {
        box-shadow: none;
    }
    .create-certificate-sidebar .accordion-button:not(.collapsed)::after {
        background-image: none;
        transform: rotate(-180deg);
    }
    .create-certificate-sidebar .accordion-button::after {
        flex-shrink: 0;
        width: 1.25rem;
        height: 1.25rem;
        margin-left: auto;
        content: "\e92e";
        transition: transform .3s ease-in-out;
        border-left: none;
        font-family: Feather!important;
        background-image: none;
        background-repeat: no-repeat;
        background-size: 0.625rem 0.625rem;
        float: right;
    }
    .create-certificate-sidebar .course-sidebar-accordion-item .accordion-collapse {
        box-shadow: none;
    }
    .create-certificate-sidebar .accordion-body {
        line-height: 25px;
        color: var(--gray-color);
    }
    .create-certificate-sidebar .accordion-button:focus {
        box-shadow: none;
    }
    .create-certificate-browse-file {
        position: relative;
    }
    .create-certificate-browse-file.form-control p {
        position: absolute;
        font-size: 14px;
        top: 5px;
    }
    .create-certificate-sidebar .upload-img-btn-wrapper {
        position: relative;
        overflow: hidden;
        display: inline-block;
        float: right;
        top: -3px;
        right: -9px;
    }
    .create-certificate-sidebar .upload-img-btn-wrapper button {
        padding: 9.5px 14px!important;
        font-size: 14px!important;
        color: var(--white-color)!important;
        font-weight: 400!important;
        height: 28px;
        background: var(--theme-color)!important;
    }
    .create-certificate-sidebar .upload-img-btn-wrapper button:hover {
        color: var(--theme-color)!important;
    }
    .create-certificate-sidebar .upload-img-btn-wrapper input[type=file] {
        font-size: 100px;
        position: absolute;
        left: 0;
        top: 0;
        opacity: 0;
    }
    .recomended-size-for-img {
        color: #B5B4BD;
    }
/* Extra Accordion css for Admin End */

/* Certificate Creating Custom Code Start */
.course-watch-certificate-img .sample-certificate-img-show {
    border: 1px solid #E5E8EC;
    border-radius: 10px;
    width: 100%;
}
.certificate-pdf-iframe {
    width: 100%;
    max-height: 100%;
    height: 600px;
}
.pdf-wrapper-box {
    width: 100%;
    height: 100%;
    margin-left: auto;
    margin-right: auto;
}

@media only screen and (max-width:991px) {
    .certificate-pdf-iframe {
        height: 400px;
    }
}
@media only screen and (max-width:767px) {
    .admin-certificate-sidebar {
        margin-top: 10px;
    }
}
@media only screen and (max-width:480px) {
    .admin-certificate-page .item-title {
        flex-direction: column;
    }
    .admin-certificate-page .item-title h2 {
        margin-bottom: 10px;
    }
}

/* Certificate Creating Custom Code End */

/* Create Certificate only for Admin Start */
.admin-certificate-radio .form-check {
    margin-right: 20px;
}
.admin-certificate-radio .form-check .form-check-label {
    margin-left: 5px;
}
.create-certificate-sidebar.admin-certificate-sidebar .upload-img-btn-wrapper {
    top: -8px;
    right: -12px;
}
/* Create Certificate only for Admin End */

/* Extra CSS For Admin Dashboard Design Start */

/*----Form Control Reset CSS----*/

.create-certificate-sidebar .form-control, .create-certificate-sidebar .form-select {
    background-color: var(--white-color);
    /* height: 49px; */
    color: var(--para-color);
    border: 1px solid var(--border-color);
    border-radius: 4px;
}
.create-certificate-sidebar .form-control:focus {
    background-color: var(--white-color);
    box-shadow: none;
}
.create-certificate-sidebar .form-select:focus {
    border-color: inherit;
    box-shadow: none;
}
.create-certificate-sidebar .form-control::placeholder {
    color: #a0a3aa;
}
.create-certificate-sidebar label {
    margin-bottom: 10px;
}
.create-certificate-sidebar .form-check-input:checked {
    background-color: var(--theme-color);
    border-color: var(--theme-color);
}
.create-certificate-sidebar textarea.form-control {
    height: auto;
}
.course-watch-certificate-img img.certificate-layout-img {
    border: 1px solid #E4E6EB;
    border-radius: 15.8px;
}
.font-mongolian {
    font-family: 'Mongolian Baiti';
    font-weight: normal;
    font-style: normal;
}

/* Only Root/Base CSS Setup Start */
/*--------------------------------
            2.1 Theme Color
-----------------------------------*/
:root {
    --white-color: #fff;
    --theme-color: #5e3fd7;
    --light-purple: rgba(117, 79, 254, 0.1);
    --heading-color: #040453;
    --orange-color: #FC8068;
    --orange-deep: #FF3C16;
    --para-color: #52526C;
    --gray-color: #767588;
    --gray-color2: #929292;
    --disable-color: #B5B4BD;
    --color-green: #45C881;
    --color-light-green: rgba(69, 200, 129, 0.22);
    --color-yellow: #FFC014;
    --light-bg: #F9F8F6;
    --page-bg: #F8F6F0;
    --border-color: rgba(0, 0, 0, 0.07);
    --border-color2: rgba(0, 0, 0, 0.09);
    --font-jost: 'Jost', sans-serif;
}

.color-heading {
    color: var(--heading-color);
}
.bg-page {
    background-color: var(--page-bg);
}
.color-hover {
    color: var(--theme-color);
}
.bg-hover {
    background-color: var(--theme-color);
}
.color-para {
    color: var(--para-color);
}
/* default text color */
.para-color {
    color: var(--para-color);
}
/* nutral-1 */
.color-gray {
    color: var(--gray-color);
}
/* nutral-2 */
.color-gray2 {
    color: var(--gray-color2);
}
.color-light-green {
    color: var(--color-light-green);
}
.theme-border {
    border:1px solid var(--border-color);
}
.theme-font-color{
    color: var(--para-color);
}
.font-12 {
    font-size: 12px;
    line-height: 13px;
}
.font-14 {
    font-size: 14px;
}
.font-medium {
    font-weight: 500;
}
.mt-30 {
    margin-top: 30px;
}
.theme-button1, .theme-button2, .btn-violet-transparent {
    color: var(--white-color) !important;
    background-color: var(--theme-color) !important;
    border: 2px solid var(--theme-color) !important;
    display: inline-flex;
    align-items: center;
    cursor: pointer;
    outline: none;
    z-index: 99;
    padding: 9.5px 24px!important;
    font-size: 15px!important;
    justify-content: center;
    border-radius: 4px;
    font-weight: 600!important;
}
.theme-btn {
    display: inline-flex;
    align-items: center;
    cursor: pointer;
    outline: none;
    z-index: 99;
    padding: 9.5px 24px!important;
    font-size: 15px!important;
    justify-content: center;
    border-radius: 4px;
    font-weight: 600!important;
}
.theme-button1:hover {
    color: var(--theme-color) !important;
    background-color: var(--white-color);
    border: 2px solid var(--white-color);
}
.default-hover-btn {
    border-width: 1px;
}
.default-hover-btn:hover {
    border-color: var(--theme-color);
    border-width: 1px;
    color: var(--theme-color);
}
.green-theme-btn {
    background-color: var(--color-green);
    border-color: var(--color-green);
}
.green-theme-btn .iconify, .theme-btn.default-hover-btn .iconify {
    margin-right: 8px;
    margin-left: 0;
}
.green-theme-btn:hover {
    border-color: var(--color-green)!important;
    color: var(--color-green)!important;
}
/* Only Root/Base CSS Setup End */

/* Extra CSS For Admin Dashboard Design End */
