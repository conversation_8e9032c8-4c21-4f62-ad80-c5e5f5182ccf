.instructor-profile-page {
    background-color: #f9fafb;
    font-family: 'circular';
}

.header-background {
    position: relative;
    height: 530px;
    background-color: #000;
    overflow: hidden;
}

.header-background::after {
    position: absolute;
    content: '';
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background: linear-gradient(to top,
            rgba(0, 0, 0, 0.8) 0%,
            rgba(0, 0, 0, 0.5) 25%,
            rgba(0, 0, 0, 0) 0%);
    z-index: 1;
}

.header-background img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    opacity: 0.5;
}

.profile-section {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 2;
    top: -130px;
}

.profile-header {
    position: relative;
    display: flex;
    align-items: flex-end;
    margin-bottom: 30px;

}

.profile-image {
    position: relative;
    width: 160px;
    height: 160px;
    border-radius: 50%;
    border: 4px solid #fff;
    margin-right: 24px;
    background-color: #fff;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.profile-image img {
    width: 152px;
    height: 152px;
    object-fit: cover;
    border-radius: 50%;
}

.online-badge {
    position: absolute;
    bottom: 10px;
    right: -4px;
    background-color: #10b981;
    color: white;
    padding-top: 0.125rem;
    padding-bottom: 0.125rem;
    padding-left: 0.625rem;
    padding-right: 0.625rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    font-family: 'circular';
}

.profile-info {
    flex: 1;
}

.profile-info h1 {
    color: #fff;
    font-size: 1.875rem;
    line-height: 2.25rem;
    font-weight: 700;
    font-family: 'circular';
    text-align: left;
    margin-bottom: 0.5rem;
}

.profile-info h2 {
    color: #fff;
    font-size: 1.25rem;
    line-height: 1.75rem;
    font-family: 'circular';
    margin-bottom: 1rem;
}

.action-buttons {
    display: flex;
    gap: 12px;
}

.action-buttons .btn,
.action-buttons .btn:focus {
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 0.875rem;
    line-height: 1.25rem;
    font-weight: 500;
    font-family: 'circular';
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    border: 1px solid #f4f4f5;
    background-color: #f4f4f5;
    color: #333;
    transition: all 0.2s ease;
}

.action-buttons .btn:hover {
    border: 1px solid rgba(244, 244, 245, 0.9);
    background-color: rgba(244, 244, 245, 0.9);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    display: flex;
    flex-direction: column;
    justify-content: center;
    background: white;
    padding: 4px 12px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    text-align: left;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.stat-number {
    font-size: 1.5rem;
    line-height: 2rem;
    font-weight: 700;
    font-family: 'circular';
    color: #111;
    margin-bottom: 0;
}

.stat-label {
    color: #71717a;
    font-size: 0.875rem;
    line-height: 1.25rem;
    font-family: 'circular';
}

.rating-stars {
    font-size: 22px;
    line-height: 1;
    color: #efb330;
    margin-bottom: 0;
}

.tabs {
    display: flex;
    border-bottom: 1px solid #e5e7eb;
    margin-bottom: 24px;
}

.tab {
    padding: 12px 24px;
    font-weight: 500;
    color: #666;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.2s ease;
}

.tab.active {
    color: #111;
    border-bottom-color: #111;
}

.tab:hover {
    color: #111;
}

.content-section {
    background: white;
    padding: 24px;
    border-radius: 12px;
    margin-bottom: 24px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.bio-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 16px;
    color: #111;
}

.bio-text {
    color: #666;
    line-height: 1.6;
    margin-bottom: 24px;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #666;
}

/* =======================
        RESPONSIVE
======================= */

@media screen and (max-width: 767px) {
    .profile-header {
        gap: 30px;
    }

    .action-buttons {
        justify-content: flex-start;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .profile-image {
        margin-right: 4px;
    }
}

@media screen and (max-width: 575px) {

    .profile-info {
        text-align: center;
    }

    .profile-header {
        gap: 0px;
    }

    .profile-info h2 {
        text-align: left;
    }

    .action-buttons {
        justify-content: center;
    }

    .profile-section {
        top: -113px;
    }

    .profile-info .action-buttons {
        flex-direction: row !important;
    }

    .profile-image img {
        width: 130px;
        height: 130px;
    }

    .profile-image {
        width: 138px;
        height: 138px;
    }

    .profile-info .action-buttons {
        justify-content: flex-start !important;
    }

    .header-background {
        height: 400px;
    }

    .action-buttons .btn {
        padding: 5px 8px;
    }

    .profile-info .action-buttons .btn .btn-text {
        display: none;
    }

    .profile-info .action-buttons .btn svg {
        width: 20px;
        height: 20px;
        color: deepskyblue;
    }

    .stat-card {
        align-items: center;
    }
}

@media screen and (max-width: 480px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .tabs {
        overflow-x: auto;
        white-space: nowrap;
    }

    .tab {
        padding: 12px 16px;
    }

    .profile-section {
        padding: 0 8px;
    }

    .profile-info h1 {
        font-size: 26px;
        line-height: 28px;
    }

    .profile-info h2 {
        margin-bottom: 0px !important;
    }

    .profile-info .action-buttons {
        margin-top: 8px !important;
    }

    .profile-header {
        gap: 20px !important;
    }

    .profile-section {
        top: -85px;
    }

    .header-background {
        height: 300px !important;
    }

    .profile-image img {
        width: 100px;
        height: 100px;
    }

    .profile-image {
        width: 108px;
        height: 108px;
        margin-right: 4px;
    }

    .profile-info .action-buttons .btn {
        padding: 5px 6px;
    }

    .stats-grid {
        margin-bottom: 8px;
    }

    .instructor-profile-tab button.nav-link {
        margin-bottom: 8px;
    }

    .instructor-profile-page .exam-center-header {
        padding: 0px;
    }

    .instructor-profile-page .exam-center-parent .container {
        padding-left: 0;
        padding-right: 0;
    }
}

@media screen and (max-width: 425px) {
    .header-background {
        height: 410px;
    }

    .profile-image {
        width: 103px;
        height: 103px;
        margin-right: 0;
    }

    .profile-image img {
        width: 95px;
        height: 95px;
    }

    .profile-info .action-buttons {
        gap: 6px;
    }

    .profile-info .action-buttons .btn svg {
        width: 15px;
        height: 15px;
    }

    .online-badge {
        right: -16px;
        bottom: 0px;
    }

    .profile-header {
        gap: 10px;
        margin-bottom: 16px;
    }

    .action-buttons {
        gap: 8px;
    }

    .action-buttons .btn,
    .action-buttons .btn:focus {
        padding: 7px 11px;
        border-radius: 5px;
        font-size: 13px;
        line-height: 1;
        gap: 2px;
    }

    .profile-info h1 {
        margin-bottom: 4px;
    }

    .profile-info h2 {
        margin-bottom: 10px;
    }

    .stats-grid {
        gap: 8px;
    }

    .stat-card {
        padding: 16px;
    }

    .profile-info .action-buttons .btn,
    .profile-info .action-buttons .btn:focus {
        padding: 5px 8px !important;
        font-size: 12px !important;
    }

    .stat-card {
        padding: 8px;
    }

    .stat-number {
        font-size: 21px;
    }

    .stat-label {
        font-size: 14px;
    }
}

@media screen and (max-width: 375px) {
    .online-badge {
        bottom: 0px;
    }

    .action-buttons {
        gap: 8px;
    }

    .action-buttons .btn,
    .action-buttons .btn:focus {
        padding: 7px 11px;
        border-radius: 5px;
        font-size: 13px;
        line-height: 1;
        gap: 2px;
    }

    .profile-info h1 {
        margin-bottom: 4px;
    }

    .profile-info h2 {
        margin-bottom: 10px;
    }

    .stats-grid {
        gap: 8px;
    }

    .profile-header {
        margin-bottom: 16px;
    }

    .stat-card {
        padding: 16px;
    }

    .profile-info h1 {
        font-size: 22px;
        line-height: 26px;
        margin-bottom: 0px;
    }

    .profile-info h2 {
        font-size: 16px;
        line-height: 22px;
    }

    .profile-info .action-buttons {
        margin-top: 6px !important;
    }

    .profile-info .action-buttons .btn,
    .profile-info .action-buttons .btn:focus {
        padding: 3px 5px !important;
        font-size: 10px !important;
        border-radius: 4px;
    }

    .profile-info .action-buttons {
        gap: 4px;
    }

    .profile-info .action-buttons .btn svg {
        width: 12px;
        height: 12px;
    }

    .profile-image img {
        width: 80px;
        height: 80px;
    }

    .profile-image {
        width: 88px;
        height: 88px;
    }

    .profile-header {
        gap: 24px !important;
    }

    .profile-section {
        top: -72px;
    }

    .header-background {
        height: 230px !important;
    }

    .profile-info .action-buttons .btn svg {
        width: 15px;
        height: 15px;
    }
}

@media screen and (max-width: 320px) {
    .profile-info .action-buttons .btn svg {
        width: 16px;
        height: 16px;
    }
}
