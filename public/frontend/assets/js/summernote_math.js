(function(factory){
    if(typeof define==='function'&&define.amd){
        define(['jquery'],factory);
    }else if(typeof module==='object'&&module.exports){
        module.exports=factory(require('jquery'));
    }else{
        factory(window.jQuery);
    }
}(function($){
    $.extend(true,$.summernote.lang,{
        'en-US':{ /* English */
            math:{
                dialogTitle:'Insert Math',
                tooltip:'Insert Math',
                pluginTitle:'Insert math',
                ok:'Insert',
                cancel:'Cancel'
            }
        }
    });
    $.extend($.summernote.options,{
        math:{
            icon:'<b>&sum;</b>'
        }
    });
    $.extend($.summernote.plugins,{
        'math':function(context){
            var self=this;
            var ui=$.summernote.ui;
            //var $note=context.layoutInfo.note;
            var $editor=context.layoutInfo.editor;
            //var $editable=context.layoutInfo.editable;
            var options=context.options;
            var lang=options.langInfo;

            self.events = {
              'summernote.keyup summernote.mouseup summernote.change summernote.scroll': () => {
                self.update();
              },
              'summernote.disable summernote.dialog.shown': () => {
                self.hide();
              },
            };


            context.memo('button.math',function(){
                let button=ui.button({
                    contents:options.math.icon,
                    tooltip:lang.math.tooltip,
                    click:function(e){
                        // Cursor position must be saved because is lost when popup is opened.
                        context.invoke('editor.saveRange');
                        context.invoke('math.show');
                    }
                });
                return button.render();
            });

            self.initialize = function(){
                let $container=options.dialogsInBody?$(document.body):$editor;
                var base_url = window.location.origin;
                 
                let body=`<div class="form-group">

                    <div class="latexcode-wrapper">
                      
                        <h3>Latex code lists are here:</h3>
                        
                        <div class="latexBlock">
                            <style> 
                                .latexBlock {
                                    position: relative;
                                }
                                
                                .latexBlock ul li {
                                    display: inline-block;
                                    border: 1px solid #000;
                                    padding: 10px;
                                    margin: 3px 2px;
                                    border-radius: 3px;
                                    vertical-align: middle;
                                    cursor: pointer;
                                }
                                
                                .latexBlock ul {
                                    position: relative;
                                    display: block;
                                }
                                
                                li.latex-math-syntax-list code {
                                    display: none;
                                }
                            </style>
                            <h4>Enviroments:</h4>
                            <ul>
                            <li class="latex-math-syntax-list">
                                <img src="http://latex.codecogs.com/gif.latex?\\alpha">
                                <code>\\alpha</code>
                            </li>
                            <li class="latex-math-syntax-list">
                                <img src="http://latex.codecogs.com/gif.latex?\\beta">
                                <code>\\beta</code>
                            </li>  
                            <li class="latex-math-syntax-list">
                                <img src="http://latex.codecogs.com/gif.latex?\\gamma">
                                <code>\\gamma</code>
                            </li>  
                            <li class="latex-math-syntax-list">
                                <img src="http://latex.codecogs.com/gif.latex?\\delta">
                                <code>\\delta</code>
                            </li>  
                            <li class="latex-math-syntax-list">
                                <img src="http://latex.codecogs.com/gif.latex?\\omega">
                                <code>\\omega</code>
                            </li>  
                            <li class="latex-math-syntax-list">
                                <img src="http://latex.codecogs.com/gif.latex?\\varphi">
                                <code>\\varphi</code>
                            </li>  

                            <li class="latex-math-syntax-list">
                                <img src="http://latex.codecogs.com/gif.latex?\\ne">
                                <code>\\ne</code>
                            </li>  
                            <li class="latex-math-syntax-list">
                                <img src="http://latex.codecogs.com/gif.latex?\\mu">
                                <code>\\mu</code>
                            </li>  

                            <li class="latex-math-syntax-list">
                                <img src="http://latex.codecogs.com/gif.latex?\\varepsilon">
                                <code>\\varepsilon</code>
                            </li>  
                            <li class="latex-math-syntax-list">
                                <img src="http://latex.codecogs.com/gif.latex?\\tau">
                                <code>\\tau</code>
                            </li>  
                            <li class="latex-math-syntax-list">
                                <img src="http://latex.codecogs.com/gif.latex?\\cup">
                                <code>\\cup</code>
                            </li> 
                            <li class="latex-math-syntax-list">
                                <img src="http://latex.codecogs.com/gif.latex?\\cap">
                                <code>\\cap</code>
                            </li> 
                            <li class="latex-math-syntax-list">
                                <img src="http://latex.codecogs.com/gif.latex?\\therefore">
                                <code>\\therefore</code>
                            </li> 
                            <li class="latex-math-syntax-list">
                                <img src="http://latex.codecogs.com/gif.latex?\\propto">
                                <code>\\propto</code>
                            </li>
                            <li class="latex-math-syntax-list">
                                <img src="http://latex.codecogs.com/gif.latex?\\oplus">
                                <code>\\oplus</code>
                            </li> 
                            <li class="latex-math-syntax-list">
                                <img src="http://latex.codecogs.com/gif.latex?\\cong">
                                <code>\\cong</code>
                            </li>
                            <li class="latex-math-syntax-list">
                                <img src="http://latex.codecogs.com/gif.latex?\\approx">
                                <code>\\approx</code>
                            </li>
                            <li class="latex-math-syntax-list">
                                <img src="http://latex.codecogs.com/gif.latex?\\equiv">
                                <code>\\equiv</code>
                            </li>
                            <li class="latex-math-syntax-list">
                                <img src="http://latex.codecogs.com/gif.latex?\\infty">
                                <code>\\infty</code>
                            </li>
                            <li class="latex-math-syntax-list">
                                <img src="http://latex.codecogs.com/gif.latex?\\pm">
                                <code>\\pm</code>
                            </li>
                            <li class="latex-math-syntax-list">
                                <img src="http://latex.codecogs.com/gif.latex?\\rho">
                                <code>\\rho</code>
                            </li>
                            <li class="latex-math-syntax-list">
                                <img src="http://latex.codecogs.com/gif.latex?\\sigma">
                                <code>\\sigma</code>
                            </li>
                            <li class="latex-math-syntax-list">
                                <img src="http://latex.codecogs.com/gif.latex?\\vartheta">
                                <code>\\vartheta</code>
                            </li>
                            <li class="latex-math-syntax-list">
                                <img src="http://latex.codecogs.com/gif.latex?\\to">
                                <code>\\to</code>
                            </li>
                            <li class="latex-math-syntax-list">
                                <img src="http://latex.codecogs.com/gif.latex?\\gets">
                                <code>\\gets</code>
                            </li>
                            <li class="latex-math-syntax-list">
                                <img src="http://latex.codecogs.com/gif.latex?\\uparrow">
                                <code>\\uparrow</code>
                            </li>
                            <li class="latex-math-syntax-list">
                                <img src="http://latex.codecogs.com/gif.latex?\\downarrow">
                                <code>\\downarrow</code>
                            </li>
                            <li class="latex-math-syntax-list">
                                <img src="http://latex.codecogs.com/gif.latex?\\sqrt[4]{}">
                                <code>\\sqrt[4]{}</code>
                            </li>
                            <li class="latex-math-syntax-list">
                                <img src="http://latex.codecogs.com/gif.latex?\\sqrt[3]{}">
                                <code>\\sqrt[3]{}</code>
                            </li>
                            <li class="latex-math-syntax-list">
                                <img src="http://latex.codecogs.com/gif.latex?\\phi">
                                <code>\\phi</code>
                            </li>
                            <li class="latex-math-syntax-list">
                                <img src="http://latex.codecogs.com/gif.latex?\\lambda">
                                <code>\\lambda</code>
                            </li>
                            <li class="latex-math-syntax-list">
                                <img src="http://latex.codecogs.com/gif.latex?\\psi">
                                <code>\\psi</code>
                            </li> 
                            <li class="latex-math-syntax-list">
                                <img src="http://latex.codecogs.com/gif.latex?\\eta">
                                <code>\\eta</code>
                            </li>
                            <li class="latex-math-syntax-list">
                                <img src="http://latex.codecogs.com/gif.latex?\\le">
                                <code>\\le</code>
                            </li>
                            <li class="latex-math-syntax-list">
                                <img src="http://latex.codecogs.com/gif.latex?\\ge">
                                <code>\\ge</code>
                            </li>

                            <li class="latex-math-syntax-list">
                                <img src="http://latex.codecogs.com/gif.latex?\\begin{matrix} 1 & 0 & 0 \\\\ 0 & 1 & 0 \\\\ 0 & 0 & 1 \\end{matrix}">
                                <code>\\begin{matrix} 1 & 0 & 0 \\\\ 0 & 1 & 0 \\\\ 0 & 0 & 1 \\end{matrix}</code>
                            </li> 
                            <li class="latex-math-syntax-list">
                                <img src="http://latex.codecogs.com/gif.latex?\\Bigg[">
                                <code>\\Bigg[</code>
                            </li> 
                            <li class="latex-math-syntax-list">
                                <img src="http://latex.codecogs.com/gif.latex?\\Bigg]">
                                <code>\\Bigg]</code>
                            </li>
                            <li class="latex-math-syntax-list">
                                <img src="http://latex.codecogs.com/gif.latex?\\int">
                                <code>\\int</code>
                            </li>
                            <li class="latex-math-syntax-list">
                                <img src="http://latex.codecogs.com/gif.latex?\\sum">
                                <code>\\sum</code>
                            </li>
                            
                            

                            


                            <li class="latex-math-syntax-list">
                                <img src="http://latex.codecogs.com/gif.latex?\\begin{pmatrix} a & b &c \\\\ a & b & c \\\\ a & b & c \\end{pmatrix}">
                                <code>\\begin{pmatrix} a & b &c \\\\ a & b & c \\\\ a & b & c \\end{pmatrix}</code>
                            </li> 
                            <li class="latex-math-syntax-list">
                                <img src="http://latex.codecogs.com/gif.latex?\\begin{bmatrix} a & b &c \\\\ a & b & c \\\\ a & b & c \\end{bmatrix}">
                                <code>\\begin{bmatrix} a & b &c \\\\ a & b & c \\\\ a & b & c \\end{bmatrix}</code>
                            </li> 
                            <li class="latex-math-syntax-list">
                                <img src="http://latex.codecogs.com/gif.latex?\\begin{vmatrix} a & b &c \\\\ a & b & c \\\\ a & b & c \\end{vmatrix}">
                                <code>\\begin{vmatrix} a & b &c \\\\ a & b & c \\\\ a & b & c \\end{vmatrix}</code>
                            </li> 




















                                <li class="latex-math-syntax-list">
                                    <img src="http://latex.codecogs.com/gif.latex?\\begin{matrix} a &amp; b \\\\ c &amp; d \\end{matrix}">
                                    <code>\\begin{matrix} a & b \\\\ c & d \\end{matrix}</code>
                                </li> 


                                <li class="latex-math-syntax-list">
                                    <img src="http://latex.codecogs.com/gif.latex?\\begin{pmatrix} a & b \\\\ c & d \\end{pmatrix}">
                                    <code>\\begin{pmatrix} a & b \\\\ c & d \\end{pmatrix}</code>
                                </li> 


                                <li class="latex-math-syntax-list">
                                    <img src="http://latex.codecogs.com/gif.latex?\\begin{vmatrix} a & b \\\\ c & d \\end{vmatrix}">
                                    <code>\\begin{vmatrix} a & b \\\\ c & d \\end{vmatrix}</code>
                                </li> 


                                <li class="latex-math-syntax-list">
                                    <img src="http://latex.codecogs.com/gif.latex?\\begin{Bmatrix} a & b \\\\ c & d \\end{Bmatrix}">
                                    <code>\\begin{Bmatrix} a & b \\\\ c & d \\end{Bmatrix}</code>
                                </li> 


                                <li class="latex-math-syntax-list">
                                    <img src="http://latex.codecogs.com/gif.latex?x = \\begin{cases} a &\\text{if } b \\\\ c &\\text{if } d \\end{cases}">
                                    <code>x = \\begin{cases} a &\\text{if } b \\\\ c &\\text{if } d \\end{cases}</code>
                                </li> 


                                

                            </ul>
                        </div>
                         
                    </div>

                    <p>Type <a href="https://khan.github.io/KaTeX/function-support.html" target=_blank">LaTeX markup</a> here: </p>
                    <p><input id="note-latex" class="form-control"></p>
                    <p>Preview: </p>
                    <div style="min-height:20px;"><span class="note-math-dialog"></span></div>
    
                    <script>
                    let $mathElement = $('.note-math-dialog');
                    let mathSpan = $mathElement[0];
                    let latexSpan = document.getElementById('note-latex');


                    $('li.latex-math-syntax-list').on('click', function(){
                        var thisLatexCodeOutput = $(this).find('code').text();
                        $('input#note-latex').val(thisLatexCodeOutput); 
                        katex.render(thisLatexCodeOutput, mathSpan);  

                        // $(this).closest('.note-modal').find('.note-math-btn').trigger('click');
                    });

                    
                    latexSpan.addEventListener('keyup', renderMath);

                    function renderMath(){ 
                        let oldMath = mathSpan.innerHTML;
                        
                        try {
                            katex.render(this.value, mathSpan);
                        }
                        catch(e) { 
                            // KaTeX parse error while typing, to prevent rendered math from dissappearing while typing
                            // partially complete markup
                            mathSpan.innerHTML = oldMath;
                        }
                    }


                    
                    </script>

                    </div>`;
                self.$dialog=ui.dialog({
                    title:lang.math.dialogTitle,
                    body:body,
                    footer:'<button class="btn btn-primary note-math-btn">'+lang.math.ok+'</button>'
                }).render().appendTo($container);

                self.$popover = ui.popover({
                  className: 'note-math-popover'
                }).render().appendTo(options.container);
                const $content = self.$popover.find('.popover-content,.note-popover-content');
                context.invoke('buttons.build', $content, ['math']);
            };

            self.hasMath = function(node) {
              return node && $(node).hasClass('note-math');
            };

            self.isOnMath = function(range) {
              const ancestor = $.summernote.dom.ancestor(range.sc, self.hasMath);
              return !!ancestor && (ancestor === $.summernote.dom.ancestor(range.ec, self.hasMath));
            };

            self.update = function() {
              // Prevent focusing on editable when invoke('code') is executed
              if (!context.invoke('editor.hasFocus')) {
                self.hide();
                return;
              }

              const rng = context.invoke('editor.getLastRange');
              if (rng.isCollapsed() && self.isOnMath(rng)) {
                const node = $.summernote.dom.ancestor(rng.sc, self.hasMath);
                const latex = $(node).find('.note-latex');

                if (latex.text().length !== 0) {
                  self.$popover.find('button').html(latex.text());
                  const pos = $.summernote.dom.posFromPlaceholder(node);
                  self.$popover.css({
                    display: 'block',
                    left: pos.left,
                    top: pos.top,
                  });
                } else {
                  self.hide();
                }
              } else {
                self.hide();
              }
            }

            self.hide = function() {
              self.$popover.hide();
            }

            self.destroy = function(){
                ui.hideDialog(this.$dialog);
                self.$dialog.remove();
                self.$popover.remove();
            };

            self.bindEnterKey = function($input,$btn){
                $input.on('keypress',function(event){
                    if(event.keyCode===13)$btn.trigger('click');
                });
            };

            self.bindLabels = function(){
                self.$dialog.find('.form-control:first').focus().select();
                self.$dialog.find('label').on('click',function(){
                    $(this).parent().find('.form-control:first').focus();
                });
            };

            self.show = function(){

                let $mathSpan = self.$dialog.find('.note-math-dialog');
                let $latexSpan = self.$dialog.find('#note-latex');

                let $selectedMathNode = self.getSelectedMath();

                if ($selectedMathNode === null) {
                    // reset the dialog input and math
                    $mathSpan.empty();
                    $latexSpan.val('');
                }
                else { // edit the selected math node
                    // get the hidden LaTeX markup from the selected math node
                    let hiddenLatex = $selectedMathNode.find('.note-latex').text();
                    $latexSpan.val(hiddenLatex);
                    katex.render(hiddenLatex, $mathSpan[0]);
                }

                let mathInfo = {}; // not used

                self.showMathDialog(mathInfo).then(function(mathInfo){
                    ui.hideDialog(self.$dialog);
                    let $mathNodeClone = $mathSpan.clone();
                    let $latexNode = $('<span>');
                    $latexNode.addClass('note-latex')
                        .css('display', 'none')
                        .text($latexSpan.val())
                        .appendTo($mathNodeClone);

                    // So we don't pick up the dialog node when selecting math nodes in the editor
                    $mathNodeClone.removeClass('note-math-dialog').addClass('note-math');

                    // We restore cursor position and element is inserted in correct pos.
                    context.invoke('editor.restoreRange');
                    context.invoke('editor.focus');

                    if ($selectedMathNode === null)
                        context.invoke('editor.insertNode',$mathNodeClone[0]);
                    else {// if we are editing an existing mathNode, just replace the contents:
                        if( $.trim($latexNode.html())=='') { // unless there's nothing there, then delete the node
                            $selectedMathNode.remove()
                        }
                        else {
                            $selectedMathNode.html($mathNodeClone.html());
                        }

                    }
                });
            };

            self.showMathDialog = function(editorInfo) {
                return $.Deferred(function (deferred) {
                    let $editBtn = self.$dialog.find('.note-math-btn');
                    ui.onDialogShown(self.$dialog, function () {

                        context.triggerEvent('dialog.shown');
                        $editBtn.click(function (e) {
                            e.preventDefault();
                            deferred.resolve({

                            });
                        });
                        self.bindEnterKey($editBtn);
                        self.bindLabels();
                    });
                    ui.onDialogHidden(self.$dialog, function () {
                        $editBtn.off('click');
                        if (deferred.state() === 'pending') deferred.reject();
                    });
                    ui.showDialog(self.$dialog);
                });
            };

            self.getSelectedMath = function() {
                let selection = window.getSelection();
                if( selection ){
                    // get all math nodes
                    let $selectedMathNode = null;
                    let $mathNodes = $('.note-math');
                    $mathNodes.each(function() {
                        // grab first math node in the selection (including partial).
                        if(selection.containsNode(this, true)) {
                            $selectedMathNode = $(this);
                        }
                    });
                    return $selectedMathNode;
                }

            };


        }
    });
}));









