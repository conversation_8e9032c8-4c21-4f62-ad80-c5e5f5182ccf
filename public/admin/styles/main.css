/*------------------------------------------------------------------------------------
Theme Name: Multizai
Author: zainiklab
URI: #
version: 1.0.0
------------------------------------------------------------------------------------*/
/*------------------------------------------------------------------------------------
CSS table of content

    01. Settings style
    02. Base style
    03. Sections style
    04. Components style
    05. Utilities style

------------------------------------------------------------------------------------*/
/*--------------------------------
01. Settings style
--------------------------------*/
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap");
/*--------------------------------
02. Base style
--------------------------------*/
* {
  margin: 0;
  padding: 0;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
}

body {
  color: #6c5ce7;
  font-family: "Inter", sans-serif;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  background: #FAFBFC;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  color: #272E35;
  font-family: "Inter", sans-serif;
  font-weight: 600;
  letter-spacing: 0;
  margin: 0;
}

h1 {
  font-size: 32px;
  line-height: 40px;
}

h2 {
  font-weight: 600;
  font-size: 18px;
  line-height: 22px;
  color: #596680;
}

h3 {
  font-weight: 600;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

p {
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

::-moz-selection {
  background-color: #2980b9;
  color: #ffffff;
}

::selection {
  background-color: #2980b9;
  color: #ffffff;
}

::-moz-selection {
  background-color: #2980b9;
  color: #ffffff;
}

a,
.btn,
button {
  text-decoration: none;
  color: inherit;
  cursor: pointer;
}

a:hover,
.btn:hover,
button:hover {
  text-decoration: none;
  color: inherit;
}

a:focus,
.btn:focus,
button:focus {
  text-decoration: none;
  color: inherit;
  outline: none;
  border: none;
}

a,
button,
img,
input,
span,
::before,
::after {
  -webkit-transition: all 0.3s ease 0s;
  transition: all 0.3s ease 0s;
}

*:focus {
  outline: none;
}

button,
input[type="submit"] {
  cursor: pointer;
}

button {
  border-radius: 0;
  border: 0;
  background-color: transparent;
}

.btn {
  margin: 0;
  padding: 0;
  border: 0;
  border-radius: 0;
}

ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

img {
  max-width: 100%;
}

.main-content {
  margin-left: 250px;
  -webkit-transition: 0.4s;
  transition: 0.4s;
}

.main-content.active {
  margin-left: 60px;
}

@media screen and (max-width: 1199px) {
  .main-content {
    margin-left: 0;
  }
  .main-content.active {
    margin-left: 0;
  }
}

.page-content-wrap {
  min-height: calc(100vh - 240px);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.page-content {
  margin-top: -150px;
}

@media screen and (max-width: 545px) {
  .page-content {
    margin-top: -120px;
  }
}

.bg-style {
  background: #ffffff;
  border: 1px solid #E4E6EB;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  -webkit-box-shadow: 0px 10px 25px rgba(163, 177, 204, 0.1);
          box-shadow: 0px 10px 25px rgba(163, 177, 204, 0.1);
  border-radius: 12px;
}

.bg-red {
  background-color: #FEE6E6;
}

.bg-green {
  background-color: #DCFADC;
}

.bg-yellow {
  background-color: #FAEBE1;
}

.bg-purple {
  background-color: #F5E6FE;
}

.bg-red {
  background-color: #F96363;
}

.bg-green {
  background-color: #4CBF4C;
}

.bg-blue {
  background-color: #5991FF;
}

.bg-yellow {
  background-color: #FF964B;
}

.bg-purple {
  background-color: #BE63F9;
}

.bg-fair-pink {
  background-color: #FFEDED;
}

.bg-linen {
  background-color: #FAF1E8;
}

.bg-loafer {
  background-color: #ECF5E4;
}

.bg-aqua-spring {
  background-color: #E6F7F7;
}

.bg-titan-white {
  background-color: #EDEDFF;
}

.bg-primary-soft-varient {
  background-color: #DAE9FF;
  color: #5991FF;
}

.bg-primary-light-varient {
  background-color: #80ABFF;
  color: #FFFFFF;
}

.bg-primary-default-varient {
  background-color: #5991FF;
  color: #FFFFFF;
}

.bg-primary-dark-varient {
  background-color: #3377FF;
  color: #FFFFFF;
}

.bg-secondary-soft-varient {
  background-color: #F0DAFF;
  color: #BE63F9;
}

.bg-secondary-light-varient {
  background-color: #D28CFF;
  color: #FFFFFF;
}

.bg-secondary-default-varient {
  background-color: #BE63F9;
  color: #FFFFFF;
}

.bg-secondary-dark-varient {
  background-color: #B440FF;
  color: #FFFFFF;
}

.bg-success-soft-varient {
  background-color: #C8F7C8;
  color: #4CBF4C;
}

.bg-success-light-varient {
  background-color: #70CC70;
  color: #FFFFFF;
}

.bg-success-default-varient {
  background-color: #4CBF4C;
  color: #FFFFFF;
}

.bg-success-dark-varient {
  background-color: #32A632;
  color: #FFFFFF;
}

.bg-info-soft-varient {
  background-color: #DAF3FF;
  color: #33BBFF;
}

.bg-info-light-varient {
  background-color: #66CCFF;
  color: #FFFFFF;
}

.bg-info-default-varient {
  background-color: #33BBFF;
  color: #FFFFFF;
}

.bg-info-dark-varient {
  background-color: #00AAFF;
  color: #FFFFFF;
}

.bg-warning-soft-varient {
  background-color: #FAE3CA;
  color: #FF9F38;
}

.bg-warning-light-varient {
  background-color: #FFB25E;
  color: #FFFFFF;
}

.bg-warning-default-varient {
  background-color: #FF9F38;
  color: #FFFFFF;
}

.bg-warning-dark-varient {
  background-color: #FF8D12;
  color: #FFFFFF;
}

.bg-danger-soft-varient {
  background-color: #FFDED1;
  color: #FF6628;
}

.bg-danger-light-varient {
  background-color: #FF824F;
  color: #FFFFFF;
}

.bg-danger-default-varient {
  background-color: #FF6628;
  color: #FFFFFF;
}

.bg-danger-dark-varient {
  background-color: #FF530D;
  color: #FFFFFF;
}

.bg-dark-primary-soft-varient {
  background-color: #DDE0E5;
  color: #273041;
}

.bg-dark-primary-light-varient {
  background-color: #363D4D;
  color: #FFFFFF;
}

.bg-dark-primary-default-varient {
  background-color: #273041;
  color: #FFFFFF;
}

.bg-dark-primary-dark-varient {
  background-color: #172133;
  color: #FFFFFF;
}

.bg-dark-secondary-soft-varient {
  background-color: #DADEE5;
  color: #596680;
}

.bg-dark-secondary-light-varient {
  background-color: #7A8599;
  color: #FFFFFF;
}

.bg-dark-secondary-default-varient {
  background-color: #596680;
  color: #FFFFFF;
}

.bg-dark-secondary-dark-varient {
  background-color: #424E66;
  color: #FFFFFF;
}

.bg-opacity-soft-varient {
  background: rgba(89, 145, 255, 0.25);
  color: #5991FF;
}

.bg-opacity-light-varient {
  background: rgba(89, 145, 255, 0.5);
  color: #FFFFFF;
}

.bg-opacity-default-varient {
  background: rgba(89, 145, 255, 0.75);
  color: #FFFFFF;
}

.bg-opacity-dark-varient {
  background-color: #5991FF;
  color: #FFFFFF;
}

.content-title h2 {
  font-weight: 600;
  font-size: 18px;
  line-height: 24px;
  color: #273041;
}

.search-item {
  position: relative;
}

.search-item .overlay {
  position: absolute;
  top: 50%;
  left: 15px;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}

.search-item .overlay a img {
  width: 20px;
  height: 20px;
}

.search-item input {
  padding: 11px 15px 12px 45px;
  border: 0;
  background: #F2F4F7;
  border-radius: 8px;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.table-style {
  width: 100%;
}

.table-style thead th {
  padding: 14px 20px;
  background: #F5F7FA;
  text-transform: capitalize;
  font-weight: 500;
  font-size: 14px;
  line-height: 17px;
  color: #273041;
}

.table-style thead th:first-child {
  border-radius: 10px 0 0 10px;
}

.table-style thead th:last-child {
  border-radius: 0 10px 10px 0;
}

.table-style tbody td {
  padding: 12px 20px;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.status {
  padding: 6px 10px 7px;
  border-radius: 4px;
  text-transform: capitalize;
  font-weight: 500;
  font-size: 12px;
  line-height: 15px;
}

.status.bg-grey {
  background: rgba(39, 48, 65, 0.1);
  color: rgba(39, 48, 65, 0.5);
}

.status.bg-yellow {
  background: #FCF6E8;
  color: #FFAA00;
}

.status.bg-green {
  background-color: #E1FAE1;
  color: #4CBF4C;
}

.status.bg-red {
  background-color: #FFE6E6;
  color: #F96363;
}

.color-grey {
  color: rgba(39, 48, 65, 0.5);
}

.color-red {
  color: #F96363;
}

.color-green {
  color: #4CBF4C;
}

.color-yellow {
  color: #FF964B;
}

.color-purple {
  color: #BE63F9;
}

.color-blue {
  color: #5991FF;
}

.view-style {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.view-style a {
  margin-left: 15px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #F2F4F7;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.view-style a i {
  font-size: 20px;
  color: #7A8599;
}

.view-style a.active i {
  color: #5991FF;
}

.error__item__area {
  width: 100%;
  min-height: 100vh;
  padding: 50px 0;
  background-image: url("../images/error/bg-error.jpg");
  background-repeat: no-repeat;
  background-size: cover;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  text-align: center;
}

.error__item__img {
  margin-bottom: 52px;
}

.error__item__content h2 {
  font-weight: 800;
  font-size: 50px;
  line-height: 65px;
  color: #FFFFFF;
  margin-bottom: 6px;
}

.error__item__content p {
  font-weight: 600;
  font-size: 18px;
  line-height: 24px;
  color: #FFFFFF;
  margin-bottom: 35px;
}

@media screen and (max-width: 767px) {
  .error__item__content p br {
    display: none;
  }
}

@media screen and (max-width: 480px) {
  .error__item__content h2 {
    font-size: 32px;
  }
  .error__item__content p {
    font-size: 14px;
  }
}

.error-button {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  margin: 0 auto;
  border-radius: 10px;
  padding: 13px 20px;
  font-style: normal;
  font-weight: bold;
  font-size: 16px;
  line-height: 19px;
  text-align: center;
  background-color: #ffffff;
  color: #BE63F9;
}

.item-top h2 {
  margin-bottom: 8px;
  text-transform: capitalize;
  font-weight: 600;
  font-size: 18px;
  line-height: 24px;
  color: #273041;
}

.item-top p {
  margin-bottom: 0;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.apexcharts-xaxis-label {
  font-family: "Inter", sans-serif;
  font-weight: 400;
  font-size: 12px;
  line-height: 22px;
  fill: #596680;
}

.lb-outerContainer {
  width: 50% !important;
  height: 50% !important;
}

.lb-outerContainer img {
  width: 100% !important;
  height: auto !important;
}

/*--------------------------------
03. Sections style
--------------------------------*/
.sidebar__area {
  position: fixed;
  z-index: 9999;
  top: 0;
  left: 0;
  width: 250px;
  height: 100vh;
  background-color: #1F242E;
  -webkit-transition: 0.4s;
  transition: 0.4s;
}

.sidebar__area.active {
  -webkit-transform: translateX(0%);
          transform: translateX(0%);
  width: 60px;
}

.sidebar__area.active .sidebar__brand {
  display: none;
}

.sidebar__area.active .sidebar__menu {
  margin-top: 60px;
}

.sidebar__area.active .sidebar__menu li a {
  padding: 10px 10px;
}

.sidebar__area.active .sidebar__menu li a span {
  display: none;
}

.sidebar__area.active .sidebar__menu .submenu.active li a {
  padding-left: 20px;
}

@media screen and (max-width: 1199px) {
  .sidebar__area {
    -webkit-transform: translateX(-100%);
            transform: translateX(-100%);
  }
  .sidebar__area.active {
    width: 250px;
  }
  .sidebar__area.active .sidebar__brand {
    display: block;
  }
  .sidebar__area.active .sidebar__menu {
    margin-top: 0;
  }
  .sidebar__area.active .sidebar__menu li a {
    padding: 11px 30px 9px;
  }
  .sidebar__area.active .sidebar__menu li a {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
  }
  .sidebar__area.active .sidebar__menu li a span {
    display: block;
  }
}

.sidebar__close {
  display: none;
  position: absolute;
  top: 20px;
  right: 10px;
}

@media screen and (max-width: 1199px) {
  .sidebar__close {
    display: block;
  }
}

.sidebar__close button {
  border: 0;
  background-color: transparent;
  padding: 5px;
  font-size: 20px;
  color: #F96363;
}

.sidebar__brand {
  padding: 24px 0 30px 30px;
}

.sidebar__menu {
  width: 100%;
  height: calc(100vh - 86px);
  overflow-y: auto;
  -ms-overflow-style: none;
  /* Internet Explorer 10+ */
  scrollbar-width: none;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}

.sidebar__menu::-webkit-scrollbar {
  display: none;
  /* Safari and Chrome */
}

.sidebar__menu li:not(:first-child) {
  margin-top: 0px;
}

.sidebar__menu li a {
  display: block;
  padding: 11px 30px 9px;
  text-transform: capitalize;
  font-weight: 500;
  font-size: 14px;
  line-height: 24px;
  color: #929BAC;
  -webkit-transition: 0.33s;
  transition: 0.33s;
}

.sidebar__menu li a i {
  color: #4B5A78;
}

.sidebar__menu li a span {
  margin-left: 13px;
}

.sidebar__menu li a.active, .sidebar__menu li a:hover {
  background-color: #1B1F28;
  color: #ffffff;
}

.sidebar__menu li a.active i, .sidebar__menu li a:hover i {
  color: #4D88FF;
}

.sidebar__menu ul li a {
  padding-left: 60px !important;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.sidebar__menu ul li a i {
  font-size: 4px;
}

.sidebar__menu ul li a span {
  margin-left: 11px;
}

.sidebar__menu ul li a:hover, .sidebar__menu ul li a.active {
  background-color: #1B1F28;
  color: #ffffff;
}

.sidebar__menu ul li a:hover i, .sidebar__menu ul li a.active i {
  color: #4D88FF;
}

.sidebar__menu ul ul li a {
  padding-left: 70px !important;
}

.metismenu .mm-active > a {
  background-color: #1B1F28;
  color: #ffffff;
}

.metismenu .mm-active > a i {
  color: #4D88FF;
}

.metismenu .has-arrow {
  position: relative;
}

.metismenu .has-arrow::after {
  border-width: 0 1px 1px 0;
}

.metismenu .mm-active > .has-arrow::after,
.metismenu .has-arrow[aria-expanded=true]::after {
  -webkit-transform: rotate(45deg) translate(0, -50%);
          transform: rotate(45deg) translate(0, -50%);
}

.header__area {
  width: 100%;
  height: 240px;
  background-image: url("../images/background/header-background.png");
  background-repeat: no-repeat;
  background-size: cover;
}

.header__navbar {
  padding: 20px 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

@media screen and (max-width: 545px) {
  .header__navbar__left {
    width: 100%;
    -webkit-box-pack: justify;
        -ms-flex-pack: justify;
            justify-content: space-between;
    margin-bottom: 20px;
  }
  .header__navbar__right {
    margin-bottom: 20px;
  }
}

.header__navbar__left {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.header__navbar__left > button {
  margin-right: 22px;
  border: 0;
  background-color: transparent;
  font-size: 14px;
  color: #7A8599;
}

.sidebar-toggler {
  display: none;
}

@media screen and (max-width: 1199px) {
  .sidebar-toggler {
    display: block;
  }
}

.header__search {
  position: relative;
  max-width: 250px;
}

.header__search input {
  width: 100%;
  border: 0;
  padding: 11px 40px 12px 18px;
  background: #2E384D;
  border-radius: 50px;
  font-size: 14px;
  font-weight: 400;
  line-height: 17px;
  color: #7A8599;
}

.header__search__overlay {
  position: absolute;
  top: 50%;
  right: 15px;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  border: 0;
  background-color: transparent;
}

.header__menu {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.header__menu li:not(:first-child) {
  margin-left: 25px;
}

.header__menu .dropdown-menu li {
  margin-left: 0;
}

.header__menu .dropdown-menu li a {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.header__menu .dropdown-menu li a img {
  width: 24px;
  height: 24px;
  border-radius: 50%;
}

.header__menu .dropdown-menu li a span {
  margin-left: 10px;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.header__menu li .user-profile img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}

.header__menu li .site-language img {
  width: 24px;
  height: 24px;
  border-radius: 50%;
}

.status__box {
  margin-bottom: 30px;
  height: calc(100% - 30px);
  padding: 44px 25px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.status__box__left {
  margin-right: 18px;
}

@media screen and (max-width: 1399px) {
  .status__box {
    padding: 44px 7px;
  }
}

@media screen and (max-width: 1199px) {
  .status__box {
    padding: 40px 13px;
  }
}

@media screen and (max-width: 991px) {
  .status__box {
    padding: 30px 15px;
  }
}

.status__box__img {
  margin-bottom: 0;
  width: 60px;
  height: 60px;
  border-radius: 12px;
  overflow: hidden;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

.status__box__img img {
  width: 100%;
  height: 100%;
}

.status__box__right p {
  margin-bottom: 0;
  text-transform: capitalize;
  font-size: 16px;
  font-weight: 400;
  line-height: 28px;
  color: #596680;
}

.status__box__data {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

.status__box__data h2 {
  text-transform: capitalize;
  font-weight: 600;
  font-size: 24px;
  line-height: 32px;
}

.status__box__data img {
  margin-left: 20px;
  width: 29px;
  height: 19px;
}

.recent__patient {
  height: calc(100% - 30px);
  margin-bottom: 30px;
  padding: 22px 25px 27px 25px;
}

.recent__patient h2 {
  font-weight: 600;
  font-size: 18px;
  line-height: 24px;
  color: #273041;
}

.recent__patient__table {
  margin-top: 7px;
  width: 100%;
  overflow-y: auto;
  scrollbar-width: thin;
}

.recent__patient__table table {
  width: 100%;
}

.recent__patient__table table tbody td {
  padding: 18px 0;
  border-bottom: 1px solid #ECEDF0;
}

.recent__patient__table table tbody td:nth-child(1) {
  min-width: 160px;
}

.recent__patient__table table tbody td:nth-child(2) {
  min-width: 100px;
  text-align: right;
}

.recent__patient__table table .status {
  padding: 0;
  font-weight: 500;
  font-size: 14px;
  line-height: 24px;
}

.badge {
  font-weight: 500;
  font-size: 14px;
  line-height: 24px;
  color: #273041;
}

.badge.color-purple {
  color: #BE63F9;
}

.badge.color-yellow {
  color: #FF9F38;
}

.badge.color-green {
  color: #4CBF4C;
}

.badge.color-red {
  color: #F96363;
}

.badge.color-grey {
  color: rgba(39, 48, 65, 0.5);
}

.revenue__chart__area {
  padding: 24px 25px 0px;
  margin-bottom: 30px;
  height: calc(100% - 30px);
}

.revenue__chart__top {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

@media screen and (max-width: 480px) {
  .revenue__chart__top {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-align: start;
        -ms-flex-align: start;
            align-items: flex-start;
  }
  .revenue__chart__top .revenue__chart__list {
    margin-top: 20px;
  }
}

.revenue__chart__list .nav.nav-tabs {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.revenue__chart__list .nav.nav-tabs button {
  border: 0;
  background-color: transparent;
  padding: 5px 14px !important;
  text-transform: capitalize;
  font-weight: 500;
  font-size: 13px;
  line-height: 16px;
  color: #596680;
}

.revenue__chart__list .nav.nav-tabs button.active {
  background: #E5EDFE;
  border-radius: 4px;
  color: #4D88FF;
}

.patient-statistics__chart__area {
  padding: 24px 25px 25px;
  margin-bottom: 40px;
  height: calc(100% - 40px);
}

@media screen and (max-width: 767px) {
  .patient-statistics__chart__area {
    padding: 24px 25px 24px;
  }
}

.patient__legend {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

.patient__legend .legend__item {
  text-align: center;
}

.patient__legend .legend__item:not(:first-child) {
  margin-left: 50px;
}

.patient__legend .legend__item h3 {
  text-transform: capitalize;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
  margin-bottom: 2px;
}

.patient__legend .legend__item h2 {
  font-weight: 600;
  font-size: 18px;
  line-height: 24px;
}

@media screen and (max-width: 991px) {
  .patient__legend .legend__item:not(:first-child) {
    margin-left: 20px;
  }
}

.top-doctor__item {
  height: calc(100% - 40px);
  margin-bottom: 40px;
  padding: 24px 25px 25px;
}

.top-doctor__table {
  margin-top: 9px;
  width: 100%;
  overflow-x: auto;
  scrollbar-width: thin;
}

.top-doctor__table table {
  width: 100%;
}

.top-doctor__table table tbody td {
  padding: 16px 10px 16px;
  border-bottom: 1px solid #ECEDF0;
}

.top-doctor__table table tbody td:nth-child(1) {
  min-width: 170px;
  padding-left: 0;
}

.top-doctor__table table .user-ratings {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.user__info {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.user__info .user__img {
  margin-right: 16px;
}

.user__info .user__img img {
  width: 50px;
  height: 50px;
  border-radius: 50%;
}

.user__info .user__text h2 {
  font-weight: 500;
  font-size: 14px;
  line-height: 24px;
  color: #273041;
}

.user__info .user__text h3 {
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.user-ratings {
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.user-ratings i {
  color: #FF9F38;
  margin-right: 8px;
}

.user-email {
  font-size: 14px;
  font-weight: 400;
  line-height: 24px;
  color: #596680;
}

.user-social-link {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.user-social-link a:not(:first-child) {
  margin-left: 5px;
}

.user-social-link a {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: #F2F6F7;
  font-size: 16px;
  color: #4D88FF;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.status__box.crypto {
  overflow: hidden;
  position: relative;
  margin-bottom: 30px;
  height: calc(100% - 30px);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding: 24px 25px 89px;
}

@media screen and (max-width: 991px) {
  .status__box.crypto {
    padding: 24px 15px 89px;
  }
}

.status__box.crypto .status__box__chart {
  width: 100%;
  height: 75px;
  z-index: 0;
  position: absolute;
  top: calc(100% - 75px);
  left: 0;
}

.status__box.crypto .status__box__info {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.status__box.crypto .status__box__info__right {
  margin-left: 18px;
}

.status__box.crypto .status__box__info__right h2 {
  text-transform: capitalize;
  font-weight: 600;
  font-size: 24px;
  line-height: 32px;
  color: #273041;
}

.status__box.crypto .status__box__data {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.status__box.crypto .status__box__data h2 {
  font-weight: 400;
  font-size: 16px;
  line-height: 28px;
  color: #596680;
}

.status__box.crypto .status__box__data img {
  margin-left: 20px;
  width: 16px;
  height: 10px;
}

.market-overview__chart__area {
  padding: 24px 25px 18px;
  margin-bottom: 30px;
  height: calc(100% - 30px);
}

.market-overview__top {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

.market-overview__chart__list .nav.nav-tabs {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.market-overview__chart__list .nav.nav-tabs button {
  border: 0;
  background-color: transparent;
  padding: 5px 14px !important;
  text-transform: capitalize;
  font-weight: 500;
  font-size: 13px;
  line-height: 16px;
  color: #596680;
}

.market-overview__chart__list .nav.nav-tabs button.active {
  background: #E5EDFE;
  border-radius: 4px;
  color: #4D88FF;
}

.market-overview__checkbox form {
  margin-top: 18px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin-left: -25px;
}

.market-overview__checkbox form .input__group {
  margin-left: 25px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
}

.market-overview__checkbox form .input__group label {
  margin-left: 8px;
  margin-bottom: 0;
  text-transform: capitalize;
  font-weight: 400;
  font-size: 11px;
  line-height: 20px;
  color: #596680;
}

@media screen and (max-width: 767px) {
  .market-overview__checkbox form {
    margin-left: -15px;
  }
  .market-overview__checkbox form .input__group {
    margin-left: 15px;
  }
}

.current-statistics__chart__area {
  padding: 24px 25px 22px;
  margin-bottom: 30px;
  height: calc(100% - 30px);
}

.current-statistics__top {
  margin-bottom: 10px;
}

.current-statistics__legend .legend__item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.current-statistics__legend .legend__item:not(:first-child) {
  margin-top: 10px;
}

.current-statistics__legend .legend__item h3 {
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.current-statistics__legend .legend__item h3 span {
  margin-right: 11px;
  display: block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
}

.current-statistics__legend .legend__item h2 {
  font-weight: 500;
  font-size: 14px;
  line-height: 24px;
  color: #273041;
}

.quick-trade__area {
  padding: 24px 25px 25px;
  margin-bottom: 30px;
  height: calc(100% - 30px);
}

.quick-trade__form {
  margin-top: 25px;
}

.quick-trade__form .input-group span {
  width: 130px;
  padding: 11px 15px 8px;
  background: #F0F4F7;
  border: 1px solid #DFE3EB;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  border-radius: 8px;
  text-transform: capitalize;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.quick-trade__form .input-group input {
  padding: 11px 16px 8px;
  background: #FAFBFC;
  border: 1px solid #DFE3EB;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  border-radius: 8px;
  text-transform: capitalize;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.quick-trade__form .input-group input:focus {
  -webkit-box-shadow: none;
          box-shadow: none;
}

.quick-trade__input__top {
  width: 100%;
  display: -ms-grid;
  display: grid;
  -ms-grid-columns: auto auto;
      grid-template-columns: auto auto;
  grid-column-gap: 25px;
}

@media screen and (max-width: 1399px) {
  .quick-trade__input__top {
    -ms-grid-columns: auto;
        grid-template-columns: auto;
  }
  .quick-trade__input__top__left {
    margin-bottom: 20px;
  }
}

.quick-trade__input__top__right {
  width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.quick-trade__input__top__right .item:last-child {
  text-align: right;
}

.quick-trade__input__top__right p {
  margin-bottom: 0;
  font-weight: 400;
  font-size: 12px;
  line-height: 24px;
  color: #596680;
}

.quick-trade__input__top__right h2 {
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  color: #273041;
}

.quick-trade__input__top__left .dropdown {
  padding: 12px 15px;
  background: #FAFBFC;
  border: 1px solid #DFE3EB;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  border-radius: 10px;
}

.quick-trade__input__top__left .dropdown button {
  border: 0;
  background-color: transparent;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #273041;
}

.quick-trade__input__top__left .dropdown button img {
  width: 24px;
  height: 24px;
}

.quick-trade__input__top__left .dropdown button span {
  margin-left: 8px;
}

.quick-trade__input__top__left .dropdown .dropdown-menu {
  width: 100%;
  position: absolute;
  top: 100% !important;
  inset: none !important;
  -webkit-transform: none !important;
          transform: none !important;
}

.latest-trading__area {
  padding: 24px 25px 25px;
  margin-bottom: 30px;
  height: calc(100% - 30px);
}

.latest-trading__table {
  margin-top: 10px;
  width: 100%;
  overflow-x: auto;
}

.latest-trading__table table {
  width: 100%;
}

.latest-trading__table table tbody td {
  padding: 15px 10px 15px;
  border-bottom: 1px solid #ECEDF0;
}

.latest-trading__table table tbody td:nth-child(1) {
  padding-left: 0;
  min-width: 50px;
}

.latest-trading__table table tbody td:nth-child(2) {
  min-width: 120px;
}

.latest-trading__table table tbody td:nth-child(3) {
  min-width: 120px;
}

.latest-trading__table table tbody td:nth-child(4) {
  min-width: 100px;
}

.latest-trading__table table tbody td:nth-child(5) {
  min-width: 100px;
}

.latest-trading__table table tbody td:nth-child(6) {
  min-width: 100px;
}

.latest-trading__table table .date,
.latest-trading__table table .time,
.latest-trading__table table .price {
  text-transform: capitalize;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.latest-trading__table table .status {
  text-transform: capitalize;
  font-weight: 500;
  font-size: 14px;
  line-height: 24px;
}

.type img {
  width: 40px;
  height: 40px;
  border-radius: 8px;
}

.trade-currency {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.trade-currency img {
  width: 24px;
  height: 24px;
  border-radius: 50%;
}

.trade-currency span {
  margin-left: 8px;
  font-weight: 500;
  font-size: 14px;
  line-height: 24px;
  color: #273041;
}

.status__box-3 {
  margin-bottom: 30px;
  padding: 44px 25px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.status__box-3 .item__left h2 {
  font-weight: normal;
  font-size: 16px;
  line-height: 28px;
  color: #596680;
}

.status__box-3 .status__box__data h2 {
  font-weight: 600;
  font-size: 24px;
  line-height: 32px;
  color: #273041;
}

.status__box-3 .status__box__data img {
  margin-left: 10px;
  width: 16px;
  height: 10px;
}

.status__box-3 .status__box__img img {
  width: 60px;
  height: 60px;
  border-radius: 12px;
}

.income-statistics__chart__area {
  margin-bottom: 30px;
  height: calc(100% - 30px);
  padding: 24px 25px 23px;
}

.income-statistics__chart__top {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.income-statistics__chart__top__left {
  margin-bottom: 14px;
}

.income-statistics__chart__top__left h2 {
  font-weight: 600;
  font-size: 18px;
  line-height: 24px;
  color: #273041;
}

.income-statistics__chart__top__right {
  margin-bottom: 14px;
}

@media screen and (max-width: 480px) {
  .income-statistics__chart__top {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-align: start;
        -ms-flex-align: start;
            align-items: flex-start;
  }
}

.total-income h2 {
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  color: #596680;
}

.total-income h2 span {
  color: #5991FF;
}

.income-statistics__chart__list .nav.nav-tabs {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.income-statistics__chart__list .nav.nav-tabs button {
  border: 0;
  background-color: transparent;
  padding: 5px 14px !important;
  text-transform: capitalize;
  font-weight: 500;
  font-size: 13px;
  line-height: 16px;
  color: #596680;
}

.income-statistics__chart__list .nav.nav-tabs button.active {
  background: #E5EDFE;
  border-radius: 4px;
  color: #4D88FF;
}

.sales-traffic__chart__area {
  padding: 24px 25px 24px;
  margin-bottom: 30px;
  height: calc(100% - 30px);
}

.sales-traffic__chart__area h2 {
  margin-bottom: 30px;
  font-weight: 600;
  font-size: 18px;
  line-height: 24px;
  color: #273041;
}

.sales-traffic__chart__area .sales-traffic__legend {
  margin-top: 29px;
  display: -ms-grid;
  display: grid;
  -ms-grid-columns: auto auto;
      grid-template-columns: auto auto;
  grid-column-gap: 50px;
  grid-row-gap: 6px;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

.sales-traffic__chart__area .sales-traffic__legend li a {
  font-weight: normal;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.sales-traffic__chart__area .sales-traffic__legend li a i {
  font-size: 12px;
}

.sales-traffic__chart__area .sales-traffic__legend li a span {
  margin-left: 8px;
}

@media screen and (max-width: 991px) {
  .sales-traffic__chart__area .sales-traffic__legend {
    grid-column-gap: 20px;
  }
}

@media screen and (max-width: 767px) {
  .sales-traffic__chart__area .sales-traffic__legend {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    margin-left: -20px;
  }
  .sales-traffic__chart__area .sales-traffic__legend li {
    margin-left: 20px;
  }
}

.recent-reviews__area {
  margin-bottom: 30px;
  height: calc(100% - 30px);
  padding: 25px;
}

.recent-reviews__title {
  margin-bottom: 10px;
}

.recent-reviews__title h2 {
  text-transform: capitalize;
  font-weight: 600;
  font-size: 18px;
  line-height: 24px;
  color: #273041;
}

.recent-reviews__table {
  width: 100%;
  overflow-x: auto;
}

.recent-reviews__table table {
  width: 100%;
}

.recent-reviews__table table tbody td {
  padding: 15px 0;
  border-bottom: 1px solid #ECEDF0;
}

.recent-reviews__table table tbody td:nth-child(1) {
  min-width: 220px;
}

.recent-reviews__table table .product-info {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.recent-reviews__table table .product-info__img {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 50px;
          flex: 0 0 50px;
  background: #F9F9FD;
  border-radius: 10px;
  width: 50px;
  height: 50px;
  margin-right: 16px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  overflow: hidden;
}

.recent-reviews__table table .product-info__text h2 {
  margin-bottom: 2px;
  text-transform: capitalize;
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  color: #273041;
}

.recent-reviews__table table .product-info__text p {
  margin-bottom: 0;
  font-weight: normal;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.recent-reviews__table table .rattings {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-left: -3px;
}

.recent-reviews__table table .rattings li {
  margin-left: 3px;
  color: #CED2D9;
}

.recent-reviews__table table .rattings li.active {
  color: #FFBF00;
}

.recent-activity__area {
  margin-bottom: 30px;
  height: calc(100% - 30px);
  padding: 25px;
}

.recent-activity__top h2 {
  margin-bottom: 19px;
  text-transform: capitalize;
  font-weight: 600;
  font-size: 18px;
  line-height: 24px;
  color: #273041;
}

.recent-activity__top a {
  text-transform: capitalize;
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  color: #5991FF;
}

.recent-activity__table {
  width: 100%;
  overflow-x: auto;
}

.recent-activity__table {
  width: 100%;
}

.recent-activity__table tbody td {
  padding: 0 30px;
}

.recent-activity__table table tbody tr {
  position: relative;
}

.recent-activity__table table tbody tr::before {
  position: absolute;
  z-index: 1;
  content: "";
  top: 50%;
  left: 0;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  width: 15px;
  height: 15px;
  border-radius: 50%;
  background-color: #ffffff;
  border: 4px solid #5991FF;
}

.recent-activity__table table tbody tr::after {
  position: absolute;
  z-index: 0;
  content: "";
  top: 0;
  left: 6px;
  width: 3px;
  height: 100%;
  background-color: #5991FF;
}

.recent-activity__table table tbody tr:first-child::after {
  height: 50%;
  top: unset;
  bottom: 0%;
}

.recent-activity__table table tbody tr:last-child::after {
  height: 50%;
  top: 0;
}

.recent-activity__table table .data-info {
  padding: 15px 0;
  border-bottom: 1px solid #ECEDF0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.recent-activity__table table .data-info__img {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 40px;
          flex: 0 0 40px;
  margin-right: 15px;
}

.recent-activity__table table .data-info__img img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}

.recent-activity__table table .data-info__text h2 {
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.recent-activity__table table .data-info__text p {
  margin-bottom: 0;
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;
  color: rgba(89, 102, 128, 0.6);
}

.recent-order__area {
  margin-bottom: 40px;
  padding: 24px 25px 25px;
}

.recent-order__area__top h2 {
  font-weight: 600;
  font-size: 18px;
  line-height: 24px;
  color: #273041;
  margin-bottom: 20px;
}

.recent-order__table {
  width: 100%;
  overflow-x: auto;
}

.recent-order__table table {
  width: 100%;
}

.recent-order__table table thead th {
  padding: 11px 20px 10px;
  background: #F5F7FA;
  font-weight: 500;
  font-size: 14px;
  line-height: 17px;
  color: #273041;
}

.recent-order__table table tbody td {
  border-bottom: 1px solid #ECEDF0;
  padding: 16px 20px 16px;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.recent-order__table table tbody td:nth-child(1) {
  min-width: 200px;
}

.recent-order__table table tbody td:nth-child(2) {
  min-width: 200px;
}

.recent-order__table table tbody td:nth-child(3) {
  min-width: 200px;
}

.recent-order__table table tbody td:nth-child(4) {
  min-width: 150px;
}

.recent-order__table table tbody td:nth-child(5) {
  min-width: 150px;
}

.recent-order__table table tbody td:nth-child(6) {
  min-width: 150px;
}

.recent-order__table table tbody td:nth-child(7) {
  min-width: 150px;
}

.status__box__v3 {
  padding: 30px 30px 20px;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  text-align: center;
  position: relative;
}

.status__box .overlay {
  position: absolute;
  top: 20px;
  right: 20px;
}

.status__box__text h2 {
  margin-bottom: 2px;
  text-transform: capitalize;
  font-weight: 600;
  font-size: 24px;
  line-height: 32px;
}

.status__box__text h3 {
  font-weight: 400;
  text-transform: capitalize;
  font-size: 16px;
  line-height: 28px;
  color: #596680;
}

.status__box__v3 .status__box__img {
  margin-bottom: 14px;
  width: 50px;
  height: 50px;
  border-radius: 12px;
  overflow: hidden;
}

.status__box .dropdown-menu.show {
  background: #ffffff;
  border: 1px solid #E9EDF5;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  -webkit-box-shadow: 0px 0px 10px rgba(163, 177, 204, 0.12), 0px 10px 20px -5px rgba(163, 177, 204, 0.2);
          box-shadow: 0px 0px 10px rgba(163, 177, 204, 0.12), 0px 10px 20px -5px rgba(163, 177, 204, 0.2);
  border-radius: 6px;
  padding: 8px 0;
  inset: 0px 0px auto auto !important;
}

.status__box .dropdown-menu.show .dropdown-item {
  font-weight: 400;
  padding: 5px 16px;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.revenue__chart-v2__area {
  padding: 25px;
  margin-bottom: 30px;
  height: calc(100% - 30px);
}

.revenue__chart-v2__top {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.revenue__chart-v2__list .nav.nav-tabs {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.revenue__chart-v2__list .nav.nav-tabs button {
  border: 0;
  background-color: transparent;
  padding: 5px 14px !important;
  text-transform: capitalize;
  font-weight: 500;
  font-size: 13px;
  line-height: 16px;
  color: #596680;
}

.revenue__chart-v2__list .nav.nav-tabs button.active {
  background: #E5EDFE;
  border-radius: 4px;
  color: #4D88FF;
}

.revenue__chart-v2__area #revenue-chart-v2 .apexcharts-canvas {
  margin-left: -15px;
}

.total-profit {
  margin-top: 14px;
  margin-bottom: 3px;
}

.total-profit h2 {
  text-transform: capitalize;
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  color: #596680;
}

.total-profit h2 span {
  color: #5991FF;
}

.sales-location__area {
  margin-bottom: 30px;
  height: calc(100% - 30px);
  padding: 25px;
}

.sales-location__title {
  margin-bottom: 28px;
}

.sales-location__title h2 {
  text-transform: capitalize;
  font-weight: 600;
  font-size: 18px;
  line-height: 24px;
  color: #273041;
}

.sales-location__map .map-item {
  width: 100%;
  height: 180px;
}

.sales-location__map .jvectormap-zoomin,
.sales-location__map .jvectormap-zoomout {
  display: none;
}

.sales-location__map .jvectormap-marker:nth-child(1) {
  stroke: #F28484;
  fill: #FF4D4D;
}

.sales-location__map .jvectormap-marker:nth-child(2) {
  stroke: #B54DFF;
  fill: #C484F2;
}

.sales-location__map .jvectormap-marker:nth-child(3) {
  stroke: #B54DFF;
  fill: #C484F2;
}

.sales-location__map .jvectormap-marker:nth-child(4) {
  stroke: #FF974D;
  fill: #F2B284;
}

.sales-location__map .jvectormap-marker:nth-child(5) {
  stroke: #3DCC3D;
  fill: #7DE57D;
}

.sales-location__bottom {
  margin-top: 13px;
}

.sales-location__total {
  margin-bottom: 17px;
  text-align: center;
}

.sales-location__total p {
  margin-bottom: 0;
  font-weight: 500;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.sales-location__total h2 {
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  color: #273041;
}

.sales-location__legend ul {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.sales-location__legend ul .legend-item {
  text-align: center;
}

.sales-location__legend ul .legend-item p {
  margin-bottom: 1px;
  text-transform: capitalize;
  font-weight: 500;
  font-size: 12px;
  line-height: 15px;
  color: #596680;
}

.sales-location__legend ul .legend-item h2 {
  font-weight: 500;
  font-size: 14px;
  line-height: 24px;
  color: #BE63F9;
}

.top-products__area {
  padding: 20px 24px;
  margin-bottom: 30px;
  height: calc(100% - 30px);
}

.top-products__area h2 {
  margin-bottom: 19px;
  text-transform: capitalize;
  font-weight: 600;
  font-size: 18px;
  line-height: 24px;
  color: #273041;
}

.top-products__table {
  width: 100%;
  overflow-x: auto;
}

.top-products__table table thead th {
  padding: 8px 14px;
  text-transform: capitalize;
  font-weight: 500;
  font-size: 14px;
  line-height: 24px;
  color: #273041;
}

.top-products__table table tbody td {
  padding-top: 16px;
  padding-left: 14px;
  padding-right: 14px;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.top-products__table table tbody td:nth-child(1) {
  min-width: 200px;
}

.top-products__table table tbody td:nth-child(2) {
  min-width: 120px;
}

.top-products__table table tbody td:nth-child(3) {
  min-width: 120px;
}

.top-products__table table tbody td:nth-child(4) {
  min-width: 80px;
}

.calendar__area {
  padding: 30px;
  margin-bottom: 40px;
}

@media screen and (max-width: 480px) {
  .calendar__area {
    padding: 15px;
  }
}

@media screen and (max-width: 767px) {
  .calendar__sidebar {
    margin-bottom: 30px;
  }
}

.calendar__sidebar .item:not(:last-child) {
  margin-bottom: 30px;
}

.calendar__event {
  background: #FFFFFF;
  border: 1px solid #E4E6EB;
  border-radius: 12px;
  padding: 20px 25px 30px;
}

.calendar__event__top {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.calendar__event__top h2 {
  text-transform: capitalize;
  font-weight: 500;
  font-size: 18px;
  line-height: 22px;
  color: #273041;
}

.calendar__event__top a {
  width: 26px;
  height: 26px;
  background: #F2F4F7;
  border-radius: 32px;
  font-size: 10px;
  color: #596680;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.calendar__event__top a:hover {
  background: #cfd7e4;
}

.calendar__event__list {
  margin-top: 24px;
}

.calendar__event__list li:not(:first-child) {
  margin-top: 18px;
}

.calendar__event__list .event__item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.calendar__event__list .event__item label {
  margin-left: 10px;
  text-transform: capitalize;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.calendar__event__list .event__item input[type="checkbox"] {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 4px;
  position: relative;
}

.calendar__event__list .event__item input[type="checkbox"]::before {
  position: absolute;
  content: "\f00c";
  font-family: "FontAwesome";
  top: 0;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  font-size: 13px;
  color: #ffffff;
  opacity: 0;
  visibility: hidden;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.calendar__event__list .event__item input[type="checkbox"]:checked::before {
  opacity: 1;
  visibility: visible;
}

.calendar__mini__area {
  background: #FFFFFF;
  border: 1px solid #E4E6EB;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  border-radius: 12px;
  overflow: hidden;
  padding: 25px;
}

@media screen and (max-width: 480px) {
  .calendar__mini__area {
    padding: 10px;
  }
}

.buttons-container {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-bottom: 10px;
  padding-bottom: 10px;
  border-bottom: 0;
}

.buttons-container button {
  font-size: 12px;
  color: #273041;
}

.buttons-container span {
  text-transform: capitalize;
  font-weight: 500;
  font-size: 16px;
  line-height: 19px;
  color: #273041;
}

.weeks-wrapper.header {
  border-bottom: 0;
}

.week .day.header {
  text-transform: capitalize;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

.week .day.header:first-child, .week .day.header:last-child {
  color: #596680;
}

.day {
  text-align: center;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

.week .day:first-child,
.week .day:last-child {
  color: #596680;
}

.week .day.highlight span {
  color: #596680;
}

.week .day.selected span,
.day.today span {
  background: #5991FF;
  color: #ffffff;
}

.day span,
.day.today span {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

.week:not(.start-on-monday) .day:first-child,
.week:not(.start-on-monday) .day:last-child {
  color: #596680;
}

.day.today span::after {
  display: none;
}

.fc-timegrid-axis-cushion {
  text-transform: capitalize;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.fc-timegrid-slot-label-cushion.fc-scrollgrid-shrink-cushion {
  text-transform: uppercase;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.calendar__view {
  background: #ffffff;
  border: 1px solid #E4E6EB;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  border-radius: 12px;
  overflow: hidden;
  padding-top: 24px;
}

.calendar__view .fc .fc-header-toolbar {
  padding: 0 24px;
}

.calendar__view .fc .fc-toolbar.fc-header-toolbar {
  margin-bottom: 25px;
}

.calendar__view .fc .fc-toolbar-title {
  text-transform: capitalize;
  font-weight: 600;
  font-size: 18px;
  line-height: 24px;
  color: #273041;
}

.calendar__view .fc .fc-today-button {
  border: 0;
  padding: 6px 18px 7px;
  background: #F2F4F7;
  border-radius: 6px;
  text-transform: capitalize;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
  -webkit-box-shadow: none;
          box-shadow: none;
}

.calendar__view .fc .fc-today-button:focus {
  -webkit-box-shadow: none;
          box-shadow: none;
}

.calendar__view .fc .fc-button-group {
  border: 0;
}

.calendar__view .fc .fc-button-group .fc-button.fc-button-primary {
  border: 0;
  border-radius: 0;
  padding: 6px 12px;
  background-color: transparent;
  text-transform: capitalize;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
  -webkit-box-shadow: none;
          box-shadow: none;
}

.calendar__view .fc .fc-button-group .fc-button.fc-button-primary .fc-icon {
  color: #273041;
}

.calendar__view .fc .fc-button-group .fc-button.fc-button-primary.fc-button-active, .calendar__view .fc .fc-button-group .fc-button.fc-button-primary.fc-button-active:focus {
  -webkit-box-shadow: none;
          box-shadow: none;
  background: #E5EDFE;
  border-radius: 6px;
  font-weight: 500;
  color: #5991FF;
}

.fc .fc-cell-shaded,
.fc .fc-day-disabled {
  background: transparent;
}

.calendar__view .fc-col-header thead th {
  padding: 15px 0;
  background-color: #F0F2F5;
  text-transform: capitalize;
  font-weight: 500;
  font-size: 16px;
  line-height: 19px;
  color: #596680;
}

.calendar__view .fc-daygrid-day-number {
  font-weight: 400;
  font-size: 16px;
  line-height: 19px;
  color: #596680;
  text-decoration: none;
}

.calendar__view .fc-daygrid-block-event .fc-event-time,
.calendar__view .fc-daygrid-block-event .fc-event-title {
  padding: 8px 15px;
}

.calendar__view .fc .fc-daygrid-day-frame {
  position: relative;
  min-height: 110px;
}

.calendar__mini__area .fc-scrollgrid-sync-table td {
  width: 36px;
  height: 36px;
  border-radius: 50%;
}

.calendar__mini__area .fc .fc-highlight {
  background-color: #5991FF !important;
  color: #ffffff !important;
}

.fc .fc-highlight {
  background-color: #F1F6FE !important;
  color: #5991FF !important;
}

@media screen and (max-width: 575px) {
  .fc-header-toolbar.fc-toolbar.fc-toolbar-ltr {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
  .fc-header-toolbar.fc-toolbar.fc-toolbar-ltr .fc-toolbar-chunk:not(:first-child) {
    margin-top: 10px;
  }
}

.chat-sidebar__area {
  margin-bottom: 30px;
  height: calc(100% - 30px);
  padding: 25px 0;
}

.chat-sidebar__top {
  padding: 0 25px;
  margin-bottom: 25px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.chat-sidebar__top__left {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.chat-sidebar__top .user-img {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 45px;
          flex: 0 0 45px;
  margin-right: 12px;
}

.chat-sidebar__top .user-img img {
  width: 45px;
  height: 45px;
  border-radius: 50%;
}

.chat-sidebar__top .user-text h2 {
  text-transform: capitalize;
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  color: #273041;
}

.chat-sidebar__top .user-text h3 {
  text-transform: capitalize;
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;
  color: #596680;
}

.chat-sidebar__top .user-text h3 i {
  font-size: 10px;
  color: #4CBF4C;
}

.chat-sidebar__top .user-text h3 span {
  margin-left: 5px;
}

.chat-sidebar__search {
  padding: 0 25px;
  margin-bottom: 25px;
}

.chat-sidebar__search .input__group {
  position: relative;
}

.chat-sidebar__search .input__group .overlay {
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  left: 15px;
  width: 20px;
  height: 20px;
  border: 0;
  background-color: transparent;
}

.chat-sidebar__search .input__group input {
  padding: 11px 24px 12px 45px;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
  background: #F2F4F7;
  border-radius: 8px;
}

.chat-sidebar__tabs .nav.nav-tabs {
  padding: 0 25px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.chat-sidebar__tabs .nav-link {
  width: calc(100% / 3);
  border: 0;
  border-bottom: 1px solid #EBEDF2;
  padding-bottom: 17px !important;
  text-transform: capitalize;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.chat-sidebar__tabs .nav-link.active {
  font-weight: 500;
  color: #5991FF;
  border-bottom: 1px solid #5991FF;
}

.chat-sidebar__user__list ul {
  height: 530px;
  overflow-y: auto;
  -ms-overflow-style: none;
  /* Internet Explorer 10+ */
  scrollbar-width: none;
}

.chat-sidebar__user__list ul::-webkit-scrollbar {
  display: none;
  /* Safari and Chrome */
}

.chat-sidebar__user__list ul li {
  margin-top: 10px;
}

.chat-sidebar__user__list .list-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding: 12px 25px;
}

.chat-sidebar__user__list .list-item.active, .chat-sidebar__user__list .list-item:hover {
  background: rgba(89, 145, 255, 0.1);
}

.chat-sidebar__user__list .list-item__left {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.chat-sidebar__user__list .list-item .user-img {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 50px;
          flex: 0 0 50px;
  margin-right: 14px;
}

.chat-sidebar__user__list .list-item .user-img img {
  width: 50px;
  height: 50px;
  border-radius: 50%;
}

.chat-sidebar__user__list .list-item .user-text h2 {
  margin-bottom: 2px;
  text-transform: capitalize;
  font-weight: 500;
  font-size: 14px;
  line-height: 24px;
  color: #273041;
}

.chat-sidebar__user__list .list-item .user-text p {
  margin-bottom: 0;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #273041;
}

.chat-sidebar__user__list .list-item__right {
  text-align: right;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: end;
      -ms-flex-align: end;
          align-items: flex-end;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}

.chat-sidebar__user__list .list-item .msg-date {
  text-transform: capitalize;
  font-weight: 400;
  font-size: 12px;
  line-height: 15px;
  color: #596680;
}

.chat-sidebar__user__list .list-item .msg-count {
  margin-bottom: 0;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  background: #FF6628;
  font-weight: normal;
  font-size: 11px;
  line-height: 13px;
  color: #FFFFFF;
}

.chat-content__area {
  margin-bottom: 30px;
  height: calc(100% - 30px);
}

.chat-content__top {
  padding: 24px;
  border-bottom: 1px solid #E4E6EB;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.chat-content__top__left {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.chat-content__top__left .user-img {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 50px;
          flex: 0 0 50px;
  margin-right: 12px;
}

.chat-content__top__left .user-img img {
  width: 50px;
  height: 50px;
  border-radius: 50%;
}

.chat-content__top__left .user-text h2 {
  text-transform: capitalize;
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  color: #273041;
}

.chat-content__top__left .user-text h3 {
  text-transform: capitalize;
  font-weight: normal;
  font-size: 12px;
  line-height: 20px;
  color: #596680;
}

.chat-content__top__left .user-text h3 i {
  font-size: 10px;
  color: #4CBF4C;
}

.chat-content__top__left .user-text h3 span {
  margin-left: 5px;
}

.chat-content__top__action {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-left: -20px;
}

.chat-content__top__action > li {
  margin-left: 20px;
}

.chat-content__top__action .dropdown button {
  border: 0;
  background-color: transparent;
}

.chat-content__top__action .dropdown .dropdown-menu {
  background: #FFFFFF;
  border: 1px solid #E9EDF5;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  -webkit-box-shadow: 0px 0px 10px rgba(163, 177, 204, 0.12), 0px 10px 20px -5px rgba(163, 177, 204, 0.2);
          box-shadow: 0px 0px 10px rgba(163, 177, 204, 0.12), 0px 10px 20px -5px rgba(163, 177, 204, 0.2);
  border-radius: 6px;
}

.chat-content__top__action .dropdown .dropdown-menu.show {
  position: absolute;
  inset: 0px 0px auto auto !important;
  margin: 0px;
  -webkit-transform: translate(0px, 26px);
          transform: translate(0px, 26px);
}

.chat-content__top__action .dropdown .dropdown-item {
  padding: 5px 12px;
  text-transform: capitalize;
  font-weight: normal;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.chat-content__top__action .dropdown .dropdown-item img {
  width: 16px;
  height: 16px;
}

.chat-content__top__action .dropdown .dropdown-item span {
  margin-left: 10px;
}

.chat-content__msg__list {
  margin-top: 14px;
  padding: 0 24px 24px 24px;
  height: 562px;
  overflow-y: auto;
  -ms-overflow-style: none;
  /* Internet Explorer 10+ */
  scrollbar-width: none;
}

.chat-content__msg__list::-webkit-scrollbar {
  display: none;
  /* Safari and Chrome */
}

.chat-content__msg__list li:not(:first-child) {
  margin-top: 25px;
}

.recieve-msg-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.recieve-msg-item .user-img {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 40px;
          flex: 0 0 40px;
  margin-right: 14px;
}

.recieve-msg-item .user-img img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}

.recieve-msg-item .user-text h2 {
  margin-bottom: 7px;
  font-weight: 500;
  font-size: 14px;
  line-height: 17px;
  color: #273041;
}

.recieve-msg-item .user-text h2 span {
  margin-left: 15px;
  font-weight: 400;
  font-size: 12px;
  line-height: 15px;
  color: #596680;
}

.msg-text {
  position: relative;
}

.msg-text:not(:first-child) {
  margin-top: 5px;
}

.msg-text .overlay {
  position: absolute;
  z-index: 9;
  top: 50%;
  left: 15%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.msg-text p {
  width: 80%;
  border-radius: 10px;
  padding: 12px 16px;
  margin-bottom: 0;
  font-weight: normal;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.msg-text p:not(:first-child) {
  margin-top: 5px;
}

.msg-text .overlay .dropdown button {
  border: 0;
  background-color: transparent;
}

.msg-text .overlay .dropdown .dropdown-menu {
  background: #FFFFFF;
  border: 1px solid #E9EDF5;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  -webkit-box-shadow: 0px 0px 10px rgba(163, 177, 204, 0.12), 0px 10px 20px -5px rgba(163, 177, 204, 0.2);
          box-shadow: 0px 0px 10px rgba(163, 177, 204, 0.12), 0px 10px 20px -5px rgba(163, 177, 204, 0.2);
  border-radius: 6px;
}

.msg-text .overlay .dropdown li:not(:first-child) {
  margin-top: 10px;
}

.msg-text .overlay .dropdown li:first-child {
  margin-top: 0;
}

.msg-text .overlay .dropdown .dropdown-menu.show {
  position: absolute;
  margin: 0px;
  -webkit-transform: translate(0px, 26px);
          transform: translate(0px, 26px);
}

.msg-text .overlay .dropdown .dropdown-item {
  padding: 5px 12px;
  text-transform: capitalize;
  font-weight: normal;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.recieve-msg-item .msg-text .overlay {
  left: unset;
  right: 8%;
}

.recieve-msg-item .msg-text .overlay a {
  margin-right: 10px;
}

@media screen and (max-width: 480px) {
  .recieve-msg-item .msg-text .overlay {
    right: 0%;
  }
}

.recieve-msg-item .msg-text p {
  background: #F2F5FC;
}

.send-msg-item .user-text h2 {
  margin-bottom: 7px;
  font-size: 12px;
  line-height: 15px;
  text-align: right;
  color: #596680;
}

.send-msg-item .seen-by {
  margin-top: 5px;
  text-align: right;
}

.send-msg-item .seen-by img {
  width: 20px;
  height: 20px;
  border-radius: 50%;
}

.send-msg-item .msg-text p {
  margin-left: auto;
  background: #5991FF;
  color: #ffffff;
}

.chat-content__inputs {
  width: 100%;
  padding: 25px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.chat-content__inputs .item-left {
  -webkit-box-flex: 50%;
      -ms-flex: 50%;
          flex: 50%;
  margin-right: 10px;
}

.chat-content__inputs .item-right {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-left: -20px;
}

.chat-content__inputs .item-right .input__group {
  margin-left: 20px;
}

@media screen and (max-width: 575px) {
  .chat-content__inputs {
    -webkit-box-align: start;
        -ms-flex-align: start;
            align-items: flex-start;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
  .chat-content__inputs .item-left {
    -webkit-box-flex: 100%;
        -ms-flex: 100%;
            flex: 100%;
    width: 100%;
    margin-right: 0px;
    margin-bottom: 10px;
  }
  .chat-content__inputs .item-right {
    -webkit-box-align: start;
        -ms-flex-align: start;
            align-items: flex-start;
  }
}

.chat-content__inputs .input__group {
  position: relative;
}

.chat-content__inputs .input__group .overlay {
  border: 0;
  background-color: transparent;
  position: absolute;
  top: 50%;
  left: 0px;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}

.chat-content__inputs .input__group label {
  margin-bottom: 0;
}

.chat-content__inputs .input__group input {
  width: 100%;
  padding: 17px 16px 16px 60px;
  background: #F6F7FA;
  border-radius: 60px;
}

.chat-content__inputs input[type='file'] {
  display: none;
}

.chat-content__inputs label {
  cursor: pointer;
  background-color: #F6F7FA;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

.chat-content__inputs button {
  width: 50px;
  height: 50px;
  border-radius: 50%;
}

.file-manager__sidebar {
  padding: 24px;
  margin-bottom: 40px;
  height: calc(100% - 40px);
}

.file-manager__sidebar__list ul li:not(:first-child) {
  margin-top: 20px;
}

.file-manager__sidebar__list ul li a {
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.file-manager__sidebar__list ul li a span {
  margin-left: 15px;
}

.storage-status__top {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.storage-status__top h2 {
  font-weight: 500;
  font-size: 14px;
  line-height: 24px;
  color: #273041;
}

.storage-status__top h3 {
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;
  color: #596680;
}

.storage-status .progress {
  margin-top: 7px;
  height: 5px;
  background: #F3F4F7;
  border-radius: 10px;
}

.storage-status .progress .progress-bar {
  background: #4CBF4C;
  border-radius: 10px;
}

.storage-status p {
  margin-top: 9px;
  margin-bottom: 0;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.upgrade-storage {
  background: #EBF1FF;
  border: 1px solid #DFE6F7;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  border-radius: 10px;
  padding: 25px 18px;
}

.upgrade-storage img {
  margin-bottom: 20px;
}

.upgrade-storage h2 {
  margin-bottom: 9px;
  text-transform: capitalize;
  font-weight: 600;
  font-size: 16px;
  line-height: 19px;
  color: #5991FF;
}

.upgrade-storage p {
  margin-bottom: 20px;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: #596680;
}

.file-manager__area {
  padding: 25px;
  margin-bottom: 40px;
  height: calc(100% - 40px);
}

.file-manager__area__top {
  margin-bottom: 25px;
}

@media screen and (max-width: 991px) {
  .file-manager__area__top__left {
    margin-bottom: 20px;
  }
}

.file-manager__area__top__left .input__group {
  position: relative;
}

.file-manager__area__top__left .input__group .overlay {
  position: absolute;
  top: 50%;
  left: 15px;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}

.file-manager__area__top__left .input__group input {
  width: 100%;
  border: 1px solid transparent;
  padding: 11px 15px 12px 45px;
  background: #F2F4F7;
  border-radius: 8px;
}

.file-manager__area__top__right {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

@media screen and (max-width: 575px) {
  .file-manager__area__top__right {
    -webkit-box-pack: start;
        -ms-flex-pack: start;
            justify-content: flex-start;
  }
}

.grid-view-btn.active i,
.list-view-btn.active i {
  color: #5991FF;
}

.grid-view-btn.active img,
.list-view-btn.active img {
  -webkit-filter: invert(53%) sepia(11%) saturate(6819%) hue-rotate(200deg) brightness(101%) contrast(101%);
          filter: invert(53%) sepia(11%) saturate(6819%) hue-rotate(200deg) brightness(101%) contrast(101%);
}

#grid-style {
  display: block;
}

#list-style {
  display: none;
}

.file-manager__content {
  margin-bottom: 5px;
}

.file-manager__content h2 {
  text-transform: capitalize;
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  color: #273041;
  margin-bottom: 13px;
}

.file__item {
  margin-bottom: 25px;
  height: calc(100% - 25px);
  background: #FAFBFC;
  border: 1px solid #EBEDF0;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  border-radius: 10px;
  padding: 30px 15px 20px;
  position: relative;
}

.file__item .overlay {
  position: absolute;
  top: 15px;
  right: 15px;
}

.file__item__link {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  text-align: center;
}

.file__item__link img {
  width: 40px;
  height: 40px;
}

.file__item__link span {
  margin-top: 14px;
  font-weight: 500;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.file__item .dropdown .dropdown-menu.show {
  background: #ffffff;
  border: 1px solid #E9EDF5;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  -webkit-box-shadow: 0px 0px 10px rgba(163, 177, 204, 0.12), 0px 10px 20px -5px rgba(163, 177, 204, 0.2);
          box-shadow: 0px 0px 10px rgba(163, 177, 204, 0.12), 0px 10px 20px -5px rgba(163, 177, 204, 0.2);
  border-radius: 6px;
  padding: 8px 0;
  inset: 0px 0px auto auto !important;
}

.file__item .dropdown .dropdown-menu.show .dropdown-item {
  padding: 5px 12px;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.file__item .dropdown .dropdown-menu.show .dropdown-item span {
  margin-left: 10px;
}

.file__item .dropdown .dropdown-menu.show .dropdown-item img {
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.file__item .dropdown .dropdown-menu.show .dropdown-item:hover {
  background: rgba(89, 145, 255, 0.1);
  color: #5991FF;
}

.file__item .dropdown .dropdown-menu.show .dropdown-item:hover img {
  -webkit-filter: invert(49%) sepia(89%) saturate(2153%) hue-rotate(202deg) brightness(104%) contrast(100%);
          filter: invert(49%) sepia(89%) saturate(2153%) hue-rotate(202deg) brightness(104%) contrast(100%);
}

.file-manager__table {
  width: 100%;
  overflow-x: auto;
}

.file-manager__table table {
  width: 100%;
}

.file-manager__table table thead th {
  padding: 13px 20px;
  font-weight: 500;
  font-size: 14px;
  line-height: 24px;
  color: #273041;
}

.file-manager__table table tbody td {
  padding: 15px 20px;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
  border-bottom: 1px solid #ECEDF0;
}

.file-manager__table table tbody td:nth-child(1) {
  min-width: 300px;
}

.file-manager__table table tbody td:nth-child(2) {
  min-width: 120px;
}

.file-manager__table table tbody td:nth-child(3) {
  min-width: 150px;
}

.file-manager__table table tbody td:nth-child(4) {
  min-width: 200px;
}

.file-manager__table table tbody td:nth-child(5) {
  min-width: 100px;
}

.member-list ul {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.member-list ul li:not(:first-child) {
  margin-left: -20px;
}

.member-list ul li a img {
  width: 34px;
  height: 34px;
  border-radius: 50%;
  border: 2px solid #ffffff;
}

.file-manager__table .file-name img {
  width: 25px;
  height: 25px;
}

.file-manager__table .file-name span {
  margin-left: 15px;
}

.file-manager__table .action button {
  background: #FAFBFC;
  border: 1px solid #F1F2F5;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  border-radius: 6px;
  padding: 8px 13px;
}

.file-manager__table .action .dropdown .dropdown-menu.show {
  background: #ffffff;
  border: 1px solid #E9EDF5;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  -webkit-box-shadow: 0px 0px 10px rgba(163, 177, 204, 0.12), 0px 10px 20px -5px rgba(163, 177, 204, 0.2);
          box-shadow: 0px 0px 10px rgba(163, 177, 204, 0.12), 0px 10px 20px -5px rgba(163, 177, 204, 0.2);
  border-radius: 6px;
  padding: 8px 0;
  inset: 0px 0px auto auto !important;
}

.file-manager__table .action .dropdown .dropdown-menu.show .dropdown-item {
  padding: 5px 12px;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.file-manager__table .action .dropdown .dropdown-menu.show .dropdown-item span {
  margin-left: 10px;
}

.file-manager__table .action .dropdown .dropdown-menu.show .dropdown-item img {
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.file-manager__table .action .dropdown .dropdown-menu.show .dropdown-item:hover {
  background: rgba(89, 145, 255, 0.1);
  color: #5991FF;
}

.file-manager__table .action .dropdown .dropdown-menu.show .dropdown-item:hover img {
  -webkit-filter: invert(49%) sepia(89%) saturate(2153%) hue-rotate(202deg) brightness(104%) contrast(100%);
          filter: invert(49%) sepia(89%) saturate(2153%) hue-rotate(202deg) brightness(104%) contrast(100%);
}

.product__area {
  height: calc(100% - 30px);
  padding: 24px 25px;
  margin-bottom: 30px;
}

.product__area__top {
  margin-bottom: 35px;
}

.product__area__top .input__group {
  position: relative;
}

.product__area__top .input__group .overlay {
  position: absolute;
  top: 50%;
  left: 15px;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}

.product__area__top .input__group input {
  width: 100%;
  border: 1px solid transparent;
  padding: 11px 15px 12px 45px;
  background: #F2F4F7;
  border-radius: 8px;
}

.product__area__top__right {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

@media screen and (max-width: 991px) {
  .product__area__top__right {
    -webkit-box-pack: start;
        -ms-flex-pack: start;
            justify-content: flex-start;
    margin-top: 20px;
  }
}

.sort-by {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

.sort-by h2 {
  margin-right: 15px;
  font-family: Inter;
  font-style: normal;
  font-weight: normal;
  font-size: 14px;
  line-height: 27px;
  color: #666B87;
}

.sort-by__list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  background: #F2F4F7;
  border-radius: 8px;
  padding: 11px 15px 12px;
}

.sort-by__list li:not(:first-child) {
  margin-left: 10px;
}

.sort-by__list li a {
  border: 0;
  border-radius: 0;
  background-color: transparent;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #666B87;
}

.sort-by__list li a.active {
  background-color: transparent;
  font-weight: 600;
  font-size: 14px;
  line-height: 24px;
  color: #5991FF;
}

.sort-by__list .nav-link {
  padding: 0 !important;
  background-color: transparent;
  border: 0;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  border-radius: 0px;
  text-transform: capitalize;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #666B87;
}

.sort-by__list .nav-link:not(:first-child) {
  margin-left: 30px;
}

.sort-by__list .nav-link.active {
  background-color: transparent;
  border: 0;
  font-weight: 600;
  font-size: 14px;
  line-height: 24px;
  text-align: center;
  color: #5991FF;
}

.view-style {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

@media screen and (max-width: 480px) {
  .view-style {
    margin-top: 10px;
  }
}

.view-style a {
  margin-left: 15px;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #F2F4F7;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.product__area #grid-style {
  display: block;
}

.product__area #list-style {
  display: none;
}

.product__item {
  margin-bottom: 26px;
  background: #ffffff;
  border: 1px solid #DFE3EB;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  border-radius: 10px;
  overflow: hidden;
}

.product__item__img {
  background: #F8F9FA;
  height: 154px;
  margin-bottom: 13px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

.product__item__info {
  padding: 0 16px;
}

.product__item__button {
  padding: 24px 16px 20px;
}

.product__item__button a {
  width: 100%;
}

.product__item__img {
  position: relative;
}

.product__item__img .overlay {
  position: absolute;
  top: 12px;
  right: 12px;
}

.product__item__img .overlay .product__wishlist {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: #FAFBFC;
  -webkit-box-shadow: 0px 4px 10px rgba(165, 170, 180, 0.15);
          box-shadow: 0px 4px 10px rgba(165, 170, 180, 0.15);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.product__item__img .overlay .product__wishlist img {
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.product__item__img .overlay .product__wishlist:hover img, .product__item__img .overlay .product__wishlist.active img {
  -webkit-filter: invert(49%) sepia(73%) saturate(558%) hue-rotate(313deg) brightness(105%) contrast(111%);
          filter: invert(49%) sepia(73%) saturate(558%) hue-rotate(313deg) brightness(105%) contrast(111%);
}

.product__item .product__item__info .product__title {
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  color: #273041;
}

.product__item .product__item__info .product__desc {
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.product__item .product__item__info .product__meta {
  margin-top: 12px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

.product__item .product__item__info .product__rattings {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.product__item .product__item__info .product__rattings li i {
  color: #FFBF00;
}

.product__item .product__item__info .product__review {
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.product__item .product__item__info .product__price {
  padding: 9px 12px 8px;
  background: #FBF5FF;
  border-radius: 4px;
  font-weight: 600;
  font-size: 16px;
  line-height: 19px;
  text-align: center;
  color: #BE63F9;
}

.product__item.list-type {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.product__item.list-type .product__item__left {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 239px;
          flex: 0 0 239px;
  margin-right: 16px;
}

.product__item.list-type .product__item__info {
  width: calc(100% - 255px);
}

.product__item.list-type .product__item__img {
  width: 239px;
  height: 173px;
  margin-bottom: 0;
}

.product__item.list-type .product__meta {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.product__item.list-type .product__meta__right {
  text-align: right;
}

.product__item.list-type .product__meta__left .item-top {
  margin-bottom: 10px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.product__item.list-type .product__meta__left .item-top .product__review {
  margin-bottom: 0;
}

.product__item.list-type .product__price {
  padding: 0;
  background-color: transparent;
}

.product__item.list-type .product__price del {
  margin: 0 10px;
  font-weight: 500;
  font-size: 14px;
  line-height: 17px;
  color: #B6B9BF;
}

.product__item.list-type .product__price span {
  font-weight: 400;
  font-size: 12px;
  line-height: 15px;
  color: #BE63F9;
}

@media screen and (max-width: 575px) {
  .product__item.list-type {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
  .product__item.list-type .product__item__left {
    width: 100%;
    margin-right: 0;
  }
  .product__item.list-type .product__item__info {
    width: 100%;
    padding: 20px;
  }
  .product__item.list-type .product__item__img {
    width: 100%;
    height: 173px;
    margin-bottom: 0;
  }
  .product__item.list-type .product__meta {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-align: start;
        -ms-flex-align: start;
            align-items: flex-start;
  }
  .product__item.list-type .product__meta__right {
    margin-top: 10px;
  }
}

@media screen and (max-width: 480px) {
  .product__item.list-type .product__meta {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-align: start;
        -ms-flex-align: start;
            align-items: flex-start;
  }
  .product__item.list-type .product__meta__right {
    margin-top: 10px;
  }
}

.sidebar__widget__area {
  height: calc(100% - 30px);
  padding: 20px 23px 20px;
  margin-bottom: 30px;
}

.widget__item .widget__title {
  margin-bottom: 9px;
}

.widget__item .widget__title h2 {
  font-weight: 600;
  font-size: 14px;
  line-height: 24px;
  color: #273041;
}

.slider {
  height: 4px;
}

.slider .slider__bar,
.slider .slider__handle {
  background-color: #5991FF;
}

.slider--horizontal .slider__bar {
  width: 10%;
  height: 4px;
}

.slider--horizontal .slider__handle {
  position: absolute;
  z-index: 1;
  cursor: pointer;
  top: 33px;
  margin-left: 0px;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  -webkit-transform: translate(-50%, 50%);
          transform: translate(-50%, 50%);
  background-color: transparent;
  background-color: #5991FF;
}

.slider .slider__tip {
  padding: 3px 8px;
  background-color: #5991FF;
  border-radius: 4px;
  font-weight: 500;
  font-size: 11px;
  line-height: 13px;
  color: #ffffff;
}

.slider .slider__tongue {
  border-bottom: 14px solid #5991FF;
}

.scale__value {
  font-weight: 400;
  font-size: 11px;
  line-height: 13px;
  color: #596680;
}

.widget__list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

.widget__list li {
  padding: 9px 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.widget__list li input {
  margin-right: 12px;
}

.widget__list .list__item {
  width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.widget__list .list__item:hover {
  opacity: 0.7;
}

.widget__item .list__item .item__rating {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  font-size: 15px;
  color: #FFBF00;
}

.widget__item .list__item .item__rating li {
  padding: 0;
}

.product-details__area {
  padding: 36px 30px 30px;
  margin-bottom: 40px;
}

.product-details__slider__for .swiper-slide {
  background: #F8F9FA;
  border-radius: 10px;
  width: 100%;
  height: auto;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

.product-details__slider__for .swiper-slide .slider__img {
  width: 100%;
  height: 355px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

.product-details__slider__for .swiper-slide .slider__img img {
  width: 348px;
  height: 355px;
  margin: 0 auto;
}

@media screen and (max-width: 480px) {
  .product-details__slider__for .swiper-slide .slider__img {
    height: auto;
  }
  .product-details__slider__for .swiper-slide .slider__img img {
    width: 100%;
    height: auto;
  }
}

.product-details__slider__nav {
  margin-top: 10px;
}

.product-details__slider__nav .swiper-slide {
  width: 113px;
  height: 113px;
  background: #F8F9FA;
  border-radius: 10px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

.product-details__slider__nav .swiper-slide .slider__img {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

.product-details__slider__nav .swiper-slide .slider__img img {
  width: 80px;
  height: 80px;
}

.product-details__slider__nav .swiper-slide.swiper-slide-thumb-active {
  background: #F8F9FA;
  border: 1px solid #5991FF;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  border-radius: 10px;
}

.product-details__info .product-title {
  margin-bottom: 6px;
  font-weight: 600;
  font-size: 24px;
  line-height: 32px;
  color: #273041;
}

.product-details__info .info-top {
  margin-bottom: 20px;
}

.product-details__info .product-brand {
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.product-details__info .info-text {
  margin-bottom: 0;
  text-transform: capitalize;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.product-details__info .info-text span {
  margin-left: 15px;
  font-weight: 600;
}

.product-details__info .product-offer {
  margin-bottom: 0;
  font-weight: 500;
  font-size: 12px;
  line-height: 15px;
  color: #BE63F9;
}

.product-details__info .product-price {
  font-weight: 600;
  font-size: 18px;
  line-height: 24px;
  color: #273041;
}

.product-details__info .product-price span {
  margin-left: 5px;
}

.product-details__info .product-price del {
  margin-left: 10px;
  font-weight: 500;
  font-size: 15px;
  line-height: 18px;
  color: #B6B9BF;
}

.product-details__info .product-desc {
  margin: 16px 0;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.product-status {
  margin-top: 20px;
}

.product-status li .info-text {
  margin-bottom: 3px;
}

.add-count {
  margin-top: 8px;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.add-count span {
  margin-right: 10px;
}

.add-count a {
  width: 35px;
  height: 35px;
  background: #FFFFFF;
  border: 1px solid #E9EAED;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  border-radius: 6px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.add-count input {
  margin: 0 10px;
  text-align: center;
  border: 0;
  width: 35px;
  height: 35px;
  background: #F8F9FA;
  border-radius: 6px;
}

.product-details__list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin-left: -40px;
}

.product-details__list li {
  margin: 6px 0 0 40px;
}

.product-details__list li .item img {
  width: 14px;
  height: 16px;
}

.product-details__list li .item span {
  margin-left: 13px;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.product-details__info .product__meta {
  margin-bottom: 8px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.product-details__info .product__meta h2 {
  margin-left: 8px;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #273041;
}

.product-details__info .product__meta h2 span {
  margin-left: 12px;
  color: #596680;
}

.product-details__info .product__meta .product__rattings {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.product-details__info .product__meta .product__rattings li:not(:first-child) {
  margin-left: 3px;
}

.product-details__info .product__meta .product__rattings li i {
  color: #FFBF00;
}

.product-details__button {
  margin-top: 30px;
  margin-left: -15px;
}

.product-details__button a {
  margin-left: 15px;
}

@media screen and (max-width: 480px) {
  .product-details__button {
    margin-top: 15px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
  }
  .product-details__button a {
    margin-top: 15px;
  }
}

.product-details__tabs {
  margin-top: 35px;
}

.nav-tabs {
  border-bottom: 0px;
}

.product-details__tabs .nav-link {
  padding: 5px 28px 7px !important;
  background-color: #FAFBFC;
  border: 1px solid #EBEDF0;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  border-radius: 8px 8px 0px 0px;
  text-transform: capitalize;
  font-weight: 400;
  font-size: 16px;
  line-height: 28px;
  color: #596680;
}

.product-details__tabs .nav-link:not(:first-child) {
  margin-left: 2px;
}

.product-details__tabs .nav-link.active {
  background-color: #5991FF;
  border: 1px solid #5991FF;
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  color: #ffffff;
}

.product-details__tabs .tab-content {
  background: #FAFBFC;
  border: 1px solid #EBEDF0;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  border-radius: 0px 10px 10px 10px;
  padding: 25px;
}

@media screen and (max-width: 575px) {
  .product-details__tabs .tab-content {
    padding: 20px;
  }
}

.product-details__review__list > li:not(:first-child) {
  margin-top: 26px;
}

.product-details__review__list > li:not(:last-child) {
  position: relative;
  padding-bottom: 26px;
}

.product-details__review__list > li:not(:last-child)::before {
  position: absolute;
  content: "";
  bottom: 0;
  left: 78px;
  width: calc(100% - 78px);
  height: 1px;
  background-color: #E4E5EB;
}

.product-details__review__list > li > ul {
  margin-top: 16px;
  padding-left: 78px;
}

.product-details__review__list > li > ul > li:not(:first-child) {
  margin-top: 26px;
}

@media screen and (max-width: 575px) {
  .product-details__review__list > li:not(:last-child)::before {
    left: 58px;
    width: calc(100% - 58px);
  }
  .product-details__review__list > li > ul {
    padding-left: 58px;
  }
}

@media screen and (max-width: 480px) {
  .product-details__review__list > li:not(:last-child)::before {
    left: 20px;
    width: calc(100% - 20px);
  }
  .product-details__review__list > li > ul {
    padding-left: 20px;
  }
}

.product-details__review {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

@media screen and (max-width: 480px) {
  .product-details__review {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
}

.product-details__review .review__left {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 40px;
          flex: 0 0 40px;
  margin-right: 40px;
}

.product-details__review .review__left .review__img img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}

.product-details__review .review__right .rattings {
  margin-bottom: 8px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.product-details__review .review__right .rattings li:not(:first-child) {
  margin-left: 3px;
}

.product-details__review .review__right .rattings li i {
  color: #FFBF00;
}

@media screen and (max-width: 575px) {
  .product-details__review .review__left {
    margin-right: 20px;
  }
}

.review__right h2 {
  font-weight: 600;
  font-size: 14px;
  line-height: 24px;
  color: #273041;
  margin-bottom: 2px;
}

.review__right p {
  margin-bottom: 0;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.review-date span {
  margin-left: 10px;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  color: #8E9AB2;
}

.review-meta {
  margin-top: 15px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.review-meta li:not(:first-child) {
  margin-left: 30px;
}

.review-meta li a {
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  color: #596680;
}

.review-meta li a span {
  margin-left: 10px;
}

.add-product__area {
  padding: 30px 30px 45px;
  margin-bottom: 40px;
}

.add-product__title {
  background: #F5F7FA;
  border-radius: 10px;
  padding: 10px 16px 11px;
  margin-bottom: 30px;
}

.add-product__title h2 {
  font-weight: 600;
  font-size: 18px;
  line-height: 24px;
  color: #273041;
}

.add-product__area .input__group {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}

.add-product__area .input__group label {
  margin-bottom: 11px;
  text-transform: capitalize;
  font-weight: 500;
  font-size: 14px;
  line-height: 17px;
  color: #273041;
}

.add-product__area .input__group input,
.add-product__area .input__group select,
.add-product__area .input__group textarea {
  width: 100%;
  background: #FAFBFC;
  border: 1px solid #DFE3EB;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  border-radius: 8px;
  padding: 11px 16px 12px;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.add-product__area .input__group textarea {
  resize: none;
  height: 132px;
}

.add-product__area .input__group__between {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.add-product__area .input__group__between .input__group input {
  width: 0;
}

.add-product__area .input__group__between .input-group {
  -webkit-box-flex: 48%;
      -ms-flex: 48%;
          flex: 48%;
}

.add-product__area .input__group__between .input-group input:focus {
  -webkit-box-shadow: none;
          box-shadow: none;
}

.add-product__area .input__group__between .input-group-text {
  background: #F5F7FA;
  border-radius: 7px 0px 0px 7px;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.upload-img {
  background: #FAFBFC;
  border: 1px solid #DFE3EB;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  border-radius: 8px;
  padding: 90px 30px;
  text-align: center;
}

.upload-img input {
  display: none;
}

.upload-img img {
  width: 40px;
  height: 40px;
  margin-bottom: 40px;
}

.upload-img h2 {
  font-weight: 600;
  font-size: 18px;
  line-height: 22px;
  color: #273041;
  margin-bottom: 13px;
}

.upload-img p {
  margin-bottom: 0;
  font-weight: normal;
  font-size: 15px;
  line-height: 18px;
  color: #596680;
}

.upload-img p label {
  color: #5991FF;
}

@media screen and (max-width: 767px) {
  .product-list {
    margin-top: 20px;
  }
}

.product-list ul li:not(:first-child) {
  margin-top: 15px;
}

.product-list ul .list__item__left {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.product-list ul .list__item {
  width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.product-list ul .list__item__img {
  width: 48px;
  height: 48px;
  margin-right: 15px;
}

.product-list ul .list__item__info h2 {
  font-weight: 500;
  font-size: 14px;
  line-height: 17px;
  color: #273041;
  margin-bottom: 7px;
}

.product-list ul .list__item__info h3 {
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.product-list ul .list__item__right a {
  background: #F5F7FA;
  border-radius: 8px;
}

.order-details__area {
  padding: 20px 25px;
  margin-bottom: 30px;
  height: calc(100% - 30px);
}

.order-details__title {
  margin-bottom: 26px;
}

.order-details__title h2 {
  font-weight: 500;
  font-size: 18px;
  line-height: 22px;
  color: #273041;
}

.order-details__table {
  width: 100%;
  overflow-x: auto;
}

.order-details__table table {
  width: 100%;
}

.order-details__table table tbody tr:nth-child(even) {
  background-color: #FAFBFC;
}

.order-details__table table thead th {
  padding: 14px 15px;
}

.order-details__table table tbody td {
  padding: 10px 15px;
}

.order-details__table table tbody td:nth-child(1) {
  min-width: 300px;
}

.order-details__table table tbody td:nth-child(2) {
  min-width: 120px;
}

.order-details__table table tbody td:nth-child(3) {
  min-width: 120px;
}

.order-details__table table tbody td:nth-child(4) {
  min-width: 120px;
}

.order-details__table table .product-info {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.order-details__table table .product-info .product__img {
  margin-right: 14px;
}

.order-details__table table .product-info .product__img img {
  width: 30px;
  height: 30px;
  background: #C4C4C4;
  border-radius: 4px;
}

.order-details__table table .product-info h2 {
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.order-details__cal {
  margin-top: 10px;
  text-align: right;
}

.order-details__cal ul li:not(:first-child) {
  margin-top: 18px;
}

.order-details__cal .list-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
}

.order-details__cal .list-item h3 {
  margin-right: 10px;
  font-weight: 500;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
  text-align: right;
}

.order-details__cal .list-item h2 {
  min-width: 70px;
  text-align: left;
  font-size: 14px;
  line-height: 17px;
  font-weight: 400;
  color: #596680;
}

.track-order__area {
  padding: 20px 25px;
  margin-bottom: 30px;
  height: calc(100% - 30px);
}

.track-order__title {
  margin-bottom: 27px;
}

.track-order__title h2 {
  text-transform: capitalize;
  font-weight: 500;
  font-size: 18px;
  line-height: 22px;
  color: #273041;
}

.tracking__id {
  margin-bottom: 27px;
}

.tracking__id h3 {
  text-transform: capitalize;
  font-weight: 500;
  font-size: 14px;
  line-height: 17px;
  color: #273041;
  margin-bottom: 7px;
}

.tracking__id h2 {
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.track-order__status ul li {
  position: relative;
  padding-left: 47px;
}

.track-order__status ul li::before {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  width: 17px;
  height: 17px;
  border-radius: 50%;
  background: #FFFFFF;
  border: 5px double #5991FF;
}

.track-order__status ul li::after {
  position: absolute;
  content: "";
  top: 17px;
  left: 8px;
  width: 3px;
  height: calc(100% + 30px);
  background: #5991FF;
  border-radius: 9px;
}

.track-order__status ul li:nth-child(3)::after {
  background: #EBEDF2;
}

.track-order__status ul li:last-child::before {
  display: none;
}

.track-order__status ul li:last-child::after {
  height: calc(100% - 17px);
  background: #EBEDF2;
}

.track-order__status ul li:not(:first-child) {
  margin-top: 30px;
}

.track-order__status ul .list-item h2 {
  text-transform: capitalize;
  font-weight: 500;
  font-size: 14px;
  line-height: 17px;
  color: #273041;
  margin-bottom: 7px;
}

.track-order__status ul .list-item h3 {
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.order-details__info {
  padding: 24px 25px;
  margin-bottom: 30px;
  height: calc(100% - 30px);
}

.order-details__info .info__title {
  margin-bottom: 22px;
}

.order-details__info .info__title h2 {
  text-transform: capitalize;
  font-weight: 500;
  font-size: 18px;
  line-height: 22px;
  color: #273041;
}

.order-details__info .info__user {
  margin-bottom: 18px;
}

.order-details__info .info__user h2 {
  font-weight: 500;
  font-size: 14px;
  line-height: 17px;
  color: #273041;
}

.order-details__info .delivery__info img {
  width: 30px;
  height: 30px;
}

.order-details__info .delivery__info h2 {
  margin: 15px 0 15px 0;
  font-weight: 500;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.order-details__info .info__table {
  width: 100%;
  overflow-x: auto;
}

.order-details__info .info__table table {
  width: 100%;
}

.order-details__info .info__table table tbody th {
  min-width: 150px;
  text-transform: capitalize;
  font-weight: 500;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.order-details__info .info__table table tbody td {
  min-width: 200px;
  margin-left: 10px;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.order-details__info .info__table table tbody th,
.order-details__info .info__table table tbody td {
  padding-bottom: 15px;
}

.cart__area {
  padding: 25px;
  margin-bottom: 30px;
  height: calc(100% - 30px);
}

.cart__table {
  width: 100%;
  overflow-x: auto;
}

.cart__table table {
  width: 100%;
}

.cart__table table thead th {
  padding: 14px 15px;
}

.cart__table table tbody td {
  padding: 15px;
  border-bottom: 1px solid #ECEDF0;
}

.cart__table table tbody td:nth-child(1) {
  min-width: 350px;
}

.cart__table table tbody td:nth-child(2) {
  min-width: 150px;
}

.cart__table table tbody td:nth-child(3) {
  min-width: 150px;
}

.cart__table table tbody td:nth-child(4) {
  min-width: 150px;
}

.cart__table table .total, .cart__table table .price {
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.cart__table table .product-info {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.cart__table table .product-info__left img {
  width: 70px;
  height: 70px;
}

.cart__table table .product-info__right {
  margin-left: 20px;
}

.cart__table table .product-info__right h2 {
  font-weight: 600;
  font-size: 16px;
  line-height: 19px;
  color: #273041;
  margin-bottom: 8px;
}

.cart__table table .product-info__right ul {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

.cart__table table .product-info__right ul li:not(:first-child) {
  margin-left: 20px;
}

.cart__table table .product-info__right ul li p {
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #273041;
}

.cart__table table .product-info__right ul li p span {
  color: #596680;
}

.quantity-btn {
  border: 1px solid #E9EAED;
  border-radius: 0;
  background-color: transparent;
  width: 35px;
  height: 35px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.quantity-btn:focus {
  -webkit-box-shadow: none;
          box-shadow: none;
  border: 1px solid #E9EAED;
}

.order-summary__area {
  padding: 20px 25px;
  margin-bottom: 30px;
  height: calc(100% - 30px);
}

.order-summary__area h2 {
  text-transform: capitalize;
  font-weight: 500;
  font-size: 18px;
  line-height: 22px;
  color: #273041;
}

.total {
  margin-top: 30px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.total p {
  margin-bottom: 0;
  text-transform: capitalize;
  font-weight: 500;
  font-size: 14px;
  line-height: 17px;
  color: #273041;
}

.total h2 {
  font-weight: 600;
  font-size: 18px;
  line-height: 22px;
  color: #BE63F9;
}

.order-summary__list {
  margin-top: 30px;
}

.order-summary__list li:not(:first-child) {
  margin-top: 15px;
}

.order-summary__list .list-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.order-summary__list .list-item p {
  text-transform: capitalize;
  font-weight: 500;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.order-summary__list .list-item h2 {
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.promo-code {
  margin-top: 45px;
}

.promo-code h2 {
  margin-bottom: 11px;
  font-weight: 500;
  font-size: 14px;
  line-height: 17px;
  color: #273041;
}

.promo-code__input {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  width: 100%;
}

.promo-code__input input {
  width: 70%;
  margin-right: 10px;
  background: #FAFBFC;
  border: 1px solid #DFE3EB;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  border-radius: 8px;
  padding: 11px 16px 12px;
}

.promo-code__input a {
  width: 90px;
  background: #FFFFFF;
  border: 1px solid #BE63F9;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  border-radius: 8px;
  padding: 11px 16px 12px;
  color: purple;
}

.order-summary__button {
  margin-top: 16px;
}

.checkout__area {
  padding: 20px 25px;
  margin-bottom: 30px;
}

.checkout__area .tab-content {
  margin-top: 30px;
}

.checkout__area .nav.nav-tabs {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.checkout__area .nav.nav-tabs .nav-link {
  width: calc(100% / 3);
}

.checkout__area .nav-link {
  padding: 15px !important;
  border: 0;
  border-bottom: 2px solid #EBEDF2;
}

.checkout__area .nav-link img {
  margin-right: 10px;
  width: 20px;
  height: 20px;
}

.checkout__area .nav-link span {
  text-transform: capitalize;
  font-weight: 500;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.checkout__area .nav-link.active {
  color: #5991FF;
  border-bottom: 2px solid #5991FF;
}

.checkout__area .nav-link.active span {
  color: #5991FF;
}

.checkout__area .nav-link.active img {
  -webkit-filter: invert(49%) sepia(93%) saturate(2125%) hue-rotate(202deg) brightness(104%) contrast(101%);
          filter: invert(49%) sepia(93%) saturate(2125%) hue-rotate(202deg) brightness(104%) contrast(101%);
}

@media screen and (max-width: 575px) {
  .checkout__area .nav-link {
    padding: 10px !important;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
  }
  .checkout__area .nav-link img {
    margin-right: 0;
    margin-bottom: 10px;
  }
}

.input__group {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}

.input__group label {
  margin-bottom: 11px;
  text-transform: capitalize;
  font-weight: 500;
  font-size: 14px;
  line-height: 17px;
  color: #273041;
}

.input__group input,
.input__group textarea {
  background: #FAFBFC;
  border: 1px solid #DFE3EB;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  border-radius: 8px;
  padding: 11px 16px 12px;
  font-weight: normal;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.input__group textarea {
  resize: none;
}

.checkout__buttons {
  margin-top: 40px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.checkout__buttons a {
  width: 160px;
  border-radius: 10px;
}

@media screen and (max-width: 480px) {
  .checkout__buttons {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    margin-top: 20px;
  }
  .checkout__buttons a {
    width: 100%;
    margin-top: 20px;
  }
}

.checkout-order__area {
  padding: 24px 25px 27px;
  margin-bottom: 30px;
}

.checkout-order__area h2 {
  text-transform: capitalize;
  font-weight: 500;
  font-size: 18px;
  line-height: 22px;
  color: #273041;
}

.checkout-order__list {
  margin-top: 30px;
}

.checkout-order__list li {
  padding-bottom: 18px;
  border-bottom: 1px solid #ECEDF0;
}

.checkout-order__list li:not(:first-child) {
  margin-top: 18px;
}

.checkout-order__list .list-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.checkout-order__list .product-info {
  width: 80%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.checkout-order__list .product-info .product-img {
  margin-right: 15px;
}

.checkout-order__list .product-info .product-img img {
  width: 50px;
  height: 50px;
}

.checkout-order__list .product-info .product-text h2 {
  font-weight: 500;
  font-size: 15px;
  line-height: 18px;
  color: #273041;
  margin-bottom: 7px;
}

.checkout-order__list .product-info .product-text p {
  margin-bottom: 0;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.checkout-order__list .total-price h2 {
  font-weight: normal;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.shipping__info .shipping__title h2 {
  margin-bottom: 20px;
  text-transform: capitalize;
  font-weight: 500;
  font-size: 16px;
  line-height: 19px;
  color: #273041;
}

.shipping__info .address__item {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #ffffff;
  border: 1px solid #DFE3EB;
  border-radius: 10px;
}

.shipping__info .address__item.active {
  border: 1px solid #5991FF;
}

.address__item__between {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.address__item__between .address__item {
  width: 48%;
  margin-bottom: 30px;
}

@media screen and (max-width: 991px) {
  .address__item__between {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
  .address__item__between .address__item {
    width: 100%;
  }
}

.address__item {
  position: relative;
}

.address__item .overlay {
  position: absolute;
  top: 20px;
  right: 20px;
}

.address__item h2 {
  margin-top: 20px;
  margin-bottom: 10px;
  font-weight: 400;
  font-size: 15px;
  line-height: 18px;
  color: #273041;
}

.address__item .address__input label {
  margin-left: 10px;
  text-transform: capitalize;
  font-weight: 500;
  font-size: 15px;
  line-height: 18px;
  color: #273041;
}

.address__item .address__input p {
  margin: 10px 0 0 28px;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.address__item__list li:not(:first-child) {
  margin-top: 15px;
}

.address__item__list .list-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
}

.address__item__list .list-item h2 {
  margin: 0 10px 0 0;
  min-width: 70px;
  text-transform: capitalize;
  font-weight: 500;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.address__item__list .list-item h3 {
  word-wrap: anywhere;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  color: #596680;
}

.checkout-order__cal {
  margin-top: 13px;
}

.checkout-order__cal li:not(:first-child) {
  margin-top: 15px;
}

.checkout-order__cal .list-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
}

.checkout-order__cal .list-item h2, .checkout-order__cal .list-item h3 {
  text-align: right;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.checkout-order__cal .list-item h2 {
  text-transform: capitalize;
  font-weight: 500;
}

.checkout-order__cal .list-item h3 {
  min-width: 85px;
  margin-left: 5px;
  font-weight: 400;
}

.email__sidebar {
  padding: 24px 25px;
  margin-bottom: 30px;
  height: calc(100% - 30px);
}

.email__sidebar .sidebar__item h2 {
  margin-bottom: 15px;
  font-weight: 500;
  font-size: 14px;
  line-height: 17px;
  color: #273041;
}

.email__sidebar .sidebar__item:not(:first-child) {
  margin-top: 30px;
}

.sidebar__item .sidebar__mail__nav li {
  position: relative;
}

.sidebar__item .sidebar__mail__nav li:not(:first-child) {
  margin-top: 15px;
}

.sidebar__item .sidebar__mail__nav .overlay {
  position: absolute;
  top: 0;
  right: 0;
}

.sidebar__item .sidebar__mail__nav .list-item {
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.sidebar__item .sidebar__mail__nav .list-item img {
  width: 20px;
  height: 20px;
  margin-right: 12px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.sidebar__item .sidebar__mail__nav .list-item span {
  font-style: normal;
  font-weight: normal;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.sidebar__item .sidebar__mail__nav .list-item:hover span, .sidebar__item .sidebar__mail__nav .list-item.active span {
  color: #BE63F9;
}

.email__sidebar .sidebar__list li:not(:first-child) {
  margin-top: 20px;
}

.email__sidebar .sidebar__list .list-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.email__sidebar .sidebar__list li span {
  margin-right: 16px;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.email__sidebar .sidebar__list li a {
  font-style: normal;
  font-weight: normal;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.email-inbox__area {
  padding: 24px 25px;
  margin-bottom: 30px;
  height: calc(100% - 30px);
}

@media screen and (max-width: 575px) {
  .email-inbox__area .orders__top {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-align: start;
        -ms-flex-align: start;
            align-items: flex-start;
  }
  .email-inbox__area .orders__top__left {
    -webkit-box-ordinal-group: 3;
        -ms-flex-order: 2;
            order: 2;
  }
}

.emai-inbox__table {
  width: 100%;
  overflow-x: auto;
}

.emai-inbox__table table {
  width: 100%;
}

.emai-inbox__table table tbody td {
  padding: 15px 10px;
  border-bottom: 1px solid #ECEDF0;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.emai-inbox__table table tbody td:nth-child(1) {
  min-width: 30px;
}

.emai-inbox__table table tbody td:nth-child(2) {
  min-width: 30px;
}

.emai-inbox__table table tbody td:nth-child(3) {
  min-width: 200px;
}

.emai-inbox__table table tbody td:nth-child(4) {
  min-width: 300px;
}

.emai-inbox__table table tbody td:nth-child(5) {
  min-width: 100px;
}

.emai-inbox__table table .user-info {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.emai-inbox__table table .user-info img {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  margin-right: 15px;
}

.email-inbox__list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.email-inbox__list li:not(:first-child) {
  margin-left: 10px;
}

.email-inbox__list li a {
  font-weight: 400;
}

.email-inbox__list > li button,
.email-inbox__list > li > a {
  background: transparent;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

.email-inbox__list .dropdown li:not(:first-child) {
  margin-left: 0px;
}

.email-inbox__list .dropdown li a {
  text-transform: initial;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.email-inbox__list .dropdown li a img {
  width: 16px;
  height: 16px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.email-inbox__list .dropdown li a span {
  margin-left: 10px;
}

.email-inbox__list .dropdown li a:hover {
  background: rgba(89, 145, 255, 0.1);
  color: #2980b9;
}

.email-inbox__list .dropdown li a:hover img {
  -webkit-filter: invert(59%) sepia(83%) saturate(3011%) hue-rotate(198deg) brightness(99%) contrast(105%);
          filter: invert(59%) sepia(83%) saturate(3011%) hue-rotate(198deg) brightness(99%) contrast(105%);
}

.read-email__area {
  padding: 24px 0px;
  margin-bottom: 30px;
  height: calc(100% - 30px);
}

.read-email__body {
  border-top: 1px solid #ECEDF0;
  padding: 20px 24px;
}

.read-email__top {
  margin-bottom: 20px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

@media screen and (max-width: 575px) {
  .read-email__top__right {
    display: none;
  }
}

.read-eamil__subject {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.read-eamil__subject h2 {
  text-transform: capitalize;
  font-weight: 500;
  font-size: 20px;
  line-height: 24px;
  color: #273041;
}

.read-eamil__subject span {
  margin-left: 15px;
}

.badge {
  padding: 2px 5px 3px;
  border-radius: 23px;
  text-transform: capitalize;
  font-weight: 600;
  font-size: 10px;
  line-height: 12px;
  text-align: center;
}

.badge.bg-purple {
  background-color: #F0DAFF;
  color: #BE63F9 !important;
}

.badge.bg-red {
  background-color: #FFDADA;
  color: #F96363 !important;
}

.read-eamil__info {
  margin-bottom: 26px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

@media screen and (max-width: 575px) {
  .read-eamil__info {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-align: start;
        -ms-flex-align: start;
            align-items: flex-start;
  }
  .read-eamil__info .info__left {
    margin-bottom: 20px;
  }
}

.read-eamil__info .user__info {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.read-eamil__info .user__info .user__img {
  margin-right: 15px;
}

.read-eamil__info .user__info .user__img img {
  width: 45px;
  height: 45px;
  border-radius: 50%;
}

.read-eamil__info .user__info .user__right h2 {
  margin-bottom: 4px;
  font-weight: 500;
  font-size: 15px;
  line-height: 18px;
  color: #273041;
}

.read-eamil__info .user__right .dropdown .dropdown-toggle {
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #8E9AB2;
}

.read-eamil__info .user__right .dropdown ul li:not(:first-child) {
  margin-top: 5px;
}

.read-eamil__info .user__right .dropdown .dropdown-item {
  text-transform: capitalize;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #8E9AB2;
}

.read-eamil__info .user__right .dropdown .dropdown-item span {
  margin-left: 5px;
  text-transform: inherit;
  font-weight: 500;
  font-size: 15px;
  line-height: 18px;
  color: #273041;
}

.read-eamil__info .user__right .dropdown .dropdown-item:hover {
  background-color: transparent;
}

.read-eamil__info .info__right .info__list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.read-eamil__info .info__right .info__list li:not(:first-child) {
  margin-left: 20px;
}

.read-eamil__info .info__right .info__list .info-date {
  font-weight: 400;
  font-size: 13px;
  line-height: 16px;
  color: #8E9AB2;
}

.read-email__text {
  border-bottom: 1px solid #ECEDF0;
}

.read-email__text p {
  margin-bottom: 20px;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.attachment__area {
  margin-top: 20px;
}

.attachment__title {
  text-transform: capitalize;
  font-weight: 500;
  font-size: 16px;
  line-height: 19px;
  color: #273041;
}

.attachment__list {
  margin-top: 16px;
  display: -ms-grid;
  display: grid;
  -ms-grid-columns: auto auto auto;
      grid-template-columns: auto auto auto;
  grid-column-gap: 15px;
  grid-row-gap: 15px;
}

@media screen and (max-width: 767px) {
  .attachment__list {
    -ms-grid-columns: auto;
        grid-template-columns: auto;
  }
}

.attachment__list .list-item {
  background: #FAFBFC;
  border: 1px solid #EBEDF0;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  border-radius: 10px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding: 15px;
}

.attachment__list .list-item__left img {
  width: 30px;
  height: 40px;
}

.attachment__list .list-item__right {
  margin-left: 12px;
}

.attachment__list .list-item__right h2 {
  font-weight: 500;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.attachment__list .list-item__right p {
  margin-bottom: 0;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #8E9AB2;
}

.read-email__button {
  margin-top: 60px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.read-email__button a {
  width: 120px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

.read-email__button a:not(:first-child) {
  margin-left: 15px;
}

.read-email__area__top {
  padding: 0px 24px;
}

.email-templates__area {
  padding: 36px 30px 30px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  margin-bottom: 30px;
}

.email-templates__item {
  margin-bottom: 30px;
}

.email-templates__item.email-templates__alert .templates__content {
  padding: 0;
}

.email-templates__item.email-templates__alert .templates__content__body {
  padding: 0 25px 30px;
}

.email-templates__item.email-templates__alert .templates__top {
  margin-bottom: 32px;
  background-color: #F96363;
}

.email-templates__item.email-templates__alert .templates__top img {
  -webkit-filter: brightness(0) invert(1);
          filter: brightness(0) invert(1);
}

.email-templates__item.email-templates__alert .templates__top h2 {
  margin-top: 14px;
  font-weight: 500;
  font-size: 14px;
  line-height: 17px;
  color: #FFFFFF;
}

.templates__title {
  margin-bottom: 20px;
}

.templates__title h2 {
  font-weight: 600;
  font-size: 20px;
  line-height: 24px;
  color: #273041;
}

.templates__content {
  background-color: red;
  width: 430px;
  background: #ffffff;
  border: 1px solid #E4E6EB;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  -webkit-box-shadow: 0px 10px 25px rgba(163, 177, 204, 0.1);
          box-shadow: 0px 10px 25px rgba(163, 177, 204, 0.1);
  border-radius: 12px;
  overflow: hidden;
  text-align: center;
  padding: 20px 25px;
}

@media screen and (max-width: 480px) {
  .templates__content {
    width: 100%;
  }
}

.templates__top {
  padding: 30px 0;
}

.templates__text h3 {
  margin-bottom: 20px;
  font-weight: 400;
  font-size: 16px;
  line-height: 19px;
  color: #596680;
}

.templates__text h3 span {
  font-weight: 600;
  color: #F96363;
}

.templates__text h2 {
  margin-bottom: 17px;
  font-weight: 600;
  font-size: 25px;
  line-height: 30px;
  text-align: center;
  color: #273041;
}

.templates__text p {
  margin-bottom: 0;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.templates__button {
  margin-top: 23px;
  margin-bottom: 28px;
}

.templates__from {
  margin-bottom: 18px;
}

.templates__from h2 {
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.templates__bottom {
  border-top: 1px solid #DCDFE6;
  padding-top: 14px;
}

.templates__bottom h2 {
  font-weight: 400;
  font-size: 13px;
  line-height: 22px;
  color: #596680;
}

.templates__bottom h2 a {
  color: #5991FF;
}

.templates__billing {
  margin-bottom: 30px;
}

.templates__billing h2 {
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  color: #273041;
}

.templates__billing p {
  margin-bottom: 24px;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.templates__billing h3 {
  text-transform: uppercase;
  font-weight: 600;
  font-size: 20px;
  line-height: 24px;
  color: #273041;
}

.templates__table {
  margin-bottom: 53px;
  width: 100%;
  overflow-x: auto;
}

.templates__table table {
  width: 100%;
}

.templates__table table thead th:nth-child(1),
.templates__table table tbody td:nth-child(1) {
  text-align: left;
}

.templates__table table thead th:nth-child(2),
.templates__table table tbody td:nth-child(2) {
  text-align: right;
}

.templates__table table thead th {
  padding: 11px 16px 10px;
  font-weight: 500;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.templates__table table tbody td {
  padding: 5px 16px 11px;
}

.templates__table table tbody td:nth-child(1) {
  padding-top: 11px;
  min-width: 200px;
}

@media screen and (max-width: 480px) {
  .templates__table table tbody td:nth-child(1) {
    min-width: unset;
  }
}

.templates__table table tfoot td {
  border-top: 1px solid #DCDFE6;
  text-align: right;
  padding: 10px 16px 11px;
}

.templates__table table .total-price {
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.templates__table table .total-price span {
  margin-left: 10px;
  font-weight: 600;
}

.compose-email__area {
  margin-bottom: 30px;
  height: calc(100% - 30px);
}

.compose-email__area__top {
  padding: 15px 25px;
}

.compose-email__area .orders__top__left,
.compose-email__area .orders__top__right {
  margin-bottom: 0;
}

.compose__bottom {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.compose__bottom button {
  padding: 11px 29px;
}

.compose__bottom .compose__attch__list {
  margin-left: 25px;
}

@media screen and (max-width: 480px) {
  .compose__bottom {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-align: start;
        -ms-flex-align: start;
            align-items: flex-start;
  }
  .compose__bottom button {
    -webkit-box-ordinal-group: 3;
        -ms-flex-order: 2;
            order: 2;
  }
  .compose__bottom .compose__attch__list {
    margin-left: 0px;
    margin-bottom: 15px;
  }
}

.compose__attch__list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.compose__attch__list li:not(:first-child) {
  margin-left: 16px;
}

.compose__attch__list li .input__group {
  margin-bottom: 0;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: transparent;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

.compose__attch__list li .input__group:hover {
  background: #F1F2F5;
}

.compose__attch__list .input__group label {
  margin-bottom: 0;
}

.compose__attch__list .input__group input[type='file'] {
  display: none;
}

.compose-email__close {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  background: #F1F2F5;
}

.compose-email__body {
  border-top: 1px solid #ECEDF0;
  padding: 25px 25px 25px;
}

.compose-email__body .input__group {
  margin-bottom: 20px;
}

.ck.ck-editor__main {
  background: #FAFBFC;
  border: 0;
}

.ck-rounded-corners .ck.ck-editor__top .ck-sticky-panel .ck-toolbar,
.ck.ck-editor__top .ck-sticky-panel .ck-toolbar.ck-rounded-corners {
  background: #EBEDF0;
  border: 1px solid transparent;
  border-radius: 10px 10px 0px 0px !important;
}

.ck.ck-content.ck-editor__editable.ck-rounded-corners.ck-editor__editable_inline.ck-blurred {
  background: #FAFBFC;
  border: 1px solid #DFE3EB;
  border-radius: 10px;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.project-grid__item {
  background: #FFFFFF;
  border: 1px solid #E4E6EB;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  -webkit-box-shadow: 0px 10px 25px rgba(163, 177, 204, 0.1);
          box-shadow: 0px 10px 25px rgba(163, 177, 204, 0.1);
  border-radius: 12px;
  margin-bottom: 30px;
  padding: 25px;
}

.project-grid__top {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.project-grid__top__left {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 60px;
          flex: 0 0 60px;
  margin-right: 15px;
}

.project-grid__top__left img {
  width: 60px;
  height: 60px;
  border-radius: 50%;
}

.project-grid__top__right h2 {
  margin-bottom: 3px;
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  color: #273041;
}

.project-grid__top__right p {
  margin-bottom: 0;
  font-weight: 500;
  font-size: 12px;
  line-height: 15px;
  color: #596680;
}

.project-grid__status {
  margin: 14px 0 19px;
}

.project-grid__status .badge {
  padding: 3px 5px;
  text-transform: uppercase;
  font-weight: 500;
  font-size: 12px;
  line-height: 15px;
  color: #FFFFFF;
  border-radius: 4px;
}

.project-grid__status .badge.bg-yellow {
  background: #FF9F38;
}

.project-grid__status .badge.bg-red {
  background: #FF6628;
  color: white !important;
}

.project-grid__status .badge.bg-green {
  background: #4CBF4C;
}

.project-grid__content p {
  margin-bottom: 0;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.project-grid__content > h2 {
  border-top: 1px solid #DCDFE5;
  padding-top: 12px;
  padding-bottom: 6px;
  font-weight: normal;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.project-grid__content .date-list {
  margin-top: 13px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-left: -35px;
  margin-bottom: 14px;
}

.project-grid__content .date-list li {
  margin-left: 35px;
}

.project-grid__content .date-list p {
  margin-bottom: 0;
  font-weight: normal;
  font-size: 12px;
  line-height: 20px;
  color: #596680;
}

.project-grid__content .date-list h2 {
  font-weight: 600;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.user__list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-left: 15px;
}

.user__list li {
  margin-left: -15px;
}

.user__list li a {
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.user__list li a img {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  border: 2px solid #ffffff;
}

.project-grid__bottom {
  margin-top: 20px;
}

.project-grid__bottom .task-text {
  margin-bottom: 7px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.project-grid__bottom .task-text h2 {
  text-transform: capitalize;
  font-weight: 500;
  font-size: 12px;
  line-height: 15px;
  color: #596680;
}

.project-grid__bottom .task-text p {
  margin-bottom: 0;
  font-weight: 500;
  font-size: 12px;
  line-height: 15px;
  color: #596680;
}

.project-grid__bottom .task-status {
  margin-top: 12px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-left: -20px;
}

.project-grid__bottom .task-status a {
  margin-left: 20px;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.project-grid__bottom .task-status a span b {
  font-weight: 600;
}

.project-grid__bottom .progress {
  background: #EBEDF0;
  border-radius: 15px;
  height: 10px;
}

.project-grid__bottom .progress-bar {
  background-color: #FF9F38;
  border-radius: 15px;
}

.project-grid__bottom .progress.bg-yellow {
  background: #EBEDF0;
  border-radius: 15px;
}

.project-grid__bottom .progress.bg-yellow .progress-bar {
  background-color: #FF9F38;
  border-radius: 15px;
}

.project-grid__bottom .progress.bg-red .progress-bar {
  background-color: #FF6628;
}

.project-grid__bottom .progress.bg-green .progress-bar {
  background-color: #4CBF4C;
}

.project-list__table {
  width: 100%;
  overflow-x: auto;
}

.project-list__table table {
  width: 100%;
}

.project-list__table table thead th {
  padding: 14px 20px;
  text-transform: capitalize;
  font-weight: 500;
  font-size: 14px;
  line-height: 17px;
  color: #273041;
}

.project-list__table table tbody td {
  padding: 16px 20px;
}

.project-list__table table tbody td:nth-child(1) {
  min-width: 220px;
}

.project-list__table table tbody td:nth-child(2) {
  min-width: 150px;
}

.project-list__table table tbody td:nth-child(3) {
  min-width: 150px;
}

.project-list__table table tbody td:nth-child(4) {
  min-width: 250px;
}

.project-list__table table tbody td:nth-child(5) {
  min-width: 150px;
}

.project-list__table table tbody td:nth-child(6) {
  min-width: 200px;
}

.project-list__table table tbody tr {
  border-bottom: 1px solid #ECEDF0;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.project-list__table table tbody tr:hover {
  background: #ffffff;
  -webkit-box-shadow: 0px 0px 15px rgba(161, 167, 179, 0.2);
          box-shadow: 0px 0px 15px rgba(161, 167, 179, 0.2);
}

.project-list__table table .project-info h2 {
  text-transform: capitalize;
  font-weight: 600;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.project-list__table table .project-info p {
  text-transform: capitalize;
  margin-bottom: 0;
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;
  color: rgba(89, 102, 128, 0.8);
}

.project-list__table table .data-text {
  text-transform: capitalize;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.project-list__table table .status {
  border-radius: 4px;
  padding: 3px 5px 2px;
  text-transform: uppercase;
  font-weight: 500;
  font-size: 12px;
  line-height: 15px;
  color: #ffffff;
}

.project-list__table table .status.bg-green {
  background-color: #4CBF4C;
}

.project-list__table table .status.bg-red {
  background-color: #FF6628;
}

.project-list__table table .status.bg-yellow {
  background-color: #FF9F38;
}

.project-list__table table .status.bg-purple {
  background-color: #BE63F9;
}

.project-list__table table .assigned-list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.project-list__table table .assigned-list li:not(:first-child) {
  margin-left: -10px;
}

.project-list__table table .assigned-list li a {
  text-transform: capitalize;
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;
  color: rgba(89, 102, 128, 0.8);
}

.project-list__table table .assigned-list li a span {
  margin-left: 15px;
}

.project-list__table table .assigned-list li a img {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  border: 2px solid #ffffff;
}

.project-list__table table .data-progress .task-text {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.project-list__table table .data-progress .task-text h2, .project-list__table table .data-progress .task-text p {
  margin-bottom: 2px;
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;
  color: #596680;
}

.project-list__table table .progress {
  background: #EBEDF0;
  border-radius: 15px;
  height: 8px;
}

.project-list__table table .progress-bar {
  background-color: #FF9F38;
  border-radius: 15px;
}

.project-list__table table .progress.bg-yellow {
  background: #EBEDF0;
  border-radius: 15px;
}

.project-list__table table .progress.bg-yellow .progress-bar {
  background-color: #FF9F38;
  border-radius: 15px;
}

.project-list__table table .progress.bg-red .progress-bar {
  background-color: #FF6628;
}

.project-list__table table .progress.bg-green .progress-bar {
  background-color: #4CBF4C;
}

.project-list__table table .progress.bg-purple .progress-bar {
  background-color: #BE63F9;
}

.overview-status__box {
  background: #FFFFFF;
  border: 1px solid #E4E6EB;
  -webkit-box-shadow: 0px 10px 25px rgba(163, 177, 204, 0.1);
          box-shadow: 0px 10px 25px rgba(163, 177, 204, 0.1);
  border-radius: 12px;
  padding: 30px 20px;
  margin-bottom: 30px;
  height: calc(100% - 30px);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.overview-status__box .item__left {
  margin-right: 15px;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 60px;
          flex: 0 0 60px;
}

.overview-status__box .item__left img {
  width: 60px;
  height: 60px;
  border-radius: 12px;
}

.overview-status__box .item__right h2 {
  margin-bottom: 5px;
  text-transform: capitalize;
  font-weight: 600;
  font-size: 24px;
  line-height: 29px;
  color: #273041;
}

.overview-status__box .item__right p {
  margin-bottom: 0;
  text-transform: capitalize;
  font-weight: 400;
  font-size: 16px;
  line-height: 19px;
  color: #596680;
}

.overview-info {
  padding: 25px;
  margin-bottom: 30px;
  height: calc(100% - 30px);
}

.overview-info .item__top {
  margin-bottom: 23px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.overview-info .item__top .item__img {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 60px;
          flex: 0 0 60px;
  margin-right: 15px;
}

.overview-info .item__top img {
  width: 60px;
  height: 60px;
  border-radius: 50%;
}

.overview-info .item__top h2 {
  margin-bottom: 3px;
  text-transform: capitalize;
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  color: #273041;
}

.overview-info .item__top p {
  margin-bottom: 0;
  text-transform: capitalize;
  font-weight: 500;
  font-size: 12px;
  line-height: 15px;
  color: #596680;
}

.overview-info .item__content h2 {
  margin-bottom: 11px;
  text-transform: capitalize;
  font-weight: 600;
  font-size: 14px;
  line-height: 24px;
  color: #273041;
}

.overview-info .item__content p {
  margin-bottom: 11px;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.overview-info__list {
  margin-top: 20px;
  margin-left: -60px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

@media screen and (max-width: 480px) {
  .overview-info__list {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-align: start;
        -ms-flex-align: start;
            align-items: flex-start;
  }
  .overview-info__list li {
    margin-left: 0;
  }
  .overview-info__list li:not(:first-child) {
    margin-top: 10px;
  }
}

.overview-info__list li {
  margin-left: 60px;
}

.overview-info__list .list-item p {
  margin-bottom: 0;
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;
  color: #596680;
}

.overview-info__list .list-item h2 {
  font-weight: 600;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.overview-team {
  padding: 25px;
  margin-bottom: 30px;
  height: calc(100% - 30px);
}

.overview-team__top {
  margin-bottom: 20px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.overview-team__top h2 {
  text-transform: capitalize;
  font-weight: 600;
  font-size: 20px;
  line-height: 24px;
  color: #273041;
}

.overview-team__top a {
  background: #F7EBFF;
  border-radius: 30px;
  padding: 10px 12px;
  text-transform: capitalize;
  font-weight: 500;
  font-size: 14px;
  line-height: 17px;
  color: #BE63F9;
}

.overview-team__list li {
  margin-top: 22px;
}

.overview-team__list .list-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.overview-team__list .list-item img {
  margin-right: 15px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
}

.overview-team__list .list-item h2 {
  margin-bottom: 2px;
  text-transform: capitalize;
  font-weight: 600;
  font-size: 14px;
  line-height: 24px;
  color: #273041;
}

.overview-team__list .list-item p {
  margin-bottom: 0;
  text-transform: capitalize;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.overview-files {
  padding: 26px 25px 30px;
  margin-bottom: 30px;
  height: calc(100% - 30px);
}

.overview-files__title {
  margin-bottom: 20px;
}

.overview-files__title h2 {
  text-transform: capitalize;
  font-weight: 600;
  font-size: 20px;
  line-height: 24px;
  color: #273041;
}

.overview-files__list {
  margin-top: -10px;
}

.overview-files__list li {
  margin-top: 10px;
}

.overview-files__list .list-item {
  background: #FAFBFC;
  border: 1px solid #EBEDF0;
  border-radius: 10px;
  padding: 15px 16px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.overview-files__list .list-item .item__left {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.overview-files__list .list-item .item__left img {
  width: 40px;
  height: 40px;
  margin-right: 18px;
}

.overview-files__list .list-item .item__left h2 {
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #273041;
}

.overview-files__list .list-item .item__left p {
  margin-bottom: 0;
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;
  color: #596680;
}

.overview-progress {
  padding: 26px 25px 30px;
  margin-bottom: 30px;
  height: calc(100% - 30px);
}

.overview-progress__top {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.overview-progress__top .item__left h2 {
  text-transform: capitalize;
  font-weight: 600;
  font-size: 20px;
  line-height: 24px;
  color: #273041;
}

.overview-progress__top .input__group select {
  background: #FAFBFC;
  border: 1px solid #EBEDF0;
  border-radius: 4px;
  padding: 10px 12px 9px 14px;
}

#overview-progress-chart .apexcharts-tooltip {
  background: #ffffff;
  -webkit-box-shadow: 0px 2px 20px rgba(163, 177, 204, 0.25);
          box-shadow: 0px 2px 20px rgba(163, 177, 204, 0.25);
  border-radius: 4px;
  font-weight: 500;
  font-size: 12px;
  line-height: 15px;
  color: #273041;
}

.create-project {
  padding: 25px;
  margin-bottom: 40px;
}

.manager__list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.manager__list li:not(:first-child) {
  margin-left: -15px;
}

.manager__list li a img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 2px solid #ffffff;
}

.create-project .upload-img {
  padding: 30px;
}

.create-project .upload-img img {
  margin-bottom: 25px;
}

.create-project .upload-img h2 {
  font-weight: 600;
  font-size: 18px;
  line-height: 22px;
  color: #273041;
}

.create-project .upload-img p {
  font-weight: 400;
  font-size: 15px;
  line-height: 18px;
  text-align: center;
  color: #596680;
}

.create-project .input__group select {
  border: 1px solid #DFE3EB;
}

.create-project .input__group .input-group {
  -ms-flex-wrap: nowrap;
      flex-wrap: nowrap;
}

.create-project .input__group .input-group-text {
  background: #F5F7FA;
}

.create-project .input__group .input__search {
  position: relative;
}

.create-project .input__group .input__search input {
  width: 100%;
  padding-left: 48px;
}

.create-project .input__group .input__search .overlay {
  position: absolute;
  top: 50%;
  left: 14px;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}

.create-project__bottom {
  margin-top: 150px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-left: -20px;
}

.create-project__bottom a {
  width: 140px;
  margin-left: 20px;
}

.task__item {
  padding: 25px;
  margin-bottom: 30px;
}

.task__item__top {
  margin-bottom: 10px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.task__item__top h2 {
  font-weight: 600;
  font-size: 20px;
  line-height: 24px;
  color: #273041;
}

.task__item .dropdown button {
  border: 0;
  background-color: transparent;
}

.task__item .dropdown .dropdown-menu {
  background: #FFFFFF;
  border: 1px solid #E9EDF5;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  -webkit-box-shadow: 0px 0px 10px rgba(163, 177, 204, 0.12), 0px 10px 20px -5px rgba(163, 177, 204, 0.2);
          box-shadow: 0px 0px 10px rgba(163, 177, 204, 0.12), 0px 10px 20px -5px rgba(163, 177, 204, 0.2);
  border-radius: 6px;
}

.task__item .dropdown .dropdown-menu.show {
  position: absolute;
  inset: 0px 0px auto auto !important;
  margin: 0px;
  -webkit-transform: translate(0px, 26px);
          transform: translate(0px, 26px);
}

.task__item .dropdown .dropdown-item {
  padding: 5px 12px;
  text-transform: capitalize;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.task__item .dropdown .dropdown-item span {
  margin-left: 10px;
}

.task__item .task__table {
  width: 100%;
  overflow-x: auto;
}

.task__item .task__table table {
  width: 100%;
}

.task__item .task__table table tbody td {
  padding: 15px 0;
  border-bottom: 1px solid #ECEDF0;
}

.task__item .task__table table tbody td:not(:first-child) {
  padding-left: 15px;
}

.task__item .task__table table tbody td:nth-child(2) {
  min-width: 250px;
}

.task__item .task__table table tbody td:nth-child(3) {
  min-width: 80px;
}

.task__item .task__table table tbody td:nth-child(4) {
  min-width: 150px;
}

.task__item .task__table table tbody td:nth-child(5) {
  min-width: 80px;
}

.task__item .task__table table .data-text {
  font-weight: 500;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.task__item .task__table table .user-img img {
  width: 36px;
  height: 36px;
  border-radius: 50%;
}

.task__item .task__table table .data-info {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.task__item .task__table table .data-info img {
  width: 16px;
  height: 16px;
  margin-right: 6px;
}

.task__item .task__table table .data-info span {
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;
  color: #596680;
}

.task__item .task__table table .status {
  background-color: #FF6628;
  border-radius: 5px;
  padding: 3px 5px 2px;
  font-style: normal;
  font-weight: 500;
  font-size: 12px;
  line-height: 15px;
  color: #ffffff;
}

.task__item .task__table table .status.bg-red {
  background-color: #FF6628;
}

.task__item .task__table table .status.bg-green {
  background-color: #4CBF4C;
}

.task__item .task__table table .status.bg-yellow {
  background-color: #FF9F38;
}

.task__chart__area {
  padding: 25px;
  margin-bottom: 30px;
}

.task__chart__top {
  margin-bottom: 20px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.task__chart__top .item__left h2 {
  text-transform: capitalize;
  font-weight: 600;
  font-size: 20px;
  line-height: 24px;
  color: #273041;
}

.latest-comments__item {
  margin-bottom: 30px;
  padding: 25px;
}

.latest-comments__title h2 {
  text-transform: capitalize;
  font-weight: 600;
  font-size: 20px;
  line-height: 24px;
  color: #273041;
}

.latest-comments__item .latest-comments__table {
  width: 100%;
  overflow-x: auto;
}

.latest-comments__item .latest-comments__table table {
  width: 100%;
}

.latest-comments__item .latest-comments__table table tbody td {
  padding: 22px 0;
  border-bottom: 1px solid #ECEDF0;
}

.latest-comments__item .latest-comments__table table tbody td:nth-child(1) {
  min-width: 60px;
}

.latest-comments__item .latest-comments__table table .user-img img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}

.latest-comments__item .latest-comments__table table .comment-info {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}

.latest-comments__item .latest-comments__table table .comment-info .comment-top {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.latest-comments__item .latest-comments__table table .comment-info h2 {
  text-transform: capitalize;
  font-weight: 500;
  font-size: 14px;
  line-height: 24px;
  color: #273041;
}

.latest-comments__item .latest-comments__table table .comment-info h3 {
  font-weight: 500;
  font-size: 12px;
  line-height: 15px;
  color: #596680;
}

.latest-comments__item .latest-comments__table table .comment-info p {
  margin-bottom: 0;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.kanban__area {
  padding: 25px;
  margin-bottom: 40px;
  height: calc(100% - 40px);
}

.kanban__area .dropdown button {
  border: 0;
  background-color: transparent;
}

.kanban__area .dropdown .dropdown-menu {
  background: #FFFFFF;
  border: 1px solid #E9EDF5;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  -webkit-box-shadow: 0px 0px 10px rgba(163, 177, 204, 0.12), 0px 10px 20px -5px rgba(163, 177, 204, 0.2);
          box-shadow: 0px 0px 10px rgba(163, 177, 204, 0.12), 0px 10px 20px -5px rgba(163, 177, 204, 0.2);
  border-radius: 6px;
}

.kanban__area .dropdown .dropdown-menu.show {
  position: absolute;
  inset: 0px 0px auto auto !important;
  margin: 0px;
  -webkit-transform: translate(0px, 26px);
          transform: translate(0px, 26px);
}

.kanban__area .dropdown .dropdown-item {
  padding: 5px 12px;
  text-transform: capitalize;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.kanban__area .dropdown .dropdown-item span {
  margin-left: 10px;
}

.kanban__top {
  margin-bottom: 25px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.kanban__top h2 {
  text-transform: capitalize;
  font-weight: 600;
  font-size: 18px;
  line-height: 24px;
  color: #273041;
}

.kanban__content .single__item {
  background: #FFFFFF;
  border: 1px solid #DFE3EB;
  border-radius: 10px;
  padding: 20px 16px;
}

.kanban__content .single__item:not(:first-child) {
  margin-top: 16px;
}

.kanban__content .single__item .item__bottom {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.kanban__content .single__item .input__group {
  margin-bottom: 13px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
}

.kanban__content .single__item .input__group input {
  margin-right: 12px;
}

.kanban__content .single__item .input__group label {
  text-transform: capitalize;
  margin-bottom: 0;
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  color: #273041;
}

.kanban__content .single__item p {
  margin-bottom: 12px;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.kanban__content .single__item h3 {
  margin-bottom: 15px;
}

.kanban__content .single__item h3 img {
  width: 16px;
  height: 16px;
}

.kanban__content .single__item h3 span {
  margin-left: 6px;
  text-transform: capitalize;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.kanban__content .member__list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-left: 15px;
}

.kanban__content .member__list li {
  margin-left: -15px;
}

.kanban__content .member__list li a img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 2px solid #ffffff;
}

.kanban__content .member__list li a span {
  display: block;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 2px solid #ffffff;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  font-weight: 600;
  font-size: 14px;
  line-height: 24px;
  text-align: center;
  color: #FFFFFF;
}

.kanban__content .single__item .item-text {
  text-align: right;
}

.kanban__content .single__item .item-text p {
  margin-bottom: 0;
  text-transform: capitalize;
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;
  color: #596680;
}

.kanban__content .single__item .item-text h2 {
  text-transform: capitalize;
  font-weight: 600;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.kanban__bottom {
  margin-top: 16px;
}

.task-details__item {
  padding: 25px;
  margin-bottom: 30px;
}

.task-details__item .item-top {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.task-details__item .item-top h2 {
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  color: #273041;
}

.task-details__item .item-top .dropdown button {
  border: 0;
  background-color: transparent;
}

.task-details__item .item-top .dropdown button:focus {
  border: 0;
  -webkit-box-shadow: none;
          box-shadow: none;
}

.task-details__item .item-top .dropdown .dropdown-menu {
  background: #FFFFFF;
  border: 1px solid #E9EDF5;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  -webkit-box-shadow: 0px 0px 10px rgba(163, 177, 204, 0.12), 0px 10px 20px -5px rgba(163, 177, 204, 0.2);
          box-shadow: 0px 0px 10px rgba(163, 177, 204, 0.12), 0px 10px 20px -5px rgba(163, 177, 204, 0.2);
  border-radius: 6px;
}

.task-details__item .item-top .dropdown .dropdown-menu.show {
  position: absolute;
  inset: 0px 0px auto auto !important;
  margin: 0px;
  -webkit-transform: translate(0px, 26px);
          transform: translate(0px, 26px);
}

.task-details__item .item-top .dropdown .dropdown-item {
  padding: 5px 12px;
  text-transform: capitalize;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.task-details__item .item-top .dropdown .dropdown-item span {
  margin-left: 10px;
}

.task-details__item .item-info {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-left: -45px;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

.task-details__item .item-info .single-info {
  margin: 0 0 24px 45px;
}

.task-details__item .item-info .single-info p {
  text-transform: capitalize;
  margin-bottom: 4px;
  font-weight: normal;
  font-size: 12px;
  line-height: 20px;
  color: #596680;
}

.task-details__item .item-info .single-info h2 {
  text-transform: capitalize;
  font-weight: 600;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.task-details__item .item-info .single-info .user-info {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.task-details__item .item-info .single-info .user-info img {
  width: 25px;
  height: 25px;
  border-radius: 50%;
  margin-right: 10px;
}

.task-details__item .item-info .single-info .user-info h2 {
  text-transform: capitalize;
  font-weight: 600;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.task-details__item .item-content {
  margin-bottom: 16px;
}

.task-details__item .item-content h2 {
  margin-bottom: 10px;
  font-weight: 600;
  font-size: 14px;
  line-height: 24px;
  color: #273041;
}

.task-details__item .item-content p {
  margin-bottom: 0;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.task-details__item .item-list-area h2 {
  margin-bottom: 10px;
  font-weight: 600;
  font-size: 14px;
  line-height: 24px;
  color: #273041;
}

.task-details__item .item-list-area .item-list li:not(:first-child) {
  margin-top: 8px;
}

.task-details__item .item-list-area .list-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.task-details__item .item-list-area .list-item img {
  width: 16px;
  height: 16px;
  margin-right: 10px;
}

.task-details__item .item-list-area .list-item p {
  margin-bottom: 0;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.attached-files__area {
  padding: 25px;
  margin-bottom: 30px;
}

.attached-files__area .upload-file {
  margin-bottom: 20px;
  background: #FAFBFC;
  border: 1px solid #EBEDF0;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  border-radius: 10px;
  padding: 34px 20px 39px;
  text-align: center;
}

.attached-files__area .upload-file input {
  display: none;
}

.attached-files__area .upload-file label {
  font-weight: 500;
  font-size: 16px;
  line-height: 19px;
  text-align: center;
  color: #273041;
}

.attached-files__area .upload-file label img {
  margin-bottom: 22px;
  width: 36px;
  height: 36px;
}

.attached-files__list li:not(:first-child) {
  margin-top: 10px;
}

.attached-files__list .list-item {
  background: #FAFBFC;
  border: 1px solid #EBEDF0;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  border-radius: 10px;
  padding: 15px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.attached-files__list .list-item .item__left {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.attached-files__list .list-item .item__left img {
  width: 40px;
  height: 40px;
  margin-right: 18px;
}

.attached-files__list .item-text h2 {
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #273041;
}

.attached-files__list .item-text p {
  margin-bottom: 0;
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;
  color: #596680;
}

.attached-files__list .list-item .item__right img {
  width: 24px;
  height: 24px;
}

.create-project .input__group {
  position: relative;
}

.search-member-list {
  position: absolute;
  top: 70px;
  left: 0;
  width: 100%;
  background: #ffffff;
  border: 1px solid #EBEDF0;
  -webkit-box-shadow: 0px 10px 20px -5px rgba(163, 177, 204, 0.25);
          box-shadow: 0px 10px 20px -5px rgba(163, 177, 204, 0.25);
  border-radius: 4px;
  margin-top: 1px;
  padding: 20px 18px;
  opacity: 0;
  visibility: hidden;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  -webkit-transform: translateY(5px);
          transform: translateY(5px);
}

.search-member-list.active {
  opacity: 1;
  visibility: visible;
  -webkit-transform: translateY(0px);
          transform: translateY(0px);
}

.search-member-list .list-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.search-member-list .list-item:not(:first-child) {
  margin-top: 20px;
}

.search-member-list .user-info {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.search-member-list .user-info img {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  margin-right: 14px;
}

.search-member-list .user-info h2 {
  text-transform: capitalize;
  font-weight: 500;
  font-size: 14px;
  line-height: 24px;
  color: #273041;
}

@media screen and (max-width: 480px) {
  .search-member-list {
    padding: 15px 10px;
  }
  .search-member-list .user-info img {
    width: 30px;
    height: 30px;
    margin-right: 5px;
  }
}

.customers__area {
  padding: 25px 25px 30px;
}

.customers__top {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

.customers__top__left {
  margin-bottom: 30px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.customers__top__left p {
  margin-bottom: 0;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.customers__top__left select {
  margin: 0 12px;
  padding: 11px 12px;
  border: 0;
  background: #F2F4F7;
  border-radius: 8px;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.customers__top__right {
  margin-bottom: 30px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

.customers__top__right .item:not(:first-child) {
  margin-left: 15px;
}

@media screen and (max-width: 480px) {
  .customers__top__right {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-align: start;
        -ms-flex-align: start;
            align-items: flex-start;
  }
  .customers__top__right .item:not(:first-child) {
    margin-left: 0;
    margin-top: 20px;
  }
}

.customers__top__right .item .input__group {
  position: relative;
}

.customers__top__right .item .input__group .overlay {
  position: absolute;
  top: 50%;
  left: 15px;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}

.customers__top__right .item .input__group .overlay a img {
  width: 20px;
  height: 20px;
}

.customers__top__right .item .input__group input {
  padding: 11px 15px 12px 45px;
  border: 0;
  background: #F2F4F7;
  border-radius: 8px;
}

.customers__table {
  width: 100%;
  overflow-x: auto;
}

.customers__table table {
  width: 100%;
}

.customers__table table thead th {
  padding: 14px 20px;
  background: #F5F7FA;
  font-weight: 500;
  font-size: 14px;
  line-height: 17px;
  color: #273041;
}

.customers__table table tbody td {
  border-bottom: 1px solid #ECEDF0;
  padding: 12px 20px 13px;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.customers__table table tbody td:nth-child(1) {
  min-width: 50px;
}

.customers__table table tbody td:nth-child(2) {
  min-width: 200px;
}

.customers__table table tbody td:nth-child(3) {
  min-width: 180px;
}

.customers__table table tbody td:nth-child(4) {
  min-width: 200px;
}

.customers__table table tbody td:nth-child(5) {
  min-width: 100px;
}

.customers__table table tbody td:nth-child(6) {
  min-width: 100px;
}

.customers__table table tbody td:nth-child(7) {
  min-width: 100px;
}

.all-course table thead th:last-child {
  min-width: 162px !important;
}

.all-course table tbody td:last-child {
  min-width: 162px !important;
}

.customers__table table tbody tr {
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.customers__table table tbody tr:hover {
  background: #ffffff;
  -webkit-box-shadow: 0px 0px 15px rgba(161, 167, 179, 0.2);
          box-shadow: 0px 0px 15px rgba(161, 167, 179, 0.2);
}

.user-info {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.user-info__img img {
  width: 30px;
  height: 30px;
  border-radius: 50%;
}

.user-info__text {
  margin-left: 14px;
}

.user-info__text h2 {
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.status {
  padding: 6px 10px 7px;
  border-radius: 4px;
  text-transform: capitalize;
  font-weight: 500;
  font-size: 12px;
  line-height: 15px;
}

.status.active {
  background-color: #E1FAE1;
  color: #4CBF4C;
}

.status.blocked {
  background-color: #FFE6E6;
  color: #F96363;
}

.action__buttons {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.action__buttons a i {
  font-size: 18px;
  color: #B6B9BF;
}

.action__buttons a:not(:first-child) {
  margin-left: 15px;
}

.user-contact {
  text-align: left;
}

.user-contact li:not(:first-child) {
  margin-top: 15px;
}

.user-contact .list-item img {
  width: 17px;
  height: 17px;
}

.user-contact .list-item span {
  margin-left: 12px;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.contact-list__area {
  padding: 25px;
  margin-bottom: 30px;
}

.contact-list__top {
  margin-bottom: 30px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

@media screen and (max-width: 767px) {
  .contact-list__top {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-align: start;
        -ms-flex-align: start;
            align-items: flex-start;
  }
  .contact-list__top .item__left {
    margin-bottom: 20px;
  }
}

.contact-list__top .input__group {
  position: relative;
}

.contact-list__top .input__group input {
  border: 0;
  width: 250px;
  background: #F2F4F7;
  border-radius: 8px;
  padding: 11px 12px 12px 45px;
}

.contact-list__top .input__group input:focus {
  border: 0;
}

.contact-list__top .input__group .overlay {
  position: absolute;
  top: 50%;
  left: 15px;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}

.contact-list__table {
  width: 100%;
  overflow-x: auto;
}

.contact-list__table table {
  width: 100%;
}

.contact-list__table table thead th {
  padding: 14px 20px;
  text-transform: capitalize;
  font-weight: 500;
  font-size: 14px;
  line-height: 17px;
  color: #273041;
}

.contact-list__table table tbody td {
  padding: 13px 20px;
}

.contact-list__table table tbody td:nth-child(1) {
  min-width: 250px;
}

.contact-list__table table tbody td:nth-child(2) {
  min-width: 200px;
}

.contact-list__table table tbody td:nth-child(3) {
  min-width: 200px;
}

.contact-list__table table tbody td:nth-child(4) {
  min-width: 200px;
}

.contact-list__table table tbody td:nth-child(5) {
  min-width: 300px;
}

.contact-list__table table tbody tr {
  border-bottom: 1px solid #ECEDF0;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.contact-list__table table tbody tr:hover {
  background: #ffffff;
  -webkit-box-shadow: 0px 0px 15px rgba(161, 167, 179, 0.2);
          box-shadow: 0px 0px 15px rgba(161, 167, 179, 0.2);
}

.contact-list__table table .user-info {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.contact-list__table table .user-info .user-img {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 40px;
          flex: 0 0 40px;
  margin-right: 12px;
}

.contact-list__table table .user-info .user-img img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}

.contact-list__table table .user-info .user-text h2 {
  text-transform: capitalize;
  font-weight: 500;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.contact-list__table table .user-info .user-text p {
  margin-bottom: 0;
  text-transform: capitalize;
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;
  color: #596680;
}

.contact-list__table table .data-text {
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.contact-list__table table .action-list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.contact-list__table table .action-list li:not(:last-child) {
  margin-right: 15px;
}

.contact-list__table table .action-list li:first-child {
  margin-right: 40px;
}

.contact-list__table table .action-list .list-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-left: -10px;
}

.contact-list__table table .action-list .list-item a {
  margin-left: 10px;
}

.profile__item {
  padding: 25px;
  margin-bottom: 30px;
}

.profile__item__top {
  margin-bottom: 23px;
  text-align: center;
}

.profile__item__top .user-img {
  margin-bottom: 20px;
}

.profile__item__top .user-img img {
  width: 95px;
  height: 95px;
  border-radius: 50%;
  border: 5px solid #FAFBFC;
}

.profile__item__top .user-text {
  margin-bottom: 17px;
}

.profile__item__top .user-text h2 {
  margin-bottom: 5px;
  text-transform: capitalize;
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  color: #273041;
}

.profile__item__top .user-text h3 {
  text-transform: capitalize;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.profile__item__content {
  margin-bottom: 20px;
}

.profile__item__content h2 {
  margin-bottom: 7px;
  font-weight: 600;
  font-size: 14px;
  line-height: 24px;
  color: #273041;
}

.profile__item__content p {
  margin-bottom: 0;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.profile__item__list li:not(:last-child) {
  padding-bottom: 7px;
  border-bottom: 1px solid #ECEDF0;
  margin-bottom: 7px;
}

.profile__item__list .list-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
}

.profile__item__list .list-item h2 {
  min-width: 80px;
  text-transform: capitalize;
  font-weight: 500;
  font-size: 14px;
  line-height: 24px;
  color: #273041;
}

.profile__item__list .list-item p {
  margin-bottom: 0;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.profile__inbox {
  padding: 25px;
  margin-bottom: 30px;
}

.profile__inbox__table {
  width: 100%;
  overflow-x: auto;
}

.profile__inbox__table table {
  width: 100%;
}

.profile__inbox__table table tbody tr td {
  padding: 17px 0;
}

.profile__inbox__table table tbody tr:first-child td {
  padding-top: 0;
}

.profile__inbox__table table tbody tr:last-child td {
  padding-bottom: 0;
}

.profile__inbox__table table tbody tr:not(:last-child) {
  border-bottom: 1px solid #ECEDF0;
}

.profile__inbox__table table .user-info {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.profile__inbox__table table .user-img {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 40px;
          flex: 0 0 40px;
  margin-right: 15px;
}

.profile__inbox__table table .user-img img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}

.profile__inbox__table table .user-text h2 {
  margin-bottom: 2px;
  text-transform: capitalize;
  font-weight: 500;
  font-size: 14px;
  line-height: 24px;
  color: #273041;
}

.profile__inbox__table table .user-text p {
  margin-bottom: 0;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.profile__status__area .status__item {
  margin-bottom: 30px;
  height: calc(100% - 30px);
  padding: 30px 20px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.profile__status__area .status__item .status-img {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 50px;
          flex: 0 0 50px;
  margin-right: 15px;
}

.profile__status__area .status__item .status-img img {
  width: 50px;
  height: 50px;
  border-radius: 12px;
}

.profile__status__area .status__item .status-text h2 {
  text-transform: capitalize;
  font-weight: 600;
  font-size: 20px;
  line-height: 24px;
  color: #273041;
}

.profile__status__area .status__item .status-text p {
  margin-bottom: 0;
  text-transform: capitalize;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.profile__timeline__area {
  padding: 25px;
  margin-bottom: 30px;
}

.profile__timeline__list h4 {
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.profile__timeline__list h2 {
  margin-bottom: 2px;
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  color: #273041;
}

.profile__timeline__list h3 {
  margin-bottom: 12px;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.profile__timeline__list p {
  margin-bottom: 27px;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.profile__timeline__list li:last-child p {
  margin-bottom: 0;
}

.profile__timeline__list .list-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.profile__timeline__list .list-item .item-left {
  margin-right: 46px;
  min-width: 55px;
}

.profile__timeline__list .list-item .item-left {
  position: relative;
}

.profile__timeline__list .list-item .item-left::before {
  position: absolute;
  z-index: 1;
  content: "";
  top: 0;
  right: -34px;
  width: 15px;
  height: 15px;
  border-radius: 50%;
  border: 5px double #5991FF;
  background-color: #FFFFFF;
}

.profile__timeline__list .list-item .item-right {
  position: relative;
}

.profile__timeline__list .list-item .item-right::before {
  position: absolute;
  content: "";
  top: 0;
  left: -21px;
  width: 3px;
  height: 100%;
  background-color: #EBEDF2;
  border-radius: 9px;
}

.profile__table__area {
  padding: 25px;
  margin-bottom: 30px;
}

.item-title {
  margin-bottom: 20px;
}

.item-title h2 {
  text-transform: capitalize;
  font-weight: 600;
  font-size: 20px;
  line-height: 24px;
  color: #273041;
}

.profile__table {
  width: 100%;
  overflow-x: auto;
}

.profile__table table {
  width: 100%;
}

.profile__table table thead th {
  padding: 14px 20px;
  font-weight: 500;
  font-size: 14px;
  line-height: 17px;
  color: #273041;
}

.profile__table table tbody td {
  padding: 11px 20px;
}

.profile__table table tbody tr {
  border-bottom: 1px solid #ECEDF0;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.profile__table table tbody tr:hover {
  background: #ffffff;
  -webkit-box-shadow: 0px 0px 15px rgba(161, 167, 179, 0.2);
          box-shadow: 0px 0px 15px rgba(161, 167, 179, 0.2);
}

.profile__table table .data-text {
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.user__item {
  margin-bottom: 30px;
  height: calc(100% - 30px);
  position: relative;
  padding: 25px;
  text-align: center;
}

@media screen and (max-width: 480px) {
  .user__item {
    padding: 20px 15px;
  }
}

.user__item__top {
  padding-bottom: 20px;
  border-bottom: 1px solid #ECEDF0;
  margin-bottom: 24px;
}

.user__item .user-img {
  margin-bottom: 20px;
}

.user__item .user-img img {
  width: 95px;
  height: 95px;
  border-radius: 50%;
  border: 5px solid #FAFBFC;
}

.user__item .user-text {
  margin-bottom: 17px;
}

.user__item .user-text h2 {
  margin-bottom: 5px;
  text-transform: capitalize;
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  color: #273041;
}

.user__item .user-text h3 {
  text-transform: capitalize;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.user__item .user-button {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  margin-left: -10px;
}

.user__item .user-button a {
  margin-left: 10px;
}

.user__item .overlay {
  position: absolute;
  top: 15px;
  right: 20px;
}

.user__item .overlay .dropdown button {
  border: 0;
  background-color: transparent;
}

.user__item .overlay .dropdown .dropdown-menu {
  background: #FFFFFF;
  border: 1px solid #E9EDF5;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  -webkit-box-shadow: 0px 0px 10px rgba(163, 177, 204, 0.12), 0px 10px 20px -5px rgba(163, 177, 204, 0.2);
          box-shadow: 0px 0px 10px rgba(163, 177, 204, 0.12), 0px 10px 20px -5px rgba(163, 177, 204, 0.2);
  border-radius: 6px;
}

.user__item .overlay .dropdown .dropdown-menu.show {
  position: absolute;
  inset: 0px 0px auto auto !important;
  margin: 0px;
  -webkit-transform: translate(0px, 26px);
          transform: translate(0px, 26px);
}

.user__item .overlay .dropdown .dropdown-item {
  padding: 5px 12px;
  text-transform: capitalize;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.user__item .overlay .dropdown .dropdown-item span {
  margin-left: 10px;
}

.user-status {
  text-align: center;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.user-status h2 {
  text-transform: capitalize;
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  color: #273041;
}

.user-status p {
  text-transform: capitalize;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.user-list__item {
  padding: 25px;
  margin-bottom: 30px;
  height: calc(100% - 30px);
}

@media screen and (max-width: 480px) {
  .user-list__item {
    padding: 20px 15px;
  }
}

.user-list__item__top {
  margin-bottom: 18px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

@media screen and (max-width: 991px) {
  .user-list__item__top {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
  .user-list__item__top .item-left {
    margin-bottom: 20px;
  }
}

.user-list__item__top .item-left {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.user-list__item__top .user-img {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 70px;
          flex: 0 0 70px;
  margin-right: 15px;
}

.user-list__item__top .user-img img {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  border: 5px solid #FAFBFC;
}

.user-list__item__top .user-text h2 {
  margin-bottom: 5px;
  text-transform: capitalize;
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  color: #273041;
}

.user-list__item__top .user-text h3 {
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.user-list__item__top .user-button {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-left: -10px;
}

.user-list__item__top .user-button a {
  margin-left: 10px;
}

.user-list__content {
  border-bottom: 1px solid #ECEDF0;
  margin-bottom: 24px;
}

.user-list__content p {
  margin-bottom: 13px;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.user-group__item {
  padding: 25px;
  margin-bottom: 30px;
  height: calc(100% - 30px);
  position: relative;
}

.user-group__item .overlay {
  position: absolute;
  top: 15px;
  right: 20px;
}

.user-group__item .overlay .dropdown button {
  border: 0;
  background-color: transparent;
}

.user-group__item .overlay .dropdown .dropdown-menu {
  background: #FFFFFF;
  border: 1px solid #E9EDF5;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  -webkit-box-shadow: 0px 0px 10px rgba(163, 177, 204, 0.12), 0px 10px 20px -5px rgba(163, 177, 204, 0.2);
          box-shadow: 0px 0px 10px rgba(163, 177, 204, 0.12), 0px 10px 20px -5px rgba(163, 177, 204, 0.2);
  border-radius: 6px;
}

.user-group__item .overlay .dropdown .dropdown-menu.show {
  position: absolute;
  inset: 0px 0px auto auto !important;
  margin: 0px;
  -webkit-transform: translate(0px, 26px);
          transform: translate(0px, 26px);
}

.user-group__item .overlay .dropdown .dropdown-item {
  padding: 5px 12px;
  text-transform: capitalize;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.user-group__item .overlay .dropdown .dropdown-item span {
  margin-left: 10px;
}

.user-group__item__top {
  margin-bottom: 18px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.user-group__item__top .user-img {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 60px;
          flex: 0 0 60px;
  margin-right: 15px;
}

.user-group__item__top .user-img img {
  width: 60px;
  height: 60px;
  border-radius: 50%;
}

.user-group__item__top .user-text h2 {
  margin-bottom: 3px;
  text-transform: capitalize;
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  color: #273041;
}

.user-group__item__top .user-text h3 {
  text-transform: capitalize;
  font-weight: 500;
  font-size: 12px;
  line-height: 15px;
  color: #596680;
}

.user-group__content {
  margin-bottom: 13px;
  border-bottom: 1px solid #DCDFE5;
}

.user-group__content p {
  margin-bottom: 15px;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.user-group__member {
  margin-bottom: 16px;
}

.user-group__member h2 {
  margin-bottom: 6px;
  text-transform: capitalize;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.user-group__member .member-list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-left: 15px;
}

.user-group__member .member-list li {
  margin-left: -15px;
}

.user-group__member .member-list li a img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 2px solid #ffffff;
}

.user-group__item .user-status {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.user-group__item .user-status .list-item.complete {
  text-align: left;
}

.user-group__item .user-status .list-item.complete h2 {
  color: #5991FF;
}

.user-group__item .user-status .list-item.pending {
  text-align: right;
}

.user-group__item .user-status .list-item.pending h2 {
  color: #BE63F9;
}

.user-group__item .user-status .list-item h3 {
  text-transform: capitalize;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.user-group__item .user-status .list-item h2 {
  text-transform: capitalize;
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
}

.add-user__area {
  padding: 25px 30px 30px;
  margin-bottom: 40px;
}

.add-user__item {
  margin-bottom: 10px;
}

.add-user__item .item-title {
  margin-bottom: 20px;
  border-bottom: 1px solid #DCDFE5;
}

.add-user__item .item-title h2 {
  margin-bottom: 12px;
  text-transform: capitalize;
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  color: #273041;
}

.add-user__item .input__group label {
  text-transform: capitalize;
  font-weight: 500;
  font-size: 14px;
  line-height: 17px;
  color: #273041;
}

.add-user__item .user__info {
  margin: 5px 0 26px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.add-user__item .user__info .user-img {
  margin-right: 40px;
  position: relative;
}

.add-user__item .user__info .user-img img {
  width: 80px;
  height: 80px;
  border-radius: 50%;
}

.add-user__item .user__info .user-img .overlay {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 25px;
  height: 25px;
  border-radius: 50%;
  background-color: #BE63F9;
  border: 1px solid #ffffff;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

.add-user__item .user__info .user-img .overlay label {
  cursor: pointer;
}

.add-user__item .user__info .user-img .overlay label img {
  width: 14px;
}

.add-user__item .user__info .user-img .overlay input {
  display: none;
}

.add-user__item .user__info .user-button a {
  border: 1px solid #EBEDF0;
  border-radius: 8px;
  padding: 12px 20px 11px;
  text-transform: capitalize;
  font-weight: 600;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

@media screen and (max-width: 480px) {
  .add-user__item .user__info {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
  .add-user__item .user__info .user-img {
    margin-right: 0;
    margin-bottom: 30px;
  }
}

.add-user__button {
  margin-top: 45px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-left: -20px;
}

.add-user__button a {
  width: 140px;
  margin-left: 20px;
}

.user-tbl__area {
  padding: 25px;
  margin-bottom: 30px;
}

.user-tbl__top {
  margin-bottom: 30px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

@media screen and (max-width: 767px) {
  .user-tbl__top {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-align: start;
        -ms-flex-align: start;
            align-items: flex-start;
  }
  .user-tbl__top .item__left {
    margin-bottom: 20px;
  }
}

.user-tbl__top .input__group {
  position: relative;
}

.user-tbl__top .input__group input {
  border: 0;
  width: 250px;
  background: #F2F4F7;
  border-radius: 8px;
  padding: 11px 12px 12px 45px;
}

.user-tbl__top .input__group input:focus {
  border: 0;
}

.user-tbl__top .input__group .overlay {
  position: absolute;
  top: 50%;
  left: 15px;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}

.user-tbl__table {
  width: 100%;
  overflow-x: auto;
}

.user-tbl__table table {
  width: 100%;
}

.user-tbl__table table thead th {
  padding: 14px 20px;
  text-transform: capitalize;
  font-weight: 500;
  font-size: 14px;
  line-height: 17px;
  color: #273041;
}

.user-tbl__table table tbody td {
  padding: 13px 20px;
}

.user-tbl__table table tbody td:nth-child(1) {
  min-width: 250px;
}

.user-tbl__table table tbody td:nth-child(2) {
  min-width: 200px;
}

.user-tbl__table table tbody td:nth-child(3) {
  min-width: 200px;
}

.user-tbl__table table tbody td:nth-child(4) {
  min-width: 200px;
}

.user-tbl__table table tbody td:nth-child(5) {
  min-width: 150px;
}

.user-tbl__table table tbody td:nth-child(6) {
  min-width: 150px;
}

.user-tbl__table table tbody tr {
  border-bottom: 1px solid #ECEDF0;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.user-tbl__table table tbody tr:hover {
  background: #ffffff;
  -webkit-box-shadow: 0px 0px 15px rgba(161, 167, 179, 0.2);
          box-shadow: 0px 0px 15px rgba(161, 167, 179, 0.2);
}

.user-tbl__table table .user-info {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.user-tbl__table table .user-info .user-img {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 40px;
          flex: 0 0 40px;
  margin-right: 12px;
}

.user-tbl__table table .user-info .user-img img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}

.user-tbl__table table .user-info .user-text h2 {
  text-transform: capitalize;
  font-weight: 500;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.user-tbl__table table .user-info .user-text p {
  margin-bottom: 0;
  text-transform: capitalize;
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;
  color: #596680;
}

.user-tbl__table table .data-text {
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.user-tbl__table table .action-list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.user-tbl__table table .action-list li:not(:first-child) {
  margin-left: 20px;
}

.user-tbl__table table .action-list a {
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.user-tbl__table table .action-list a img {
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.user-tbl__table table .action-list a:hover.action-preview img {
  -webkit-filter: invert(53%) sepia(36%) saturate(3255%) hue-rotate(200deg) brightness(101%) contrast(101%);
          filter: invert(53%) sepia(36%) saturate(3255%) hue-rotate(200deg) brightness(101%) contrast(101%);
}

.user-tbl__table table .action-list a:hover.action-edit img {
  -webkit-filter: invert(48%) sepia(54%) saturate(3180%) hue-rotate(242deg) brightness(100%) contrast(96%);
          filter: invert(48%) sepia(54%) saturate(3180%) hue-rotate(242deg) brightness(100%) contrast(96%);
}

.user-tbl__table table .action-list a:hover.action-trash img {
  -webkit-filter: invert(57%) sepia(77%) saturate(1400%) hue-rotate(317deg) brightness(94%) contrast(111%);
          filter: invert(57%) sepia(77%) saturate(1400%) hue-rotate(317deg) brightness(94%) contrast(111%);
}

.note-sidebar {
  padding: 24px;
  margin-bottom: 40px;
  height: calc(100% - 40px);
}

.note-sidebar__item:not(:first-child) {
  margin-top: 30px;
}

.note-sidebar__item h2 {
  margin-bottom: 15px;
  text-transform: capitalize;
  font-weight: 500;
  font-size: 14px;
  line-height: 17px;
  color: #273041;
}

.note-sidebar__menu li a {
  display: block;
  padding: 6px 12px;
}

.note-sidebar__menu li a img {
  width: 18px;
  height: 18px;
  margin-right: 12px;
}

.note-sidebar__menu li a span {
  font-weight: normal;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.note-sidebar__menu li a:hover, .note-sidebar__menu li a.active {
  background: rgba(89, 145, 255, 0.1);
  border-radius: 6px;
}

.note-sidebar__menu li a:hover img, .note-sidebar__menu li a.active img {
  -webkit-filter: invert(43%) sepia(95%) saturate(1127%) hue-rotate(203deg) brightness(105%) contrast(101%);
          filter: invert(43%) sepia(95%) saturate(1127%) hue-rotate(203deg) brightness(105%) contrast(101%);
}

.note-sidebar__menu li a:hover span, .note-sidebar__menu li a.active span {
  color: #5991FF;
}

.note-sidebar__list li:not(:first-child) {
  margin-top: 10px;
}

.note-sidebar__list li a {
  padding: 6px 12px;
  text-transform: capitalize;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.note-sidebar__list li a span {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  margin-right: 16px;
}

.note__area {
  padding: 25px;
  margin-bottom: 40px;
  height: calc(100% - 40px);
}

.note__area__top {
  margin-bottom: 26px;
}

.note__area__top .input__group {
  position: relative;
}

.note__area__top .input__group input {
  width: 100%;
  padding: 11px 12px 12px 45px;
  background: #F2F4F7;
  border-radius: 8px;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.note__area__top .input__group .overlay {
  position: absolute;
  top: 50%;
  left: 15px;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}

.note__item {
  margin-bottom: 26px;
  background: #FAFBFC;
  border: 1px solid #EBEDF0;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  border-radius: 10px;
  padding: 16px 18px 22px;
  position: relative;
}

.note__item .overlay {
  position: absolute;
  top: 20px;
  right: 18px;
}

.note__item .overlay .dropdown .dropdown-menu {
  background: #FFFFFF;
  border: 1px solid #E9EDF5;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  -webkit-box-shadow: 0px 0px 10px rgba(163, 177, 204, 0.12), 0px 10px 20px -5px rgba(163, 177, 204, 0.2);
          box-shadow: 0px 0px 10px rgba(163, 177, 204, 0.12), 0px 10px 20px -5px rgba(163, 177, 204, 0.2);
  border-radius: 6px;
}

.note__item .overlay .dropdown .dropdown-menu.show {
  position: absolute;
  inset: 0px 0px auto auto !important;
  margin: 0px;
  -webkit-transform: translate(0px, 26px);
          transform: translate(0px, 26px);
}

.note__item .overlay .dropdown .dropdown-item {
  padding: 5px 12px;
  text-transform: capitalize;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.note__item .overlay .dropdown .dropdown-item span {
  margin-right: 10px;
  width: 14px;
  height: 14px;
  border-radius: 50%;
}

.note__item__content h2 {
  margin-bottom: 1px;
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  color: #273041;
}

.note__item__content h3 {
  margin-bottom: 10px;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.note__item__content p {
  margin-bottom: 0;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.note__item__bottom {
  margin-top: 17px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.note__item__bottom .note-button {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-left: -15px;
}

.note__item__bottom .note-button a {
  margin-left: 15px;
}

.note__item__bottom .note-button a img {
  width: 20px;
  height: 20px;
}

.note__item__bottom .note-status {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  display: block;
}

#add-note-modal .overlay {
  position: absolute;
  z-index: 999;
  top: 15px;
  right: 15px;
}

#add-note-modal .modal-content {
  background: #FFFFFF;
  -webkit-box-shadow: 0px 15px 20px rgba(39, 48, 65, 0.25);
          box-shadow: 0px 15px 20px rgba(39, 48, 65, 0.25);
  border-radius: 12px;
}

#add-note-modal .modal-header {
  border: 0;
}

#add-note-modal .modal-body {
  margin-top: -25px;
  padding: 25px;
}

#add-note-modal .modal-footer {
  padding: 0 25px 25px;
  border: 0;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
}

#add-note-modal .modal-footer a {
  width: 140px;
}

.todo-sidebar {
  padding: 24px;
  margin-bottom: 40px;
  height: calc(100% - 40px);
}

.todo-sidebar__item:not(:first-child) {
  margin-top: 30px;
}

.todo-sidebar__item h2 {
  margin-bottom: 15px;
  text-transform: capitalize;
  font-weight: 500;
  font-size: 14px;
  line-height: 17px;
  color: #273041;
}

.todo-sidebar__menu li a {
  display: block;
  padding: 6px 12px;
}

.todo-sidebar__menu li a img {
  width: 18px;
  height: 18px;
  margin-right: 12px;
}

.todo-sidebar__menu li a span {
  font-weight: normal;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.todo-sidebar__menu li a:hover, .todo-sidebar__menu li a.active {
  background: rgba(89, 145, 255, 0.1);
  border-radius: 6px;
}

.todo-sidebar__menu li a:hover img, .todo-sidebar__menu li a.active img {
  -webkit-filter: invert(43%) sepia(95%) saturate(1127%) hue-rotate(203deg) brightness(105%) contrast(101%);
          filter: invert(43%) sepia(95%) saturate(1127%) hue-rotate(203deg) brightness(105%) contrast(101%);
}

.todo-sidebar__menu li a:hover span, .todo-sidebar__menu li a.active span {
  color: #5991FF;
}

.todo-sidebar__list li:not(:first-child) {
  margin-top: 10px;
}

.todo-sidebar__list li a {
  padding: 6px 12px;
  text-transform: capitalize;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.todo-sidebar__list li a img {
  width: 18px;
  height: 18px;
  margin-right: 12px;
}

.todo__area {
  padding: 25px;
  margin-bottom: 40px;
  height: calc(100% - 40px);
}

.todo__area .modal-content {
  background: #FFFFFF;
  -webkit-box-shadow: 0px 15px 20px rgba(39, 48, 65, 0.25);
          box-shadow: 0px 15px 20px rgba(39, 48, 65, 0.25);
  border-radius: 12px;
}

.todo__area__top {
  margin-bottom: 26px;
}

.todo__area__top .input__group {
  position: relative;
}

.todo__area__top .input__group input {
  width: 100%;
  padding: 11px 12px 12px 45px;
  background: #F2F4F7;
  border-radius: 8px;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.todo__area__top .input__group .overlay {
  position: absolute;
  top: 50%;
  left: 15px;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}

.todo__item {
  margin-bottom: 26px;
  background: #FAFBFC;
  border: 1px solid #EBEDF0;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  border-radius: 10px;
  padding: 16px 18px 22px;
  position: relative;
}

.todo__item .overlay {
  position: absolute;
  top: 20px;
  right: 18px;
}

.todo__item .overlay .overlay__list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.todo__item .overlay .overlay__list .list-item:not(:first-child) {
  margin-left: 20px;
}

.todo__item .overlay .dropdown .dropdown-menu {
  background: #FFFFFF;
  border: 1px solid #E9EDF5;
  -webkit-box-shadow: 0px 0px 10px rgba(163, 177, 204, 0.12), 0px 10px 20px -5px rgba(163, 177, 204, 0.2);
          box-shadow: 0px 0px 10px rgba(163, 177, 204, 0.12), 0px 10px 20px -5px rgba(163, 177, 204, 0.2);
  border-radius: 6px;
}

.todo__item .overlay .dropdown .dropdown-menu.show {
  position: absolute;
  inset: 0px 0px auto auto !important;
  margin: 0px;
  -webkit-transform: translate(0px, 26px);
          transform: translate(0px, 26px);
}

.todo__item .overlay .dropdown .dropdown-item {
  padding: 5px 12px;
  text-transform: capitalize;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.todo__item .overlay .dropdown .dropdown-item img {
  margin-right: 10px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
}

.todo__item {
  background: #FAFBFC;
  border: 1px solid #EBEDF0;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  border-radius: 10px;
  padding: 18px 20px 15px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
}

.todo__item .item-check {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 16px;
          flex: 0 0 16px;
  margin-right: 15px;
}

.todo__item .item-text h2 {
  margin-bottom: 4px;
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  color: #273041;
}

.todo__item .item-text h3 {
  margin-bottom: 8px;
  font-weight: 500;
  font-size: 12px;
  line-height: 15px;
  color: #596680;
}

.todo__item .item-text p {
  margin-bottom: 0;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

@media screen and (max-width: 480px) {
  .todo__item {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
  .todo__item .item-check {
    margin-right: 0;
    margin-bottom: 15px;
  }
}

.todo__item.active .item-text h2,
.todo__item.active .item-text h3,
.todo__item.active .item-text p {
  text-decoration: line-through;
}

.modal-todo-edit .overlay {
  position: absolute;
  z-index: 999;
  top: 15px;
  right: 15px;
}

.modal-todo-edit .modal-header {
  border: 0;
}

.modal-todo-edit .modal-body {
  margin-top: -25px;
  padding: 25px;
}

.modal-todo-edit .modal-body h2 {
  padding-bottom: 12px;
  border-bottom: 1px solid #DCDFE5;
  margin-bottom: 18px;
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  color: #273041;
}

.modal-todo-edit .modal-body p {
  margin-bottom: 0;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.modal-todo-edit .modal-footer {
  padding: 25px;
  border: 0;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
}

.modal-todo-edit .modal-footer a {
  width: 140px;
}

#add-todo-modal .overlay {
  position: absolute;
  z-index: 999;
  top: 15px;
  right: 15px;
}

#add-todo-modal .modal-content {
  background: #FFFFFF;
  -webkit-box-shadow: 0px 15px 20px rgba(39, 48, 65, 0.25);
          box-shadow: 0px 15px 20px rgba(39, 48, 65, 0.25);
  border-radius: 12px;
}

#add-todo-modal .modal-header {
  /* border: 0; */
}
#add-todo-modal .modal-header {
  border: 0;
  padding: 25px;
}

#add-todo-modal .modal-body {
  margin-top: -25px;
  padding: 25px;
}

#add-todo-modal .modal-footer {
  padding: 0 25px 25px;
  border: 0;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
}

#add-todo-modal .modal-footer a {
  width: 140px;
}

.orders__area {
  padding: 25px 25px 30px;
}

.orders__top {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

.orders__top__left {
  margin-bottom: 30px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.orders__top__left p {
  margin-bottom: 0;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.orders__top__left select {
  margin: 0 12px;
  padding: 11px 12px;
  border: 0;
  background: #F2F4F7;
  border-radius: 8px;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.orders__top__right {
  margin-bottom: 10px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

.orders__top__right .item {
  margin-bottom: 20px;
}

.orders__top__right .item:not(:first-child) {
  margin-left: 15px;
}

@media screen and (max-width: 480px) {
  .orders__top__right {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-align: start;
        -ms-flex-align: start;
            align-items: flex-start;
  }
  .orders__top__right .item:not(:first-child) {
    margin-left: 0;
  }
}

.orders__top__right .item .input__group {
  position: relative;
}

.orders__top__right .item .input__group .overlay {
  position: absolute;
  top: 50%;
  left: 15px;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}

.orders__top__right .item .input__group .overlay a img {
  width: 20px;
  height: 20px;
}

.orders__top__right .item .input__group input {
  padding: 11px 15px 12px 45px;
  border: 0;
  background: #F2F4F7;
  border-radius: 8px;
}

@media screen and (max-width: 480px) {
  .orders__top__right .item .input__group input {
    width: 100%;
  }
}

.sort-by {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

@media screen and (max-width: 480px) {
  .sort-by {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-align: start;
        -ms-flex-align: start;
            align-items: flex-start;
  }
  .sort-by h2 {
    margin-bottom: 10px;
  }
}

.sort-by h2 {
  margin-right: 15px;
  font-family: Inter;
  font-style: normal;
  font-weight: normal;
  font-size: 14px;
  line-height: 17px;
  color: #666B87;
}

.sort-by__list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  background: #F2F4F7;
  border-radius: 8px;
  padding: 11px 15px 12px;
}

.sort-by__list li a,
.sort-by__list .nav-link {
  display: block;
  padding: 0 10px;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #666B87;
}

.sort-by__list li a.active,
.sort-by__list .nav-link.active {
  font-weight: 600;
  font-size: 14px;
  line-height: 24px;
  color: #5991FF;
}

.orders__table {
  width: 100%;
  overflow-x: auto;
}

.orders__table table {
  width: 100%;
}

.orders__table table thead th {
  padding: 14px 20px;
  background: #F5F7FA;
  font-weight: 500;
  font-size: 14px;
  line-height: 17px;
  color: #273041;
}

.orders__table table tbody td {
  border-bottom: 1px solid #ECEDF0;
  padding: 12px 20px 13px;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.orders__table table tbody td:nth-child(1) {
  min-width: 50px;
}

.orders__table table tbody td:nth-child(2) {
  min-width: 150px;
}

.orders__table table tbody td:nth-child(3) {
  min-width: 200px;
}

.orders__table table tbody td:nth-child(4) {
  min-width: 150px;
}

.orders__table table tbody td:nth-child(5) {
  min-width: 150px;
}

.orders__table table tbody td:nth-child(6) {
  min-width: 200px;
}

.orders__table table tbody td:nth-child(7) {
  min-width: 200px;
}

.orders__table table tbody td:nth-child(8) {
  min-width: 150px;
}

.orders__table table tbody tr {
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.orders__table table tbody tr:hover {
  background: #ffffff;
  -webkit-box-shadow: 0px 0px 15px rgba(161, 167, 179, 0.2);
          box-shadow: 0px 0px 15px rgba(161, 167, 179, 0.2);
}

.user-info {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.user-info__img img {
  width: 30px;
  height: 30px;
  border-radius: 50%;
}

.user-info__text {
  margin-left: 14px;
}

.user-info__text h2 {
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.status {
  padding: 6px 10px 7px;
  border-radius: 4px;
  text-transform: capitalize;
  font-weight: 500;
  font-size: 12px;
  line-height: 15px;
}

.status.bg-grey {
  background: rgba(39, 48, 65, 0.1);
  color: rgba(39, 48, 65, 0.5);
}

.status.bg-yellow {
  background: #FCF6E8;
  color: #FFAA00;
}

.status.bg-green {
  background-color: #E1FAE1;
  color: #4CBF4C;
}

.status.bg-red {
  background-color: #FFE6E6;
  color: #F96363;
}

.action__buttons {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.action__buttons a i {
  font-size: 18px;
  color: #B6B9BF;
}

.action__buttons a:not(:first-child) {
  margin-left: 15px;
}

.shop__item {
  margin-bottom: 30px;
  padding: 30px 30px 33px;
  height: calc(100% - 30px);
}

.shop__item__top {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border-bottom: 1px solid #ECEDF0;
}

.shop__item__top .shop__item__img {
  width: 130px;
  height: 130px;
  border-radius: 50%;
  background-color: #C4C4C4;
  -webkit-filter: drop-shadow(0px 5px 15px rgba(182, 186, 191, 0.15));
          filter: drop-shadow(0px 5px 15px rgba(182, 186, 191, 0.15));
  margin-bottom: 20px;
}

.shop__item__top .shop__item__img img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.shop__item__top h2 {
  margin-bottom: 16px;
  font-weight: 600;
  font-size: 18px;
  line-height: 22px;
  color: #273041;
}

.shop__item__between {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.shop__item__between .single__item {
  margin-top: 17px;
}

.shop__item__between .single__item h3 {
  font-weight: 400;
  font-size: 16px;
  line-height: 19px;
  color: #596680;
  margin-bottom: 6px;
}

.shop__item__between .single__item h2 {
  font-weight: 600;
  font-size: 23px;
  line-height: 28px;
  color: #BE63F9;
}

.shop__item__button {
  margin-top: 29px;
}

.shop__item__button a {
  font-weight: 600;
  font-size: 16px;
  line-height: 19px;
  color: #5991FF;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.shop__item__button a span {
  margin-left: 15px;
}

.shop__item__button a span i {
  font-size: 35px;
}

.sellers__area {
  padding: 25px 25px 30px;
}

.sellers__top {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

.sellers__top__left {
  margin-bottom: 30px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.sellers__top__left p {
  margin-bottom: 0;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.sellers__top__left select {
  margin: 0 12px;
  padding: 11px 12px;
  border: 0;
  background: #F2F4F7;
  border-radius: 8px;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.sellers__top__right {
  margin-bottom: 30px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

.sellers__top__right .item:not(:first-child) {
  margin-left: 15px;
}

@media screen and (max-width: 480px) {
  .sellers__top__right {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-align: start;
        -ms-flex-align: start;
            align-items: flex-start;
  }
  .sellers__top__right .item:not(:first-child) {
    margin-left: 0;
    margin-top: 20px;
  }
}

.sellers__top__right .item .input__group {
  position: relative;
}

.sellers__top__right .item .input__group .overlay {
  position: absolute;
  top: 50%;
  left: 15px;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}

.sellers__top__right .item .input__group .overlay a img {
  width: 20px;
  height: 20px;
}

.sellers__top__right .item .input__group input {
  padding: 11px 15px 12px 45px;
  border: 0;
  background: #F2F4F7;
  border-radius: 8px;
}

.sellers__table {
  width: 100%;
  overflow-x: auto;
}

.sellers__table table {
  width: 100%;
}

.sellers__table table thead th {
  padding: 14px 20px;
  background: #F5F7FA;
  font-weight: 500;
  font-size: 14px;
  line-height: 17px;
  color: #273041;
}

.sellers__table table tbody td {
  border-bottom: 1px solid #ECEDF0;
  padding: 12px 20px 13px;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.sellers__table table tbody td:nth-child(1) {
  min-width: 50px;
}

.sellers__table table tbody td:nth-child(2) {
  min-width: 220px;
}

.sellers__table table tbody td:nth-child(3) {
  min-width: 200px;
}

.sellers__table table tbody td:nth-child(4) {
  min-width: 150px;
}

.sellers__table table tbody td:nth-child(5) {
  min-width: 200px;
}

.sellers__table table tbody td:nth-child(6) {
  min-width: 180px;
}

.sellers__table table tbody td:nth-child(7) {
  min-width: 100px;
}

.sellers__table table tbody tr {
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.sellers__table table tbody tr:hover {
  background: #ffffff;
  -webkit-box-shadow: 0px 0px 15px rgba(161, 167, 179, 0.2);
          box-shadow: 0px 0px 15px rgba(161, 167, 179, 0.2);
}

.user-info {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.user-info__img img {
  width: 30px;
  height: 30px;
  border-radius: 50%;
}

.user-info__text {
  margin-left: 14px;
}

.user-info__text h2 {
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.status {
  padding: 6px 10px 7px;
  border-radius: 4px;
  text-transform: capitalize;
  font-weight: 500;
  font-size: 12px;
  line-height: 15px;
}

.status.active {
  background-color: #E1FAE1;
  color: #4CBF4C;
}

.status.edit {
  background-color: #222ec0;
  color: #ffffff;
}

.status.blocked {
  background-color: #FFE6E6;
  color: #F96363;
}

.action__buttons {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.action__buttons a i {
  font-size: 18px;
  color: #B6B9BF;
}

.action__buttons a:not(:first-child) {
  margin-left: 15px;
}

.social-feed__area {
  background: #FFFFFF;
  -webkit-box-shadow: 0px 10px 25px rgba(163, 177, 204, 0.1);
          box-shadow: 0px 10px 25px rgba(163, 177, 204, 0.1);
  overflow: hidden;
  border-radius: 12px;
  margin-bottom: 40px;
}

.cover-photo {
  border-radius: 12px 12px 0 0;
  overflow: hidden;
}

.cover-photo img {
  width: 100%;
  height: 301px;
}

@media screen and (max-width: 575px) {
  .cover-photo img {
    height: 200px;
  }
}

.social-feed__content {
  padding: 0 30px 30px;
  border-top: 0;
  border-left: 1px solid #E4E6EB;
  border-right: 1px solid #E4E6EB;
  border-bottom: 1px solid #E4E6EB;
  border-radius: 0 0 12px 12px;
}

@media screen and (max-width: 480px) {
  .social-feed__content {
    padding: 0 15px 30px;
  }
}

.social-feed__top {
  margin-top: -129px;
  margin-bottom: 20px;
}

.social-feed__top .profile-photo {
  margin-bottom: 12px;
}

.social-feed__top .profile-photo img {
  width: 180px;
  height: 180px;
  border-radius: 50%;
  border: 5px solid #ffffff;
}

.social-feed__top .profile-info {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

@media screen and (max-width: 575px) {
  .social-feed__top .profile-info {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-align: start;
        -ms-flex-align: start;
            align-items: flex-start;
  }
  .social-feed__top .profile-info .info__left {
    margin-bottom: 20px;
  }
}

.social-feed__top .profile-info .info__left h2 {
  margin-bottom: 7px;
  text-transform: capitalize;
  font-weight: 700;
  font-size: 25px;
  line-height: 30px;
  color: #273041;
}

.social-feed__top .profile-info .info__left h3 {
  text-transform: capitalize;
  font-weight: 500;
  font-size: 16px;
  line-height: 19px;
  color: #596680;
}

.social-feed__top .profile-info .info__list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.social-feed__top .profile-info .info__list li:not(:first-child) {
  margin-left: 45px;
}

.social-feed__top .profile-info .info__list .list-item {
  text-align: center;
}

.social-feed__top .profile-info .info__list .list-item h2 {
  margin-bottom: 4px;
  font-weight: 600;
  font-size: 16px;
  line-height: 19px;
  color: #273041;
}

.social-feed__top .profile-info .info__list .list-item p {
  margin-bottom: 0;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  text-transform: uppercase;
  color: #596680;
}

.social-feed__tabs .nav-tabs {
  border-top: 1px solid #DCDFE5;
  border-bottom: 1px solid #DCDFE5;
}

.social-feed__tabs .nav-tabs .nav-link:not(:first-child) {
  margin-left: 34px;
}

@media screen and (max-width: 480px) {
  .social-feed__tabs .nav-tabs .nav-link:not(:first-child) {
    margin-left: 15px;
  }
}

.social-feed__tabs .nav-link {
  padding: 15px 0 !important;
  border: 0;
  border-bottom: 1px solid transparent;
  background-color: transparent;
  text-transform: capitalize;
  font-weight: 500;
  font-size: 16px;
  line-height: 19px;
  color: #596680;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.social-feed__tabs .nav-link.active {
  color: #5991FF;
  border-bottom: 1px solid #5991FF;
}

.social-feed__tabs .tab-content {
  margin-top: 28px;
}

.create-post__item {
  padding: 26px 25px 28px;
  margin-bottom: 30px;
  background: #F7F9FA;
  border-radius: 10px;
}

@media screen and (max-width: 480px) {
  .create-post__item {
    padding: 25px 20px;
  }
}

.create-post__title {
  margin-bottom: 25px;
}

.create-post__title h2 {
  text-transform: capitalize;
  font-weight: 600;
  font-size: 20px;
  line-height: 24px;
  color: #273041;
}

.create-post__top {
  margin-bottom: 20px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.create-post__top__left {
  margin-right: 15px;
}

.create-post__top .user__img img {
  width: 50px;
  height: 50px;
  border-radius: 50%;
}

.create-post__top .user__name h2 {
  margin-bottom: 5px;
  text-transform: capitalize;
  font-weight: 500;
  font-size: 16px;
  line-height: 19px;
  color: #596680;
}

.create-post__top .dropdown {
  text-align: center;
  padding: 3px 10px 4px;
  border: 0;
  background: #BE63F9;
  border-radius: 4px;
}

.create-post__top .dropdown a {
  text-transform: capitalize;
  font-weight: 500;
  font-size: 12px;
  line-height: 15px;
  color: #ffffff;
}

.create-post__top .dropdown a img {
  margin-right: 5px;
  width: 12px;
  height: 12px;
}

.create-post__top .dropdown .dropdown-menu {
  position: absolute;
  top: 100% !important;
  left: 0%;
  inset: none !important;
  margin: 0px;
  -webkit-transform: none !important;
          transform: none !important;
  background: #BE63F9;
}

.create-post__content {
  margin-bottom: 12px;
}

.create-post__content textarea {
  width: 100%;
  resize: none;
  padding: 15px 16px;
  background: #ffffff;
  border: 1px solid #DFE3EB;
  border-radius: 10px;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
}

.create-post__bottom {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.create-post__bottom h2 {
  margin-right: 20px;
  font-weight: 500;
  font-size: 16px;
  line-height: 19px;
  color: #596680;
}

.create-post__bottom__list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-left: -12px;
}

.create-post__bottom__list li {
  margin-left: 12px;
}

.create-post__bottom__list li button {
  border: 0;
  background-color: transparent;
}

@media screen and (max-width: 480px) {
  .create-post__bottom {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-align: start;
        -ms-flex-align: start;
            align-items: flex-start;
  }
  .create-post__bottom h2 {
    margin-right: 0;
    margin-bottom: 10px;
  }
}

.create-post__bottom__list .input__group label {
  margin-bottom: 0;
}

.create-post__bottom__list .input__group input {
  display: none;
}

.social-feed__content .post__item {
  border: 0;
  background-color: #F7F9FA;
  border-radius: 10px;
  padding: 25px;
}

.social-feed__content .post__item__top {
  margin-bottom: 20px;
}

@media screen and (max-width: 480px) {
  .social-feed__content .post__item {
    padding: 20px;
  }
}

.post__item__top {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.post__author {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.post__author .author__img img {
  width: 45px;
  height: 45px;
  border-radius: 50%;
}

.post__author .author__info {
  margin-left: 15px;
}

.post__author .author__info h2 {
  margin-bottom: 5px;
  font-weight: 500;
  font-size: 15px;
  line-height: 18px;
  color: #273041;
}

.post__author .author__info p {
  margin-bottom: 0;
  font-weight: 400;
  font-size: 12px;
  line-height: 15px;
  color: #596680;
}

.post__item__top__right .dropdown .dropdown-menu {
  padding: 8px 0 !important;
  background: #FFFFFF;
  border: 1px solid #E9EDF5;
  -webkit-box-shadow: 0px 0px 10px rgba(163, 177, 204, 0.12), 0px 10px 20px -5px rgba(163, 177, 204, 0.2);
          box-shadow: 0px 0px 10px rgba(163, 177, 204, 0.12), 0px 10px 20px -5px rgba(163, 177, 204, 0.2);
  border-radius: 6px;
}

.post__item__top__right .dropdown li:not(:first-child) {
  margin-left: 0px;
}

.post__item__top__right .dropdown .dropdown-item {
  padding: 5px 12px;
  text-transform: initial;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.post__item__top__right .dropdown .dropdown-item img {
  width: 16px;
  height: 16px;
  margin-right: 10px;
}

.post__item__top__right .dropdown .dropdown-item:hover {
  border-color: transparent;
}

.post__item__top__right .dropdown hr {
  background-color: #DCDFE5;
}

.post__item__content p {
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.post__item__content .multiple-img {
  margin: 18px 0 25px;
  display: -ms-grid;
  display: grid;
  -ms-grid-columns: auto auto;
      grid-template-columns: auto auto;
  grid-row-gap: 15px;
  grid-column-gap: 15px;
}

.post__item__content .multiple-img .item-img {
  border-radius: 12px;
  overflow: hidden;
}

.post__item__content .multiple-img .item-img:nth-child(1) {
  -ms-grid-column: 1;
  grid-column: 1;
  -ms-grid-row: 1;
  -ms-grid-row-span: 2;
  grid-row: 1 / span 2;
}

.post__item__content .multiple-img .item-img:nth-child(2) {
  -ms-grid-column: 2;
  grid-column: 2;
  -ms-grid-row: 1;
  grid-row: 1;
}

.post__item__content .multiple-img .item-img:nth-child(3) {
  -ms-grid-column: 2;
  grid-column: 2;
  -ms-grid-row: 2;
  grid-row: 2;
}

.post__item__content .multiple-img .item-img {
  position: relative;
}

.post__item__content .multiple-img .item-img img {
  width: 100%;
  height: 100%;
}

.post__item__content .multiple-img .item-img .overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
}

.post__item__content .multiple-img .item-img .overlay a {
  text-transform: capitalize;
  font-weight: 600;
  font-size: 32px;
  line-height: 40px;
  color: #FFFFFF;
}

.post__item__content .site-share {
  margin: 18px 0 25px 0;
  border: 1px solid #DCDFE5;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  border-radius: 10px;
  overflow: hidden;
}

.post__item__content .site-share__text {
  padding: 20px;
}

.post__item__content .site-share img {
  width: 100%;
  height: 300px;
}

.post__item__content .site-share h2 {
  margin-bottom: 9px;
  font-weight: 600;
  font-size: 18px;
  line-height: 24px;
  color: #273041;
}

.post__item__content .site-share p {
  margin-bottom: 8px;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.post__item__content .site-share h3 {
  font-weight: 500;
  font-size: 12px;
  line-height: 15px;
  color: #596680;
}

@media screen and (max-width: 480px) {
  .post__item__content .site-share img {
    height: 200px;
  }
}

.post__item__bottom {
  border-top: 1px solid #DCDFE5;
}

.post__item__meta .meta__status {
  border-bottom: 1px solid #DCDFE5;
  padding: 15px 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.post__item__meta .meta__status__left {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.post__item__meta .meta__status__left h2 {
  margin-left: 20px;
  font-weight: normal;
  font-size: 12px;
  line-height: 22px;
  color: #596680;
}

@media screen and (max-width: 575px) {
  .post__item__meta .meta__status {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-align: start;
        -ms-flex-align: start;
            align-items: flex-start;
  }
  .post__item__meta .meta__status__left {
    margin-bottom: 10px;
  }
}

@media screen and (max-width: 375px) {
  .post__item__meta .meta__status__left {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-align: start;
        -ms-flex-align: start;
            align-items: flex-start;
  }
  .post__item__meta .meta__status__left h2 {
    margin-left: 0px;
    margin-top: 5px;
  }
}

.post__item__meta .meta__status .meta__user__list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-left: 15px;
}

.post__item__meta .meta__status .meta__user__list li {
  margin-left: -15px;
}

.post__item__meta .meta__status .meta__user__list li a {
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.post__item__meta .meta__status .meta__user__list li a img {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  border: 2px solid #ffffff;
}

.post__item__meta .meta__status .meta__status__list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-left: -20px;
}

.post__item__meta .meta__status .meta__status__list li {
  margin-left: 20px;
}

.post__item__meta .meta__status .meta__status__list li a {
  font-weight: 400;
  font-size: 12px;
  line-height: 22px;
  color: #596680;
}

.post__item__meta .meta__status .meta__status__list li a span {
  margin-right: 5px;
}

.post__item__meta .meta__list {
  margin-top: 17px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.post__item__meta .meta__list li:not(:first-child) {
  margin-left: 60px;
}

.post__item__meta .meta__list li button {
  border: 0;
  background-color: transparent;
}

.post__item__meta .meta__list li button span {
  margin-left: 8px;
  text-transform: capitalize;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  color: #596680;
}

@media screen and (max-width: 480px) {
  .post__item__meta .meta__list li:not(:first-child) {
    margin-left: 20px;
  }
}

.about-me__widget {
  padding: 26px 25px 30px;
  background: #F7F9FA;
  border-radius: 10px;
  margin-bottom: 30px;
}

.about-me__text {
  padding-bottom: 18px;
  border-bottom: 1px solid #DCDFE5;
}

.about-me__text h2 {
  margin-bottom: 20px;
  text-transform: capitalize;
  font-weight: 600;
  font-size: 20px;
  line-height: 24px;
  color: #273041;
}

.about-me__text p {
  margin-bottom: 0;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.about-me__text p:not(:first-child) {
  margin-top: 20px;
}

.about-me__list {
  padding-top: 25px;
}

.about-me__list ul {
  margin-top: -15px;
}

.about-me__list ul li {
  margin-top: 15px;
}

.about-me__list ul .list-item i {
  margin-right: 10px;
  font-size: 20px;
  color: #8E9AB2;
  width: 20px;
  height: 20px;
}

.about-me__list ul .list-item span {
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.about-me__button {
  margin-top: 20px;
}

.about-me__button .btn.btn-blue {
  font-size: 15px;
  font-weight: 500;
  line-height: 18px;
}

.contact__widget {
  background: #F7F9FA;
  border-radius: 10px;
  padding: 26px 15px 30px;
  margin-bottom: 30px;
}

.contact__widget__top {
  margin-bottom: 13px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.contact__widget__top__left h2 {
  font-weight: 600;
  font-size: 20px;
  line-height: 24px;
  color: #273041;
}

.contact__widget__list {
  height: 405px;
  overflow-y: auto;
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.contact__widget__list::-webkit-scrollbar {
  display: none;
}

.contact__widget__list ul .list-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.contact__widget__list ul .list-item .user-img {
  margin-right: 15px;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 45px;
          flex: 0 0 45px;
}

.contact__widget__list ul .list-item .user-img img {
  width: 45px;
  height: 45px;
  border-radius: 50%;
}

.contact__widget__list ul .list-item .user-name h2 {
  text-transform: capitalize;
  font-weight: 500;
  font-size: 14px;
  line-height: 17px;
  color: #273041;
}

.contact__widget__list .list-item {
  padding: 7px 10px;
}

.contact__widget__list .list-item:hover {
  background: #FFFFFF;
  -webkit-box-shadow: 0px 6px 10px rgba(206, 210, 217, 0.15);
          box-shadow: 0px 6px 10px rgba(206, 210, 217, 0.15);
  border-radius: 4px;
}

.contact__widget__list .list-item .user-img {
  position: relative;
}

.contact__widget__list .list-item .user-img .overlay {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 14px;
  height: 14px;
  border-radius: 50%;
  border: 2px solid #FFFFFF;
  background-color: #4CBF4C;
}

.gallery__widget {
  background: #F7F9FA;
  border-radius: 10px;
  padding: 26px 25px;
  margin-bottom: 30px;
}

.gallery__widget__top {
  margin-bottom: 20px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.gallery__widget__top__left h2 {
  text-transform: capitalize;
  font-weight: 600;
  font-size: 20px;
  line-height: 24px;
  color: #273041;
}

.gallery__widget__content {
  display: -ms-grid;
  display: grid;
  -ms-grid-columns: auto auto auto auto;
      grid-template-columns: auto auto auto auto;
  grid-column-gap: 7px;
  grid-row-gap: 7px;
}

.gallery__widget__content .gallery__img {
  border-radius: 4px;
  overflow: hidden;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.gallery__widget__content .gallery__img:hover {
  opacity: 0.8;
}

.gallery__widget__content .gallery__img img {
  width: 100%;
  height: 100%;
}

.gallery__widget__bottom {
  margin-top: 22px;
}

.gallery__widget__bottom a {
  text-transform: capitalize;
  font-weight: 600;
  font-size: 15px;
  line-height: 18px;
  color: #BE63F9;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.gallery__widget__bottom a:hover {
  opacity: 0.8;
}

.group__widget {
  background: #F7F9FA;
  border-radius: 10px;
  padding: 25px;
}

.group__widget__top {
  margin-bottom: 20px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.group__widget__top__left h2 {
  text-transform: capitalize;
  font-weight: 600;
  font-size: 20px;
  line-height: 24px;
  color: #273041;
}

.group__widget__content .group__item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.group__widget__content .group__item:not(:first-child) {
  margin-top: 15px;
}

.group__widget__content .group__item__left {
  margin-right: 15px;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 70px;
          flex: 0 0 70px;
  width: 70px;
  height: 70px;
  border-radius: 4px;
  overflow: hidden;
}

.group__widget__content .group__item__left img {
  width: 100%;
  height: 100%;
}

.group__widget__content .group__item__right h2 {
  margin-bottom: 7px;
  font-weight: 600;
  font-size: 15px;
  line-height: 18px;
  color: #273041;
}

.group__widget__content .group__item__right h3 {
  margin-bottom: 4px;
  font-weight: 400;
  font-size: 12px;
  line-height: 15px;
  color: #596680;
}

.group__widget__content .group__item__right p {
  margin-bottom: 0;
  font-weight: 400;
  font-size: 12px;
  line-height: 15px;
  color: rgba(89, 102, 128, 0.6);
}

.group__widget__bottom {
  margin-top: 22px;
}

.group__widget__bottom a {
  text-transform: capitalize;
  font-weight: 600;
  font-size: 15px;
  line-height: 18px;
  color: #BE63F9;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.group__widget__bottom a:hover {
  opacity: 0.8;
}

@media screen and (max-width: 767px) {
  .group__widget {
    margin-bottom: 30px;
  }
}

.invoice-list__item {
  background: #FFFFFF;
  border: 1px solid #E4E6EB;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  -webkit-box-shadow: 0px 10px 25px rgba(163, 177, 204, 0.1);
          box-shadow: 0px 10px 25px rgba(163, 177, 204, 0.1);
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 30px;
  height: calc(100% - 30px);
}

.invoice-list__top {
  text-align: center;
  padding-bottom: 25px;
  border-bottom: 1px solid #DCDFE5;
}

.invoice-list__top img {
  width: 60px;
  height: 60px;
  border-radius: 50%;
}

.invoice-list__top h2 {
  margin-top: 12px;
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  color: #273041;
}

.invoice-list__top h3 {
  margin-top: 3px;
  font-weight: 500;
  font-size: 12px;
  line-height: 15px;
  color: #596680;
}

.invoice-list__content {
  margin-top: 19px;
}

.invoice-list__content h3 {
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #4D88FF;
}

.invoice-list__content h2 {
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  color: #596680;
}

.invoice-list__meta {
  margin-top: 17px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.invoice-list__meta li:not(:first-child) {
  margin-left: 20px;
}

.invoice-list__meta .list-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.invoice-list__meta .list-item span {
  margin-left: 10px;
  font-weight: 500;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.invoice-preview__area {
  padding: 50px 40px 30px;
  margin-bottom: 40px;
}

.invoice-preview__top {
  margin-bottom: 33px;
}

.invoice-preview__top.preview {
  padding-bottom: 33px;
  border-bottom: 1px solid #ECEDF0;
  margin-bottom: 22px;
}

.invoice-preview__top .brand-img {
  display: block;
  margin-bottom: 23px;
}

.invoice-preview__top .brand-img img {
  width: 131px;
  height: 32px;
}

.invoice-preview__top .invoice-preview__top__text h2 {
  text-transform: capitalize;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.invoice-preview__content {
  margin-bottom: 40px;
  background: #F5F7FA;
  border-radius: 10px;
  padding: 23px 30px;
  display: -ms-grid;
  display: grid;
  -ms-grid-columns: auto auto auto auto;
      grid-template-columns: auto auto auto auto;
  grid-column-gap: 60px;
}

@media screen and (max-width: 991px) {
  .invoice-preview__content {
    -ms-grid-columns: auto auto;
        grid-template-columns: auto auto;
    grid-row-gap: 30px;
  }
}

@media screen and (max-width: 575px) {
  .invoice-preview__content {
    -ms-grid-columns: auto;
        grid-template-columns: auto;
  }
}

.invoice-preview__content .single-item h2 {
  margin-bottom: 10px;
  text-transform: capitalize;
  font-weight: 600;
  font-size: 32px;
  line-height: 40px;
  color: #273041;
}

.invoice-preview__content .single-item h3 {
  text-transform: capitalize;
  font-weight: 600;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.invoice-preview__content .single-item h4 {
  text-transform: capitalize;
  font-weight: normal;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.invoice-preview__content .single-item p {
  margin-bottom: 0;
  font-weight: normal;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.invoice-preview__table {
  width: 100%;
  overflow-x: auto;
}

.invoice-preview__table table {
  width: 100%;
}

.invoice-preview__table table thead th {
  padding: 13px 30px;
  text-transform: capitalize;
  font-weight: 500;
  font-size: 14px;
  line-height: 24px;
  color: #273041;
}

.invoice-preview__table table tbody td {
  padding: 16px 30px;
  border-bottom: 1px solid #ECEDF0;
}

.invoice-preview__table table tfoot td {
  padding: 8px 30px;
}

.invoice-preview__table table tfoot td:nth-child(1) {
  text-align: right;
  font-weight: 500;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.invoice-preview__table table tfoot td:nth-child(2) {
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #273041;
  text-align: right;
}

.invoice-preview__table table tfoot td:nth-child(2) input {
  text-align: right;
}

.invoice-preview__table table .product-info h2 {
  margin-bottom: 7px;
  text-transform: capitalize;
  font-weight: 500;
  font-size: 14px;
  line-height: 17px;
  color: #273041;
}

.invoice-preview__table table .product-info ul {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  text-align: center;
  margin-left: -20px;
}

.invoice-preview__table table .product-info ul li {
  margin-left: 20px;
}

.invoice-preview__table table .product-info .list-item {
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.invoice-preview__table table .product-info .list-item span {
  color: #273041;
}

.invoice-preview__table table .data-text {
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.invoice-preview__table table .total-data {
  font-weight: 600;
  font-size: 14px;
  line-height: 17px;
  text-align: right;
  color: #BE63F9;
}

.invoice-preview__bottom {
  margin-top: 57px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
  margin-left: -15px;
}

.invoice-preview__bottom a {
  margin-left: 15px;
  min-width: 120px;
}

@media screen and (max-width: 480px) {
  .invoice-preview__bottom {
    margin-top: 30px;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
  .invoice-preview__bottom a {
    margin-left: 15px;
    margin-top: 15px;
    width: 100%;
  }
}

.input__group-2 {
  display: -ms-grid;
  display: grid;
  -ms-grid-columns: 48% 48%;
      grid-template-columns: 48% 48%;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

@media screen and (max-width: 767px) {
  .input__group-2 {
    -ms-grid-columns: 100%;
        grid-template-columns: 100%;
  }
  .input__group-2 .input__group {
    margin-bottom: 10px;
  }
}

.input__group-3 {
  display: -ms-grid;
  display: grid;
  -ms-grid-columns: 32% 32% 32%;
      grid-template-columns: 32% 32% 32%;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

@media screen and (max-width: 767px) {
  .input__group-3 {
    -ms-grid-columns: 100%;
        grid-template-columns: 100%;
  }
  .input__group-3 .input__group {
    margin-bottom: 10px;
  }
}

.input__group input:focus {
  border: 1px solid #5991FF;
}

.input__group select {
  padding: 11px 16px 12px;
  background: #FFFFFF;
  border: 1px solid transparent;
  border-radius: 8px;
}

.input__group select:focus {
  border: 1px solid #5991FF;
}

.invoice-preview__table table tbody td {
  vertical-align: top;
}

.invoice-preview__table table tbody td:nth-child(1) {
  min-width: 20px;
}

.invoice-preview__table table tbody td:nth-child(2) {
  min-width: 280px;
}

.invoice-preview__table table tbody td:nth-child(3) {
  min-width: 150px;
}

.invoice-preview__table table tbody td:nth-child(4) {
  min-width: 150px;
}

.invoice-preview__table table tbody td:nth-child(5) {
  min-width: 150px;
}

.invoice-preview__table table tfoot td input {
  width: 100px;
}

.invoice-preview__table table td a {
  display: block;
  width: 20px;
  height: 20px;
}

.invoice-preview__table table input,
.invoice-preview__table table textarea {
  background: #FAFBFC;
  border: 1px solid #DFE3EB;
  border-radius: 8px;
  padding: 11px 16px 12px;
}

.invoice-preview__table table .product-input {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}

.invoice-preview__table table .product-input input {
  margin-bottom: 5px;
}

.invoice-preview__table table .product-input input, .invoice-preview__table table .product-input textarea {
  width: 270px;
}

.invoice-preview__table table .data-input input {
  width: 123px;
}

.invoice-preview__table table .add-new-product {
  margin-top: 10px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.blog__area {
  padding: 24px 25px 0px;
  margin-bottom: 30px;
  height: calc(100% - 30px);
}

.blog__area__top {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

.blog__area__top__left, .blog__area__top__right {
  margin-bottom: 35px;
}

.blog__area__top__right {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

@media screen and (max-width: 480px) {
  .blog__area__top {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-align: start;
        -ms-flex-align: start;
            align-items: flex-start;
  }
  .blog__area__top__left input {
    width: 100%;
  }
}

.blog__sidebar {
  padding: 20px 24px 25px;
  margin-bottom: 30px;
  height: calc(100% - 30px);
}

.blog__sidebar__top {
  margin-bottom: 31px;
}

.blog__sidebar__top h2 {
  font-weight: 600;
  font-size: 18px;
  line-height: 24px;
  color: #273041;
}

.blog__sidebar__divider {
  width: 100%;
  height: 1px;
  background-color: #DCDFE5;
  margin-bottom: 24px;
}

.blog__sidebar__item:last-child {
  margin-bottom: 0;
}

.blog__sidebar__item h2 {
  text-transform: capitalize;
  margin-bottom: 16px;
  font-style: normal;
  font-weight: 600;
  font-size: 14px;
  line-height: 24px;
  color: #273041;
}

.blog__sidebar__item.blog__sidebar__post h2 {
  margin-bottom: 14px;
}

.blog__sidebar__item.blog__sidebar__post .blog__sidebar__list li {
  margin-bottom: 14px;
}

.blog__sidebar__item.blog__sidebar__post .blog__sidebar__list li:last-child {
  margin-top: 21px;
  margin-bottom: 0;
}

.blog__sidebar__list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}

.blog__sidebar__list li {
  margin-bottom: 18px;
}

.blog__sidebar__list li:last-child {
  margin-bottom: 0;
}

.blog__sidebar__list .list-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.blog__sidebar__list .list-item .list-text {
  text-transform: capitalize;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.blog__sidebar__list .list-item .list-data {
  font-weight: 600;
  font-size: 10px;
  line-height: 12px;
  text-align: right;
  color: #BE63F9;
  background: #FBF5FF;
  border-radius: 9px;
  padding: 2px 5px 3px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

.blog__sidebar__list .add-new-item {
  font-weight: 500;
  font-size: 14px;
  line-height: 17px;
  color: #5991FF;
}

.list-item__post {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.list-item__post__left {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 70px;
          flex: 0 0 70px;
  margin-right: 15px;
}

.list-item__post__img {
  width: 70px;
  height: 70px;
  background: #C4C4C4;
  border-radius: 4px;
  overflow: hidden;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

.list-item__post__info a {
  font-weight: 600;
  font-size: 15px;
  line-height: 18px;
  color: #273041;
  margin-bottom: 7px;
}

.list-item__post__info h3 {
  margin-bottom: 4px;
  font-weight: 400;
  font-size: 12px;
  line-height: 15px;
  color: #596680;
}

.list-item__post__info p {
  margin-bottom: 0;
  font-weight: 400;
  font-size: 12px;
  line-height: 15px;
  color: rgba(89, 102, 128, 0.6);
}

.blog__area__content #grid-style {
  display: block;
}

.blog__area__content #list-style {
  display: none;
}

.blog__area__content.list #grid-style {
  display: none;
}

.blog__area__content.list #list-style {
  display: block;
}

.post__item {
  margin-bottom: 26px;
  background: #FFFFFF;
  border: 1px solid #DFE3EB;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  border-radius: 10px;
  overflow: hidden;
}

.post__img {
  background-color: #DFE3EB;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

.post__content {
  padding: 14px 16px;
}

.post__content .post__meta {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.post__content .post__meta .meta-item span {
  margin-left: 6px;
  font-weight: 400;
  font-size: 12px;
  line-height: 15px;
  color: rgba(89, 102, 128, 0.6);
}

.post__content .post__text {
  margin-top: 11px;
}

.post__content .post__button {
  margin-top: 13px;
}

.post-title {
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  color: #273041;
  margin-bottom: 2px;
}

.post-desc {
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
  margin-bottom: 10px;
}

.post-date {
  font-weight: 400;
  font-size: 12px;
  line-height: 15px;
  color: #596680;
}

.post-link {
  font-weight: 500;
  font-size: 14px;
  line-height: 17px;
  color: #5991FF;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.post-link span {
  margin-left: 6px;
}

.post-link:hover {
  color: #5991FF;
  letter-spacing: 0.5px;
}

.post__item.list-view {
  margin-bottom: 25px;
  background: #FFFFFF;
  border: 1px solid #DFE3EB;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  border-radius: 10px;
  display: -ms-grid;
  display: grid;
  -ms-grid-columns: 265px auto;
      grid-template-columns: 265px auto;
  grid-column-gap: 16px;
}

.post__item.list-view .post__item__left {
  width: 100%;
  height: 100%;
}

.post__item.list-view .post__item__left .post__item__img {
  width: 100%;
  height: 100%;
}

.post__item.list-view .post__item__left .post__item__img img {
  width: 100%;
  height: 100%;
}

.post__item.list-view .post__item__right {
  padding: 20px 16px 20px 0;
}

.post__item.list-view .post__item__content .post-title {
  margin-bottom: 5px;
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  color: #273041;
}

.post__item.list-view .post__item__content p {
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
  margin-bottom: 11px;
}

.post__item.list-view .post__item__content h3 {
  font-weight: 400;
  font-size: 12px;
  line-height: 15px;
  color: #596680;
}

.post__item.list-view .post__item__content .post__button {
  margin-top: 20px;
}

@media screen and (max-width: 575px) {
  .post__item.list-view {
    -ms-grid-columns: auto;
        grid-template-columns: auto;
    grid-column-gap: 16px;
  }
  .post__item.list-view .post__item__left {
    width: 100%;
  }
  .post__item.list-view .post__item__right {
    padding: 20px 16px;
  }
}

.blog__details__area {
  margin-bottom: 30px;
  padding: 31px 30px;
}

.blog__details__meta {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.blog__details__meta h3,
.blog__details__meta h2 {
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
  margin-bottom: 24px;
}

@media screen and (max-width: 480px) {
  .blog__details__meta {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
}

.blog__Details__title h2 {
  font-weight: 600;
  font-size: 24px;
  line-height: 34px;
  color: #273041;
  margin-bottom: 27px;
}

.blog__details__img {
  margin-bottom: 23px;
  background: #F8F9FA;
  border-radius: 12px;
  overflow: hidden;
}

.blog__details__img img {
  width: 100%;
}

.blog__details__content p {
  margin: 16px 0;
  font-weight: 400;
  font-size: 14px;
  line-height: 26px;
  color: #596680;
}

.blog__details__content blockquote {
  max-width: 496px;
  padding-left: 25px;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  position: relative;
}

@media screen and (max-width: 575px) {
  .blog__details__content blockquote {
    max-width: 100%;
  }
}

.blog__details__content blockquote::before {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  width: 5px;
  height: 100%;
  background-color: #DCDFE5;
}

.blog__details__content blockquote span {
  margin-top: 16px;
  padding-top: 4px;
  position: relative;
}

.blog__details__content blockquote span::before {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  width: 100px;
  height: 1px;
  background-color: #DCDFE5;
}

.blog__details__content ul {
  margin-left: 13px;
  list-style: disc;
}

.blog__details__content ul li {
  font-weight: 400;
  font-size: 14px;
  line-height: 26px;
  color: #596680;
}

.main-content__area {
  min-height: 100vh;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  padding: 50px 0;
}

.main-content__area.bg-img {
  background-image: url("../images/background/bg-img.png");
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center center;
}

.authentication__item {
  padding: 60px;
  background: #FFFFFF;
  border-radius: 12px;
}

@media screen and (max-width: 575px) {
  .authentication__item {
    padding: 30px 25px;
  }
}

.authentication__item__logo {
  margin-bottom: 32px;
}

.authentication__item__logo a img {
  width: 147px;
  height: 36px;
}

.authentication__item__title h2 {
  margin-bottom: 5px;
  text-transform: capitalize;
  font-weight: 600;
  font-size: 32px;
  line-height: 40px;
  color: #273041;
}

.authentication__item__title h3 {
  font-weight: 500;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.authentication__item__title h3 a {
  text-transform: capitalize;
  color: #5991FF;
}

.authentication__item__content .input__group .input-overlay {
  position: relative;
}

.authentication__item__content .input__group .input-overlay .overlay {
  position: absolute;
  top: 50%;
  left: 14px;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}

.authentication__item__content .input__group .input-overlay .overlay img {
  width: 20px;
  height: 20px;
}

.authentication__item__content .input__group .input-overlay .password-visibility {
  position: absolute;
  top: 50%;
  right: 14px;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  cursor: pointer;
}

.authentication__item__content .input__group .input-overlay .password-visibility.show::before {
  opacity: 1;
}

.authentication__item__content .input__group .input-overlay .password-visibility::before {
  position: absolute;
  content: "";
  top: 0;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  width: 1px;
  height: 100%;
  -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
  background-color: #8F95B2;
  opacity: 0;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.authentication__item__content .input__group .input-overlay .password-visibility img {
  width: 20px;
  height: 20px;
}

.authentication__item__content .input__group label {
  margin-bottom: 11px;
  font-weight: 500;
  font-size: 14px;
  line-height: 17px;
  color: #273041;
}

.authentication__item__content .input__group input {
  padding: 13px 13px 13px 49px;
  width: 100%;
  background: #FAFBFC;
  border: 1px solid #DFE3EB;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  border-radius: 10px;
}

.authentication__item__content .input__group input:focus {
  border: 1px solid #5991FF;
  -webkit-box-shadow: 0px 0px 2px rgba(89, 145, 255, 0.7);
          box-shadow: 0px 0px 2px rgba(89, 145, 255, 0.7);
}

.authentication__item__content .input__group input:focus + .overlay img {
  -webkit-filter: invert(67%) sepia(44%) saturate(7028%) hue-rotate(201deg) brightness(102%) contrast(101%);
          filter: invert(67%) sepia(44%) saturate(7028%) hue-rotate(201deg) brightness(102%) contrast(101%);
}

.authentication__item__content .input__group .continue-text {
  display: block;
  position: relative;
}

.authentication__item__content .input__group .continue-text::before {
  position: absolute;
  content: "";
  z-index: 0;
  top: 50%;
  left: 0;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  width: 100%;
  height: 1px;
  background: #DCDFE5;
}

.authentication__item__content .input__group .continue-text h2 {
  position: relative;
  z-index: 1;
  margin: 0 auto;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  padding: 2px 5px;
  background: #ffffff;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.authentication__item__content .item__group__between {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

@media screen and (max-width: 480px) {
  .authentication__item__content .item__group__between {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-align: start;
        -ms-flex-align: start;
            align-items: flex-start;
  }
  .authentication__item__content .item__group__between .input__group:not(:first-child) {
    margin-top: 15px;
  }
}

.authentication__item__content .item__group__between .input__group {
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.authentication__item__content .item__group__between .input__group label {
  margin-bottom: 0;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.authentication__item__content .item__group__between .input__group input {
  margin-right: 12px;
  width: 16px;
  height: 16px;
}

.authentication__item__content .item__group__between a {
  text-transform: capitalize;
  font-weight: 500;
  font-size: 14px;
  line-height: 24px;
  color: #BE63F9;
}

.authentication__modern {
  background: #FFFFFF;
  border: 1px solid #E4E6EB;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  -webkit-box-shadow: 0px 10px 25px rgba(163, 177, 204, 0.1);
          box-shadow: 0px 10px 25px rgba(163, 177, 204, 0.1);
  border-radius: 12px;
  overflow: hidden;
  display: -ms-grid;
  display: grid;
  -ms-grid-columns: 40% 60%;
      grid-template-columns: 40% 60%;
}

@media screen and (max-width: 1199px) {
  .authentication__modern {
    -ms-grid-columns: 45% 55%;
        grid-template-columns: 45% 55%;
  }
}

@media screen and (max-width: 991px) {
  .authentication__modern {
    -ms-grid-columns: 100%;
        grid-template-columns: 100%;
  }
}

.authentication__modern .item-left {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.authentication__modern .authentication__item {
  width: 100%;
  padding: 70px 40px;
}

.modern__content {
  height: 100%;
  background-image: url("../images/background/bg-form.png");
  background-repeat: no-repeat;
  background-size: cover;
  padding: 90px 80px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

@media screen and (max-width: 1199px) {
  .modern__content {
    padding: 0 40px;
  }
}

@media screen and (max-width: 991px) {
  .modern__content {
    display: none;
  }
}

.form-img {
  margin-bottom: 50px;
}

.form-text h2 {
  margin-bottom: 15px;
  font-weight: 700;
  font-size: 36px;
  line-height: 44px;
  color: #FFFFFF;
}

.form-text p {
  margin-bottom: 0;
  font-weight: 400;
  font-size: 16px;
  line-height: 28px;
  color: #FFFFFF;
}

@media screen and (max-width: 1199px) {
  .form-text h2 {
    font-size: 30px;
  }
}

.split-content__area {
  min-height: 100vh;
  display: -ms-grid;
  display: grid;
  -ms-grid-columns: 50% 50%;
      grid-template-columns: 50% 50%;
}

.split-content__area__left {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
}

.split-content__area .authentication__item {
  width: 80%;
  background-color: transparent;
}

@media screen and (max-width: 991px) {
  .split-content__area {
    -ms-grid-columns: 100%;
        grid-template-columns: 100%;
  }
  .split-content__area__left {
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
  }
  .split-content__area .authentication__item {
    width: 70%;
  }
}

@media screen and (max-width: 767px) {
  .split-content__area .authentication__item {
    width: 100%;
  }
}

.full-content__area {
  min-height: 100vh;
  display: -ms-grid;
  display: grid;
  -ms-grid-columns: 40% 60%;
      grid-template-columns: 40% 60%;
}

.full-content__area__left {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
}

.full-content__area .authentication__item {
  width: 100%;
  background-color: transparent;
}

@media screen and (max-width: 991px) {
  .full-content__area {
    -ms-grid-columns: 100%;
        grid-template-columns: 100%;
  }
  .full-content__area__left {
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
  }
  .full-content__area .authentication__item {
    width: 70%;
  }
}

@media screen and (max-width: 767px) {
  .full-content__area .authentication__item {
    width: 100%;
  }
}

.faq__area {
  padding: 73px 150px 50px;
  margin-bottom: 40px;
}

@media screen and (max-width: 991px) {
  .faq__area {
    padding: 50px 100px 50px;
  }
}

@media screen and (max-width: 767px) {
  .faq__area {
    padding: 50px;
  }
}

@media screen and (max-width: 480px) {
  .faq__area {
    padding: 25px;
  }
}

.faq__area__heading {
  margin-bottom: 38px;
  text-align: center;
}

.faq__area__heading h2 {
  margin-bottom: 16px;
  text-transform: capitalize;
  font-weight: 600;
  font-size: 24px;
  line-height: 32px;
  color: #273041;
}

.faq__area__heading p {
  margin-bottom: 0;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.faq__item:not(:first-child) {
  margin-top: 45px;
}

.faq__item__title {
  margin-bottom: 25px;
  text-align: center;
}

.faq__item__title h2 {
  text-transform: capitalize;
  font-weight: 600;
  font-size: 18px;
  line-height: 24px;
  color: #273041;
}

.faq__item .accordion-item {
  background-color: #FAFBFC;
  border: 1px solid #EBEDF0;
  border-radius: 10px;
  font-weight: 400;
  font-size: 16px;
  line-height: 28px;
  color: #273041;
}

.faq__item .accordion-item:not(:first-child) {
  margin-top: 10px;
}

.faq__item .accordion-item .accordion-button {
  padding: 16px 25px;
  background-color: #FAFBFC;
  border: 0;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  border-bottom-left-radius: 10px !important;
  border-bottom-right-radius: 10px !important;
  font-weight: 400;
  font-size: 16px;
  line-height: 28px;
  color: #273041;
}

.faq__item .accordion-item .accordion-button:not(.collapsed) {
  color: #5991FF;
  -webkit-box-shadow: none;
          box-shadow: none;
  background-color: transparent;
}

.faq__item .accordion-item .accordion-button:focus {
  -webkit-box-shadow: 0 0 0 0 rgba(13, 110, 253, 0.25);
          box-shadow: 0 0 0 0 rgba(13, 110, 253, 0.25);
}

.faq__item .accordion-item .accordion-body {
  border-top: 1px solid #E7E8EB;
}

.testimonial__area {
  padding: 0 30px 60px;
  margin-bottom: 40px;
}

.testimonial__item {
  margin-top: 73px;
}

.testimonial__item > h2 {
  margin-bottom: 34px;
  text-transform: capitalize;
  font-weight: 600;
  font-size: 24px;
  line-height: 32px;
  text-align: center;
  color: #273041;
}

.testimonial__slider__one .slick-arrow {
  position: absolute;
  z-index: 99;
  top: 88%;
}

.testimonial__slider__one .slick-arrow::before {
  content: "";
}

.testimonial__slider__one .slick-prev.slick-arrow {
  left: 30%;
}

.testimonial__slider__one .slick-next.slick-arrow {
  right: 30%;
}

.testimonial__slider__one .slider__item {
  background: #F7F9FA;
  border-radius: 10px;
  padding: 40px 32px;
  position: relative;
}

@media screen and (max-width: 480px) {
  .testimonial__slider__one .slider__item {
    padding: 20px 10px;
  }
}

.testimonial__slider__one .slider__item .overlay {
  position: absolute;
  top: 40px;
  left: 40px;
}

.testimonial__slider__one .slider__item .overlay img {
  width: 62px;
  height: 54px;
}

@media screen and (max-width: 480px) {
  .testimonial__slider__one .slider__item .overlay {
    top: 20px;
    left: 20px;
  }
}

.testimonial__slider__one .slider__item__img img {
  width: 86px;
  height: 86px;
  border-radius: 50%;
  -webkit-box-shadow: 0px 8px 20px rgba(58, 68, 89, 0.15);
          box-shadow: 0px 8px 20px rgba(58, 68, 89, 0.15);
  border: 3px solid #ffffff;
  margin: 0 auto 19px;
}

.testimonial__slider__one .slider__item h2 {
  text-align: center;
  text-transform: capitalize;
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  color: #273041;
}

.testimonial__slider__one .slider__item h3 {
  margin-bottom: 20px;
  text-align: center;
  text-transform: capitalize;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.testimonial__slider__one .slider__item p {
  margin-bottom: 80px;
  text-align: center;
  font-weight: 400;
  font-size: 16px;
  line-height: 28px;
  color: #596680;
}

.testimonial__slider__two .slick-arrow {
  position: absolute;
  z-index: 9;
  background: #FFFFFF;
  -webkit-box-shadow: 0px 6px 10px rgba(58, 68, 89, 0.1);
          box-shadow: 0px 6px 10px rgba(58, 68, 89, 0.1);
  width: 46px;
  height: 46px;
  border-radius: 50%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

.testimonial__slider__two .slick-arrow::before {
  content: "";
}

.testimonial__slider__two .slider__item {
  background: #F7F9FA;
  border-radius: 10px;
  padding: 32px 53px 40px;
  text-align: center;
}

@media screen and (max-width: 480px) {
  .testimonial__slider__two .slider__item {
    padding: 20px 10px;
  }
}

.testimonial__slider__two .slider__item p {
  margin-bottom: 26px;
  font-weight: 400;
  font-size: 16px;
  line-height: 28px;
  color: #596680;
}

.testimonial__slider__two .slider__item h2 {
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  color: #273041;
}

.testimonial__slider__two .slider__item h3 {
  margin-bottom: 23px;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.testimonial__slider__two .slider__item__img img {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: #C4C4C4;
  margin: 0 auto;
}

.testimonial__slider__three .slider__item {
  background: #FAFBFC;
  border: 1px solid #EBEDF0;
  border-radius: 10px;
  padding: 40px 35px 38px;
}

.testimonial__slider__three .slider__item img {
  width: 62px;
  height: 54px;
  margin-bottom: 24px;
}

.testimonial__slider__three .slider__item h2 {
  margin-bottom: 16px;
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  color: #273041;
}

.testimonial__slider__three .slider__item p {
  margin-bottom: 0;
  font-weight: 400;
  font-size: 16px;
  line-height: 28px;
  color: #596680;
}

@media screen and (max-width: 480px) {
  .testimonial__slider__three .slider__item {
    padding: 20px;
  }
}

.testimonial__slider__three__nav {
  width: 100%;
  height: 305px;
}

@media screen and (max-width: 767px) {
  .testimonial__slider__three__nav {
    margin-bottom: 30px;
  }
}

.testimonial__slider__three__nav .swiper-slide .slider__item {
  background: #FAFBFC;
  border: 1px solid #EBEDF0;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  border-radius: 10px;
  padding: 15px;
  display: -webkit-box !important;
  display: -ms-flexbox !important;
  display: flex !important;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.testimonial__slider__three__nav .swiper-slide .slider__item__img {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 65px;
          flex: 0 0 65px;
  margin-right: 20px;
}

.testimonial__slider__three__nav .swiper-slide .slider__item__img img {
  width: 65px;
  height: 65px;
  border-radius: 50%;
}

.testimonial__slider__three__nav .swiper-slide .slider__item__text h2 {
  margin-bottom: 3px;
  text-transform: capitalize;
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  color: #273041;
}

.testimonial__slider__three__nav .swiper-slide .slider__item .rattings {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.testimonial__slider__three__nav .swiper-slide .slider__item .rattings h2 {
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.testimonial__slider__three__nav .swiper-slide .slider__item .rattings li:not(:last-child) {
  margin-right: 3px;
}

.testimonial__slider__three__nav .swiper-slide .slider__item .rattings li:first-child {
  margin-right: 9px;
}

.testimonial__slider__three__nav .swiper-slide .slider__item .rattings li i {
  font-weight: 900;
  font-size: 12px;
  line-height: 27px;
  color: #FF9F38;
}

@media screen and (max-width: 480px) {
  .testimonial__slider__three__nav .swiper-slide .slider__item {
    padding: 12px;
  }
  .testimonial__slider__three__nav .swiper-slide .slider__item__img {
    -webkit-box-flex: 0;
        -ms-flex: 0 0 50px;
            flex: 0 0 50px;
    margin-right: 10px;
  }
  .testimonial__slider__three__nav .swiper-slide .slider__item__img img {
    width: 50px;
    height: 50px;
  }
}

.testimonial__slider__three__nav .swiper-slide.swiper-slide-thumb-active .slider__item {
  background: #FFFFFF;
  border: 1px solid #5991FF;
  -webkit-box-shadow: 0px 4px 10px rgba(101, 110, 128, 0.15);
          box-shadow: 0px 4px 10px rgba(101, 110, 128, 0.15);
}

.testimonial__slider__four .slick-dots li {
  width: 15px;
  height: 15px;
  border-radius: 50%;
  background-color: #CFD6E5;
}

.testimonial__slider__four .slick-dots li.slick-active {
  background-color: #5991FF;
}

.testimonial__slider__four .slick-dots li button::before {
  display: none;
}

.testimonial__slider__four .slider__item {
  background: #FFFFFF;
  border-radius: 8px;
  padding: 40px 35px 33px;
  text-align: center;
  position: relative;
}

@media screen and (max-width: 480px) {
  .testimonial__slider__four .slider__item {
    padding: 10px;
  }
}

.testimonial__slider__four .slider__item .overlay {
  position: absolute;
  top: 40px;
  left: 40px;
}

.testimonial__slider__four .slider__item .overlay img {
  width: 62px;
  height: 54px;
}

@media screen and (max-width: 480px) {
  .testimonial__slider__four .slider__item .overlay {
    top: 10px;
    left: 10px;
  }
}

.testimonial__slider__four .slider__item .overlay-bottom {
  position: absolute;
  bottom: 30px;
  right: 30px;
}

@media screen and (max-width: 480px) {
  .testimonial__slider__four .slider__item .overlay-bottom {
    position: unset;
  }
}

.testimonial__slider__four .slider__item__img img {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: #C4C4C4;
  -webkit-box-shadow: 0px 10px 20px rgba(207, 214, 230, 0.8);
          box-shadow: 0px 10px 20px rgba(207, 214, 230, 0.8);
  margin: 0 auto 22px;
}

.testimonial__slider__four .slider__item p {
  margin-bottom: 26px;
  font-weight: 400;
  font-size: 16px;
  line-height: 28px;
  color: #596680;
}

.testimonial__slider__four .slider__item h2 {
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  color: #273041;
}

.testimonial__slider__four .slider__item h3 {
  margin-bottom: 23px;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.swiper.testimonial__slider__four .swiper-slide {
  -webkit-box-shadow: 0px 15px 30px rgba(58, 68, 89, 0.06);
          box-shadow: 0px 15px 30px rgba(58, 68, 89, 0.06);
  opacity: 0.6;
  border-radius: 8px;
}

.swiper.testimonial__slider__four .swiper-slide-active {
  -webkit-box-shadow: 0px 15px 30px rgba(58, 68, 89, 0.06);
          box-shadow: 0px 15px 30px rgba(58, 68, 89, 0.06);
  opacity: 1;
}

.swiper.testimonial__slider__four .swiper-pagination {
  margin-top: 30px;
  position: unset;
}

.swiper.testimonial__slider__four .swiper-pagination .swiper-pagination-bullet {
  width: 15px;
  height: 15px;
  border-radius: 50%;
  background: #CFD6E5;
  opacity: 1;
}

.swiper.testimonial__slider__four .swiper-pagination .swiper-pagination-bullet-active {
  background: #5991FF;
}

.comming-soon {
  width: 100%;
  min-height: 100vh;
  padding: 50px 0;
  background-image: url("../images/background/bg-comming-soon.png");
  background-repeat: no-repeat;
  background-size: cover;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.comming-soon__content {
  text-align: center;
}

.comming-soon__content .item-img {
  margin-bottom: 56px;
}

.comming-soon__content .item-text {
  margin-bottom: 35px;
}

.comming-soon__content .item-text h2 {
  margin-bottom: 2px;
  font-weight: 800;
  font-size: 50px;
  line-height: 65px;
  color: #FFFFFF;
}

.comming-soon__content .item-text p {
  margin-bottom: 0;
  font-weight: 600;
  font-size: 18px;
  line-height: 30px;
  color: #FFFFFF;
}

@media screen and (max-width: 575px) {
  .comming-soon__content .item-text h2 {
    font-size: 40px;
  }
}

@media screen and (max-width: 480px) {
  .comming-soon__content .item-text h2 {
    font-size: 26px;
    line-height: 35px;
  }
  .comming-soon__content .item-text p {
    font-size: 14px;
  }
}

.comming-soon__content .countdown {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

.comming-soon__content .countdown .countdown-item:not(:first-child) {
  margin-left: 120px;
}

@media screen and (max-width: 767px) {
  .comming-soon__content .countdown .countdown-item:not(:first-child) {
    margin-left: 30px;
  }
}

.comming-soon__content .countdown .countdown-item h2 {
  margin-bottom: 6px;
  text-transform: capitalize;
  font-weight: 600;
  font-size: 24px;
  line-height: 32px;
  color: #FFFFFF;
}

.comming-soon__content .countdown .countdown-item p {
  margin-bottom: 0;
  font-weight: 400;
  font-size: 16px;
  line-height: 28px;
  color: #FFFFFF;
}

@media screen and (max-width: 480px) {
  .comming-soon__content .countdown .countdown-item h2 {
    font-size: 20px;
  }
  .comming-soon__content .countdown .countdown-item p {
    font-size: 14px;
  }
}

.basic-tables__area {
  padding: 25px 25px 30px;
  margin-bottom: 30px;
}

.basic-tables__area .item-title {
  margin-bottom: 26px;
}

.basic-tables__area .item-title h2 {
  text-transform: capitalize;
  font-weight: 600;
  font-size: 18px;
  line-height: 24px;
  color: #273041;
}

.basic-example__table {
  width: 100%;
  overflow-x: auto;
}

.basic-example__table table {
  width: 100%;
}

.basic-example__table table thead th {
  text-transform: capitalize;
  font-weight: 500;
  font-size: 14px;
  line-height: 17px;
  color: #273041;
  padding: 14px 15px;
  border-bottom: 1px solid #ECEDF0;
}

.basic-example__table table tbody td {
  padding: 19px 15px;
  border-bottom: 1px solid #ECEDF0;
}

.basic-example__table table tbody td:nth-child(1) {
  min-width: 50px;
}

.basic-example__table table tbody td:nth-child(2) {
  min-width: 150px;
}

.basic-example__table table tbody td:nth-child(3) {
  min-width: 100px;
}

.basic-example__table table tbody td:nth-child(4) {
  min-width: 200px;
}

.basic-example__table table tbody td:nth-child(5) {
  min-width: 200px;
}

.basic-example__table table tbody td:nth-child(6) {
  min-width: 220px;
}

.basic-example__table table.table-style thead th {
  border-bottom: 0;
}

.basic-example__table table.striped-style tbody tr:nth-child(odd) {
  background-color: #F7F9FA;
}

.basic-example__table table.bordered-style thead th,
.basic-example__table table.bordered-style tbody td {
  border: 1px solid #ECEDF0;
}

.basic-example__table table.borderless-style thead th,
.basic-example__table table.borderless-style tbody td {
  border: 0;
}

.basic-example__table table.hover-style tbody tr:hover {
  background: #FFFFFF;
  -webkit-box-shadow: 0px 0px 15px rgba(161, 167, 179, 0.2);
          box-shadow: 0px 0px 15px rgba(161, 167, 179, 0.2);
}

.basic-example__table table.dark-style thead th {
  background-color: #2D3340;
  padding: 19px 15px;
  color: #ffffff;
  border-bottom: 1px solid #393F4D;
}

.basic-example__table table.dark-style thead th:first-child {
  border-top-left-radius: 10px;
}

.basic-example__table table.dark-style thead th:last-child {
  border-top-right-radius: 10px;
}

.basic-example__table table.dark-style tbody td {
  background-color: #2D3340;
  border-bottom: 1px solid #393F4D;
}

.basic-example__table table.dark-style tbody tr:last-child td {
  border-bottom: 0;
}

.basic-example__table table.dark-style tbody tr:last-child td:first-child {
  border-bottom-left-radius: 10px;
}

.basic-example__table table.dark-style tbody tr:last-child td:last-child {
  border-bottom-right-radius: 10px;
}

.basic-example__table table .data-text {
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.basic-example__table table .customer-name,
.basic-example__table table .gender {
  text-transform: capitalize;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.basic-example__table table.dark-style .data-text,
.basic-example__table table.dark-style .customer-name,
.basic-example__table table.dark-style .gender {
  color: #B8BECC;
}

.responsive__table {
  width: 100%;
  overflow-x: auto;
}

.responsive__table table {
  width: 100%;
}

.responsive__table table thead th {
  text-transform: capitalize;
  font-weight: 500;
  font-size: 14px;
  line-height: 17px;
  color: #273041;
  padding: 14px 15px;
  border-bottom: 1px solid #ECEDF0;
}

.responsive__table table tbody td {
  padding: 15px;
  border-bottom: 1px solid #ECEDF0;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.responsive__table table tbody td:nth-child(1) {
  min-width: 200px;
}

.responsive__table table tbody td:nth-child(2) {
  min-width: 100px;
}

.responsive__table table tbody td:nth-child(3) {
  min-width: 200px;
}

.responsive__table table tbody td:nth-child(4) {
  min-width: 150px;
}

.responsive__table table tbody td:nth-child(5) {
  min-width: 200px;
}

.responsive__table table tbody td:nth-child(6) {
  min-width: 150px;
}

.responsive__table table .form-control {
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.responsive__table table .form-control:focus {
  -webkit-box-shadow: none;
          box-shadow: none;
  border: 1px solid #5991FF;
}

.responsive__table table .data-text {
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.responsive__table table .customer-name,
.responsive__table table .gender {
  text-transform: capitalize;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.editable__table {
  width: 100%;
  overflow-x: auto;
}

.editable__table table {
  width: 100%;
}

.editable__table table thead th {
  text-transform: capitalize;
  font-weight: 500;
  font-size: 14px;
  line-height: 17px;
  color: #273041;
  padding: 14px 15px;
  border-bottom: 1px solid #ECEDF0;
}

.editable__table table thead th:nth-child(1) {
  padding: 0;
  width: 0px;
}

.editable__table table tbody td {
  padding: 4px 15px;
  border-bottom: 1px solid #ECEDF0;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.editable__table table tbody td:nth-child(1) {
  width: 0px;
}

.editable__table table tbody td:nth-child(2) {
  min-width: 150px;
}

.editable__table table tbody td:nth-child(3) {
  min-width: 200px;
}

.editable__table table tbody td:nth-child(4) {
  min-width: 150px;
}

.editable__table table tbody td:nth-child(5) {
  min-width: 300px;
}

.editable__table table tbody td:nth-child(6) {
  min-width: 150px;
}

.data-table__area {
  width: 100%;
  overflow: auto;
}

.data-table__area table {
  width: 100%;
}

.data-table__area table thead th {
  padding: 14px 15px;
  text-transform: capitalize;
  font-weight: 500;
  font-size: 14px;
  line-height: 17px;
  color: #273041;
}

.data-table__area table tbody td {
  padding: 14px 15px;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

table.dataTable thead th {
  border-bottom: 0;
}

table.dataTable.row-border tbody th,
table.dataTable.row-border tbody td,
table.dataTable.display tbody th,
table.dataTable.display tbody td {
  border-top: 0;
}

#project-list-table.dataTable tbody td,
#filter-table.dataTable thead th,
#filter-table.dataTable tbody td {
  border-bottom: 1px solid #ECEDF0;
}

#customers-table.dataTable thead .sorting,
#customers-table.dataTable thead .sorting_asc,
#customers-table.dataTable thead .sorting_desc,
#project-list-table.dataTable thead .sorting,
#project-list-table.dataTable thead .sorting_asc,
#project-list-table.dataTable thead .sorting_desc {
  background-image: none;
}

#filter-table tbody td:nth-child(1) {
  min-width: 150px;
}

#filter-table tbody td:nth-child(2) {
  min-width: 100px;
}

#filter-table tbody td:nth-child(3) {
  min-width: 200px;
}

#filter-table tbody td:nth-child(4) {
  min-width: 150px;
}

#filter-table tbody td:nth-child(5) {
  min-width: 200px;
}

table.dataTable.no-footer {
  border-bottom: 0;
}

.dataTables_length {
  margin-bottom: 25px;
}

.dataTables_length label {
  text-transform: capitalize;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.dataTables_length label select {
  margin: 0 8px;
  border: 0;
  padding: 12px;
  background: #F2F4F7;
  border-radius: 8px;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.dataTables_filter {
  margin-bottom: 25px;
}

.dataTables_filter label {
  text-transform: capitalize;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.dataTables_filter label input {
  border: 0;
  padding: 12px;
  background: #F2F4F7;
  border-radius: 8px;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

#customers-table_paginate,
#project-list-table_paginate {
  display: none;
}

@media screen and (max-width: 640px) {
  .dataTables_length,
  .dataTables_filter {
    text-align: left;
  }
}

.icon__item__area {
  padding: 25px 25px 30px;
  margin-bottom: 30px;
}

.icon__item__area .item-top-between {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.icon__item__area .item-top-between .item-top__left {
  width: 70%;
}

.icon__item__area .item-top-between .item-top__right {
  width: 25%;
}

@media screen and (max-width: 767px) {
  .icon__item__area .item-top-between .item-top__left {
    width: 58%;
  }
  .icon__item__area .item-top-between .item-top__right {
    width: 38%;
  }
}

@media screen and (max-width: 480px) {
  .icon__item__area .item-top-between {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
  .icon__item__area .item-top-between .item-top__left {
    width: 100%;
    margin-bottom: 20px;
  }
  .icon__item__area .item-top-between .item-top__right {
    width: 100%;
  }
}

.icon__item__area .item-top .input__group input,
.icon__item__area .item-top .input__group select,
.icon__item__area .item-top-between .input__group input,
.icon__item__area .item-top-between .input__group select {
  width: 100%;
  background: #F2F4F7;
  border-radius: 10px;
  border: 1px solid transparent;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.icon__item__area .item-top .input__group input:focus,
.icon__item__area .item-top .input__group select:focus,
.icon__item__area .item-top-between .input__group input:focus,
.icon__item__area .item-top-between .input__group select:focus {
  border: 1px solid #5991FF;
}

.icon__item__area .item-top .input__group .input-overlay input:focus + .overlay img,
.icon__item__area .item-top-between .input__group .input-overlay input:focus + .overlay img {
  -webkit-filter: none;
          filter: none;
}

.icon__item__top h2 {
  margin-bottom: 15px;
  text-transform: capitalize;
  font-weight: 600;
  font-size: 18px;
  line-height: 24px;
  color: #273041;
}

.icon__item__grid {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin: -20px 0 0 -20px;
}

.icon__item__grid .single-item {
  margin: 20px 0 0 20px;
  width: calc(100% / 6 - 20px);
}

@media screen and (max-width: 991px) {
  .icon__item__grid .single-item {
    width: calc(100% / 5 - 20px);
  }
}

@media screen and (max-width: 767px) {
  .icon__item__grid .single-item {
    width: calc(100% / 4 - 20px);
  }
}

@media screen and (max-width: 575px) {
  .icon__item__grid .single-item {
    width: calc(100% / 3 - 20px);
  }
}

@media screen and (max-width: 480px) {
  .icon__item__grid .single-item {
    width: calc(100% / 2 - 20px);
  }
}

@media screen and (max-width: 375px) {
  .icon__item__grid .single-item {
    width: calc(100% - 20px);
  }
}

.icon__item__grid .single-item {
  background: #FAFBFC;
  border: 1px solid #EBEDF0;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  border-radius: 10px;
  padding: 35px 7px;
  text-align: center;
}

.icon__item__grid .single-item i {
  font-size: 20px;
  color: #596680;
}

.icon__item__grid .single-item svg {
  width: 20px;
  height: 20px;
  color: #596680;
}

.icon__item__grid .single-item img {
  width: 20px;
  height: 20px;
}

.icon__item__grid .single-item p {
  margin-top: 12px;
  margin-bottom: 0;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.setting__area {
  padding: 30px;
  margin-bottom: 40px;
}

.setting__content .nav.nav-tabs {
  border-bottom: 2px solid #EBEDF2;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.setting__content .nav.nav-tabs .nav-link {
  border: 2px solid transparent;
  padding: 0 16px 12px !important;
  text-transform: capitalize;
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  color: #596680;
}

.setting__content .nav.nav-tabs .nav-link img {
  width: 24px;
  height: 24px;
  margin-right: 10px;
}

.setting__content .nav.nav-tabs .nav-link.active {
  border-bottom: 2px solid #5991FF;
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  color: #5991FF;
}

.setting__content .nav.nav-tabs .nav-link.active img {
  -webkit-filter: invert(54%) sepia(45%) saturate(2864%) hue-rotate(199deg) brightness(99%) contrast(104%);
          filter: invert(54%) sepia(45%) saturate(2864%) hue-rotate(199deg) brightness(99%) contrast(104%);
}

.setting__content .tab-content {
  margin-top: 24px;
}

.setting__content .item-title h2 {
  margin-bottom: 4px;
  text-transform: capitalize;
  font-weight: 600;
  font-size: 18px;
  line-height: 24px;
  color: #273041;
}

.setting__content .item-title p {
  margin-bottom: 0;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.setting__content .tab-content .user__info {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

@media screen and (max-width: 480px) {
  .setting__content .tab-content .user__info {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-align: start;
        -ms-flex-align: start;
            align-items: flex-start;
  }
}

.setting__content .tab-content .user-img {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 80px;
          flex: 0 0 80px;
  margin-right: 40px;
  position: relative;
}

.setting__content .tab-content .user-img img {
  width: 80px;
  height: 80px;
  border-radius: 50%;
}

@media screen and (max-width: 480px) {
  .setting__content .tab-content .user-img {
    margin-right: 0;
    margin-bottom: 30px;
  }
}

.setting__content .tab-content .user-img .overlay {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 25px;
  height: 25px;
  border-radius: 50%;
  background-color: #BE63F9;
  border: 1px solid #ffffff;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

.setting__content .tab-content .user-img .overlay label {
  cursor: pointer;
}

.setting__content .tab-content .user-img .overlay label img {
  width: 14px;
}

.setting__content .tab-content .user-img .overlay input {
  display: none;
}

.setting__content .tab-content .user-button a {
  background: #EBEDF0;
  border-radius: 8px;
  padding: 12px 20px;
  font-weight: 600;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.setting__content .input__button button {
  min-width: 140px;
}

@media screen and (max-width: 480px) {
  .setting__content .input__button {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
  .setting__content .input__button button {
    width: 100%;
  }
  .setting__content .input__button button:not(:first-child) {
    margin-top: 20px;
  }
}

.setting__item .list-title h2 {
  font-weight: 600;
  font-size: 14px;
  line-height: 24px;
  color: #273041;
}

.setting__item .input__group input,
.setting__item .input__group textarea,
.setting__item .input__group select {
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  color: #596680;
}

.setting__item .input__group__between.style-2 {
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
}

.setting__item .input__group__between.style-2 .error-msg {
  margin-top: 7px;
  font-weight: 500;
  font-size: 12px;
  line-height: 15px;
  color: #596680;
}

.setting__item .input__group .input-group {
  width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -ms-flex-wrap: nowrap;
      flex-wrap: nowrap;
}

.setting__item .input__group .input-group .input-group-text {
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
  background-color: #F0F4F7;
  padding: 11px 14px;
}

.setting__item .input__group .input-overlay {
  position: relative;
}

.setting__item .input__group .input-overlay .overlay {
  position: absolute;
  top: 50%;
  left: 14px;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}

.setting__item .input__group .input-overlay .overlay img {
  width: 20px;
  height: 20px;
}

.setting__item .input__group .input-overlay .password-visibility {
  position: absolute;
  top: 50%;
  right: 14px;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  cursor: pointer;
}

.setting__item .input__group .input-overlay .password-visibility.show::before {
  opacity: 1;
}

.setting__item .input__group .input-overlay .password-visibility::before {
  position: absolute;
  content: "";
  top: 0;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  width: 1px;
  height: 100%;
  -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
  background-color: #8F95B2;
  opacity: 0;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.setting__item .input__group .input-overlay .password-visibility img {
  width: 20px;
  height: 20px;
}

.setting__item .input__group label {
  margin-bottom: 11px;
  font-weight: 500;
  font-size: 14px;
  line-height: 17px;
  color: #273041;
}

.setting__item .input__group input {
  padding: 13px 13px 13px 49px;
  width: 100%;
  background: #FAFBFC;
  border: 1px solid #DFE3EB;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  border-radius: 10px;
}

.setting__item .input__group input:focus {
  border: 1px solid #5991FF;
  -webkit-box-shadow: 0px 0px 2px rgba(89, 145, 255, 0.7);
          box-shadow: 0px 0px 2px rgba(89, 145, 255, 0.7);
}

.setting__item .input__group input:focus + .overlay img {
  -webkit-filter: invert(67%) sepia(44%) saturate(7028%) hue-rotate(201deg) brightness(102%) contrast(101%);
          filter: invert(67%) sepia(44%) saturate(7028%) hue-rotate(201deg) brightness(102%) contrast(101%);
}

.setting__item .input__check:not(:first-child) {
  margin-top: 13px;
  border-top: 1px solid #DCDFE5;
  padding-top: 13px;
}

.setting__item .input__check .item-left h2 {
  font-weight: 600;
  font-size: 14px;
  line-height: 24px;
  color: #273041;
}

.setting__item .input__check .item-left p {
  margin-bottom: 0;
  font-weight: normal;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.setting__item .input__check {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.setting__item .input__check input[type="checkbox"] {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  position: relative;
  width: 45px;
  height: 25px;
  background: #94A0B8;
  border-radius: 27px;
}

.setting__item .input__check input[type="checkbox"]::before {
  position: absolute;
  content: "";
  top: 50%;
  left: 3px;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  width: 19px;
  height: 19px;
  border-radius: 50%;
  background: #FFFFFF;
  -webkit-box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
          box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.setting__item .input__check input[type="checkbox"]:checked {
  background: #5991FF;
}

.setting__item .input__check input[type="checkbox"]:checked::before {
  left: 23px;
}

.setting__item .card-type__area h2 {
  margin-bottom: 11px;
  text-transform: capitalize;
  font-weight: 500;
  font-size: 14px;
  line-height: 17px;
  color: #273041;
}

.setting__item .card-type__list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-left: -20px;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

.setting__item .card-type__list .card-type__item {
  background: #FAFBFC;
  border: 1px solid #EBEDF0;
  border-radius: 10px;
  margin-left: 20px;
  width: calc(100% / 4 - 20px);
  height: 100px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

@media screen and (max-width: 575px) {
  .setting__item .card-type__list .card-type__item {
    width: calc(100% / 2 - 20px);
    margin-bottom: 20px;
  }
}

@media screen and (max-width: 480px) {
  .setting__item .card-type__list .card-type__item {
    width: calc(100% / 1 - 20px);
  }
}

.setting__item .card-type__item {
  position: relative;
}

.setting__item .card-type__item .card-check {
  position: absolute;
  top: 10px;
  right: 10px;
}

.setting__item .card-type__item .card-check input[type='checkbox'] {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  width: 20px;
  height: 20px;
  background: #ffffff;
  border-radius: 4px;
  position: relative;
}

.setting__item .card-type__item .card-check input[type='checkbox']::before {
  position: absolute;
  content: "\f00c";
  font-family: 'FontAwesome';
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  color: #ffffff;
  opacity: 0;
}

.setting__item .card-type__item .card-check input[type='checkbox']:checked {
  background: #5991FF;
}

.setting__item .card-type__item .card-check input[type='checkbox']:checked::before {
  opacity: 1;
}

.setting__item .card-type__item.active {
  border: 2px solid #5991FF;
}

.setting__item .card-type__item.active .card-check input[type='checkbox'] {
  background: #5991FF;
}

.setting__item .plan__list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  margin-left: -24px;
}

@media screen and (max-width: 767px) {
  .setting__item .plan__list {
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
  }
}

.setting__item .plan__list .plan__item h2 {
  margin-bottom: 10px;
  text-transform: capitalize;
  font-weight: 600;
  font-size: 18px;
  line-height: 24px;
  color: #273041;
}

.setting__item .plan__list .plan__item p {
  margin-bottom: 17px;
  font-weight: normal;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.setting__item .plan__list .plan__item h3 {
  font-weight: 600;
  font-size: 18px;
  line-height: 24px;
  color: #273041;
}

.setting__item .plan__list .plan__item h3 span {
  font-weight: normal;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.setting__item .plan__list .plan__item {
  background: #FAFBFC;
  border: 1px solid #EBEDF0;
  border-radius: 10px;
  padding: 20px 20px 24px;
  width: calc(100%/3 - 24px);
  height: 100%;
  margin-left: 24px;
  position: relative;
}

.setting__item .plan__list .plan__item .plan-check {
  position: absolute;
  top: 20px;
  right: 18px;
}

.setting__item .plan__list .plan__item .plan-check input[type='checkbox'] {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  width: 25px;
  height: 25px;
  background: #ffffff;
  border-radius: 4px;
  position: relative;
}

.setting__item .plan__list .plan__item .plan-check input[type='checkbox']::before {
  position: absolute;
  content: "\f00c";
  font-family: 'FontAwesome';
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  color: #5991FF;
  opacity: 0;
}

.setting__item .plan__list .plan__item .plan-check input[type='checkbox']:checked {
  background: #ffffff;
}

.setting__item .plan__list .plan__item .plan-check input[type='checkbox']:checked::before {
  opacity: 1;
}

.setting__item .plan__list .plan__item.active {
  background: linear-gradient(219.61deg, #5991FF 0%, #BE63F9 100%);
}

.setting__item .plan__list .plan__item.active h2,
.setting__item .plan__list .plan__item.active p,
.setting__item .plan__list .plan__item.active h3,
.setting__item .plan__list .plan__item.active h3 span {
  color: #ffffff;
}

.setting__item .plan__list .plan__item.active .plan-check input[type='checkbox'] {
  background: #ffffff;
}

@media screen and (max-width: 767px) {
  .setting__item .plan__list .plan__item {
    width: calc(100%/2 - 24px);
    margin-bottom: 20px;
  }
}

@media screen and (max-width: 575px) {
  .setting__item .plan__list .plan__item {
    width: calc(100%/1 - 24px);
  }
}

.gallery__area {
  padding: 30px 25px;
}

.gallery__area .pagination__area {
  padding: 0;
}

.gallery__content .nav.nav-tabs {
  background: #F2F4F7;
  border-radius: 8px;
  padding: 2px;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.gallery__content .nav-link {
  border: 0;
  padding: 6px 20px !important;
  font-weight: 400;
  font-size: 16px;
  line-height: 28px;
  color: #596680;
}

.gallery__content .nav-link:focus {
  border: 0;
}

.gallery__content .nav-link.active {
  border: 0;
  background: #FFFFFF;
  -webkit-box-shadow: 0px 2px 6px rgba(163, 177, 204, 0.15);
          box-shadow: 0px 2px 6px rgba(163, 177, 204, 0.15);
  border-radius: 8px;
  font-weight: 500;
  color: #5991FF;
}

.gallery__content .nav-link.active:focus {
  border: 0;
}

.gallery__content .tab-content {
  margin-top: 30px;
}

.gallery__item {
  margin-bottom: 24px;
  background: #FFFFFF;
  border: 1px solid #DFE3EB;
  border-radius: 10px;
  overflow: hidden;
  height: calc(100% - 24px);
}

.gallery__item .gallery-img {
  width: 100%;
  height: auto;
}

.gallery__item .gallery-img img {
  width: 100%;
  height: auto;
}

.gallery__item .gallery-text {
  padding: 20px;
}

.gallery__item .gallery-text a {
  margin-bottom: 3px;
  text-transform: capitalize;
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  color: #273041;
}

.gallery__item .gallery-text h3 {
  margin-bottom: 15px;
  text-transform: capitalize;
  font-weight: 500;
  font-size: 12px;
  line-height: 15px;
  color: #596680;
}

.gallery__item .gallery-text p {
  margin-bottom: 0;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.map__area {
  margin-bottom: 30px;
  padding: 25px;
}

.map__area .map__item {
  width: 100%;
  height: 280px;
  border-radius: 8px;
  overflow: hidden;
}

.map__content iframe {
  width: 100%;
  height: 280px;
  border: 0;
}

.map__area .map__item .jvectormap-zoomin,
.map__area .map__item .jvectormap-zoomout {
  position: absolute;
  left: unset;
  right: 10px;
  background: #F7F9FA;
  border: 1px solid #DFE2EB;
  font-size: 20px;
  color: #596680;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

.map__area .map__item .jvectormap-zoomin {
  top: 10px;
  width: 20px;
  height: 20px;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}

.map__area .map__item .jvectormap-zoomin::before {
  position: absolute;
  z-index: 9;
  content: "";
  top: 100%;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  width: 17px;
  height: 1px;
  background: #DFE2EB;
}

.map__area .map__item .jvectormap-zoomout {
  top: 34px;
  width: 20px;
  height: 20px;
  border-top: 0;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
}

.leaflet-default-icon-path {
  background-image: url("../images/map-icon/marker-icon.png");
}

.leaflet-default-shadow-path {
  background-image: url("../images/map-icon/marker-shadow.png");
}

.leaflet-control-layers-toggle {
  background-image: url("../images/map-icon/layers.png");
}

.toast-chart__area {
  padding: 25px;
  margin-bottom: 30px;
}

.toast-chart__area .item-title {
  margin-bottom: 12px;
}

.toast-chart__area .item-title h2 {
  text-transform: capitalize;
  font-weight: 600;
  font-size: 18px;
  line-height: 24px;
  color: #273041;
}

.toast-chart__area .item-title h3 {
  margin-top: 12px;
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  color: #596680;
}

.toast-chart__area .chart__content .chart__item {
  width: 100%;
  height: 380px;
}

.pricing__area {
  padding: 25px;
  margin-bottom: 40px;
}

.pricing__area__title {
  padding: 49px 0 45px;
  max-width: 45%;
  margin: 0 auto;
  text-align: center;
}

.pricing__area__title h2 {
  margin-bottom: 16px;
  font-weight: 600;
  font-size: 24px;
  line-height: 32px;
  color: #273041;
}

.pricing__area__title p {
  margin-bottom: 0;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

@media screen and (max-width: 991px) {
  .pricing__area__title {
    padding: 49px 0 45px;
    max-width: 56%;
  }
}

@media screen and (max-width: 767px) {
  .pricing__area__title {
    padding: 25px 0 40px;
    max-width: 80%;
  }
}

@media screen and (max-width: 480px) {
  .pricing__area__title {
    max-width: 100%;
  }
  .pricing__area__title h2 {
    font-size: 20px;
  }
}

.pricing__area__content .nav.nav-tabs {
  background: #F6F7FA;
  border-radius: 8px;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  margin: 0 auto 50px;
  padding: 2px;
}

.pricing__area__content .nav.nav-tabs .nav-link {
  padding: 6px 12px !important;
  text-transform: capitalize;
  font-weight: 400;
  font-size: 16px;
  line-height: 28px;
  color: #596680;
  border: 0;
}

.pricing__area__content .nav.nav-tabs .nav-link:hover {
  border: 0;
}

.pricing__area__content .nav.nav-tabs .nav-link.active {
  background: #FFFFFF;
  -webkit-box-shadow: 0px 2px 6px rgba(163, 177, 204, 0.15);
          box-shadow: 0px 2px 6px rgba(163, 177, 204, 0.15);
  border-radius: 8px;
  font-weight: 500;
  color: #5991FF;
}

.pricing__item {
  height: calc(100% - 25px);
  margin-bottom: 25px;
  background: #FAFBFC;
  border: 1px solid #EBEDF0;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  border-radius: 10px;
  padding: 30px 20px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  position: relative;
  z-index: 0;
  overflow: hidden;
}

.pricing__item::before {
  position: absolute;
  z-index: -1;
  content: "";
  top: 0;
  left: 0;
  width: 0%;
  height: 100%;
  background: linear-gradient(219.61deg, #5991FF 0%, #BE63F9 100%);
  opacity: 0;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.pricing__item:hover::before, .pricing__item.active::before {
  width: 100%;
  opacity: 1;
}

.pricing__item:hover .pricing__item__top .item-img, .pricing__item.active .pricing__item__top .item-img {
  background: rgba(255, 255, 255, 0.15);
}

.pricing__item:hover .pricing__item__top .item-img img, .pricing__item.active .pricing__item__top .item-img img {
  -webkit-filter: brightness(0) invert(1);
          filter: brightness(0) invert(1);
}

.pricing__item:hover .pricing__item__top .item-text h2, .pricing__item.active .pricing__item__top .item-text h2 {
  color: #ffffff;
}

.pricing__item:hover .pricing__item__top .item-text p, .pricing__item.active .pricing__item__top .item-text p {
  color: rgba(255, 255, 255, 0.8);
}

.pricing__item:hover .pricing__item__top .item-text h3, .pricing__item.active .pricing__item__top .item-text h3 {
  color: rgba(255, 255, 255, 0.8);
}

.pricing__item:hover .pricing__item__top .item-text h3 span, .pricing__item.active .pricing__item__top .item-text h3 span {
  color: #ffffff;
}

.pricing__item:hover .pricing__item__list .list-item img, .pricing__item.active .pricing__item__list .list-item img {
  -webkit-filter: brightness(0) invert(1);
          filter: brightness(0) invert(1);
}

.pricing__item:hover .pricing__item__list .list-item h2, .pricing__item.active .pricing__item__list .list-item h2 {
  color: rgba(255, 255, 255, 0.8);
}

.pricing__item:hover .pricing__item__button a, .pricing__item.active .pricing__item__button a {
  background-color: rgba(255, 255, 255, 0.8);
}

.pricing__item__top .item-img {
  margin-bottom: 24px;
  background: #F7EBFF;
  border-radius: 12px;
  width: 60px;
  height: 60px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

.pricing__item__top .item-img img {
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.pricing__item__top .item-text h2 {
  margin-bottom: 8px;
  text-transform: capitalize;
  font-weight: 600;
  font-size: 18px;
  line-height: 24px;
  color: #273041;
}

.pricing__item__top .item-text p {
  margin-bottom: 17px;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.pricing__item__top .item-text h3 {
  text-transform: capitalize;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.pricing__item__top .item-text h3 span {
  font-weight: 600;
  font-size: 18px;
  line-height: 24px;
  color: #273041;
}

.pricing__item .pricing__item__list {
  margin: 30px 0;
}

.pricing__item .pricing__item__list li:not(:first-child) {
  margin-top: 10px;
}

.pricing__item .pricing__item__list .list-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.pricing__item .pricing__item__list .list-item img {
  width: 16px;
  height: 16px;
  margin-right: 10px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.pricing__item .pricing__item__list .list-item h2 {
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.pricing__item__button a {
  width: 100%;
  background: #FAFBFC;
  border: 1px solid #5991FF;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  border-radius: 10px;
  padding: 13px;
  font-weight: 700;
  font-size: 16px;
  line-height: 19px;
  text-align: center;
  color: #5991FF;
}

.pricing__table {
  width: 100%;
  overflow-x: auto;
}

.pricing__table table {
  width: 100%;
  border: 1px solid #E7E8EB;
}

.pricing__table table thead th {
  border: 1px solid #E7E8EB;
}

.pricing__table table thead th:nth-child(1) {
  margin-top: 10px;
}

.pricing__table table tbody td {
  border: 1px solid #E7E8EB;
  padding: 11px 20px;
}

.pricing__table table tbody td:nth-child(1) {
  min-width: 250px;
}

.pricing__table table tbody td:nth-child(2) {
  min-width: 250px;
}

.pricing__table table tbody td:nth-child(3) {
  min-width: 250px;
}

.pricing__table table tbody td:nth-child(4) {
  min-width: 250px;
}

.pricing__table table .heading-text {
  display: block;
  padding: 13px 19px;
  background: #EDF1F9;
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  color: #273041;
}

.pricing__table table .title-text {
  padding: 20px 20px 25px;
}

.pricing__table table .title-text .data-title {
  margin-bottom: 11px;
  text-transform: capitalize;
  font-weight: 600;
  font-size: 18px;
  line-height: 24px;
  color: #273041;
}

.pricing__table table .title-text p {
  margin-bottom: 20px;
  text-transform: capitalize;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.pricing__table table .title-text p span {
  font-weight: 600;
  font-size: 18px;
  line-height: 24px;
  color: #273041;
}

.pricing__table table .title-text a {
  display: block;
  background: #FFFFFF;
  border: 1px solid #5991FF;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  border-radius: 10px;
  padding: 13px;
  text-align: center;
  text-transform: capitalize;
  font-weight: 700;
  font-size: 16px;
  line-height: 19px;
  color: #5991FF;
}

.pricing__table table .data-title {
  text-transform: capitalize;
  font-weight: 400;
  font-size: 16px;
  line-height: 28px;
  color: #273041;
}

.pricing__table table .data-text {
  text-transform: capitalize;
  font-weight: 400;
  font-size: 16px;
  line-height: 28px;
  color: #596680;
}

.color__item {
  padding: 25px 25px 30px;
  margin-bottom: 30px;
  height: calc(100% - 30px);
}

.color__item .item-title h2 {
  text-transform: capitalize;
  font-weight: 600;
  font-size: 18px;
  line-height: 24px;
  color: #273041;
}

.color__item .color__list .list-item:first-child {
  border-radius: 10px 10px 0 0;
}

.color__item .color__list .list-item:last-child {
  border-radius: 0 0 10px 10px;
}

.color__item .color__list .list-item {
  padding: 23px;
  text-align: center;
  text-transform: capitalize;
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
}

.button__area {
  padding: 25px 25px 30px;
  margin-bottom: 30px;
  height: calc(100% - 30px);
}

.button__area .item-title {
  margin-bottom: 30px;
}

.button__area .item-title h2 {
  text-transform: capitalize;
  font-weight: 600;
  font-size: 18px;
  line-height: 24px;
  color: #273041;
}

.button__area .button__list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin: -15px 0 0 -15px;
}

.button__area .button__list a {
  margin: 15px 0 0 15px;
}

.button__area .button__list .item-between {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin: -15px 0 0 -15px;
}

.button__area .button__list .item-between .group-button {
  margin: 15px 0 0 15px;
}

.button__list .group-button .nav.nav-tabs {
  background: #F6F7FA;
  border-radius: 8px;
  padding: 2px;
}

.button__list .group-button .nav.nav-tabs .nav-link {
  text-transform: capitalize;
  font-weight: 400;
  font-size: 16px;
  line-height: 28px;
  color: #596680;
  padding: 6px 15px !important;
  border: 0;
}

.button__list .group-button .nav.nav-tabs .nav-link.active {
  background: #FFFFFF;
  -webkit-box-shadow: 0px 2px 6px rgba(163, 177, 204, 0.15);
          box-shadow: 0px 2px 6px rgba(163, 177, 204, 0.15);
  border-radius: 8px;
  font-weight: 500;
  color: #5991FF;
}

.button__list .group-button .nav.nav-tabs.style-2 .nav-link.active {
  background: transparent;
  -webkit-box-shadow: none;
          box-shadow: none;
}

@media screen and (max-width: 480px) {
  .button__list .group-button .nav.nav-tabs .nav-link {
    padding: 5px 10px !important;
  }
}

.button__list .group-button-tab {
  margin-top: 20px;
  width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.button__list .group-button-tab .nav.nav-tabs .nav-link {
  border: 0;
  padding: 0 20px 10px !important;
  border-bottom: 2px solid #EBEDF2;
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  color: #596680;
}

.button__list .group-button-tab .nav.nav-tabs .nav-link i {
  font-size: 24px;
  color: #596680;
  margin-right: 10px;
}

.button__list .group-button-tab .nav.nav-tabs .nav-link.active {
  border-bottom: 2px solid #5991FF;
  color: #5991FF;
}

.button__list .group-button-tab .nav.nav-tabs .nav-link.active i {
  color: #5991FF;
}

@media screen and (max-width: 480px) {
  .button__list .group-button-tab .nav.nav-tabs .nav-link {
    padding: 5px 10px !important;
  }
}

.typography__area {
  padding: 25px;
  margin-bottom: 30px;
  height: calc(100% - 30px);
}

.typography__area .item-title {
  margin-bottom: 20px;
}

.typography__area .item-title h2 {
  text-transform: capitalize;
  font-weight: 600;
  font-size: 18px;
  line-height: 24px;
  color: #273041;
}

.typography__area .item-content .heading-title.display h1, .typography__area .item-content .heading-title.display h2, .typography__area .item-content .heading-title.display h3, .typography__area .item-content .heading-title.display h4, .typography__area .item-content .heading-title.display h5, .typography__area .item-content .heading-title.display h6 {
  font-weight: 400;
  color: #273041;
  margin-bottom: 20px;
}

.typography__area .item-content .heading-title.display h1 {
  font-size: 80px;
  line-height: 80px;
}

.typography__area .item-content .heading-title.display h2 {
  font-size: 72px;
  line-height: 72px;
}

.typography__area .item-content .heading-title.display h3 {
  font-size: 64px;
  line-height: 64px;
}

.typography__area .item-content .heading-title.display h4 {
  font-size: 56px;
  line-height: 56px;
}

.typography__area .item-content .heading-title.display h5 {
  font-size: 48px;
  line-height: 48px;
}

.typography__area .item-content .heading-title.display h6 {
  font-size: 40px;
  line-height: 40px;
}

@media screen and (max-width: 480px) {
  .typography__area .item-content .heading-title.display h1 {
    font-size: 58px;
    line-height: 60px;
  }
  .typography__area .item-content .heading-title.display h2 {
    font-size: 48px;
    line-height: 50px;
  }
  .typography__area .item-content .heading-title.display h3 {
    font-size: 38px;
    line-height: 64px;
  }
  .typography__area .item-content .heading-title.display h4 {
    font-size: 28px;
    line-height: 40px;
  }
  .typography__area .item-content .heading-title.display h5 {
    font-size: 24px;
    line-height: 30px;
  }
  .typography__area .item-content .heading-title.display h6 {
    font-size: 20px;
    line-height: 30px;
  }
}

.typography__area .item-content .heading-title h1, .typography__area .item-content .heading-title h2, .typography__area .item-content .heading-title h3, .typography__area .item-content .heading-title h4, .typography__area .item-content .heading-title h5, .typography__area .item-content .heading-title h6 {
  font-weight: 600;
  color: #273041;
  margin-bottom: 20px;
}

.typography__area .item-content .heading-title h1 {
  font-size: 32px;
  line-height: 32px;
}

.typography__area .item-content .heading-title h2 {
  font-size: 24px;
  line-height: 24px;
}

.typography__area .item-content .heading-title h3 {
  font-size: 18.72px;
  line-height: 20px;
}

.typography__area .item-content .heading-title h4 {
  font-size: 16px;
  line-height: 16px;
}

.typography__area .item-content .heading-title h5 {
  font-size: 13.28px;
  line-height: 15px;
}

.typography__area .item-content .heading-title h6 {
  font-size: 10.72px;
  line-height: 15px;
}

@media screen and (max-width: 480px) {
  .typography__area .item-content .heading-title h1 {
    font-size: 28px;
    line-height: 32px;
  }
  .typography__area .item-content .heading-title h2 {
    font-size: 24px;
    line-height: 30px;
  }
  .typography__area .item-content .heading-title h3 {
    font-size: 18px;
    line-height: 20px;
  }
  .typography__area .item-content .heading-title h4 {
    font-size: 16px;
    line-height: 20px;
  }
  .typography__area .item-content .heading-title h5 {
    font-size: 13px;
    line-height: 15px;
  }
  .typography__area .item-content .heading-title h6 {
    font-size: 10px;
    line-height: 15px;
  }
}

.inline-text h2 {
  margin-bottom: 12px;
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  color: #273041;
}

.inline-text p {
  margin-bottom: 0;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.inline-text p:not(:first-child) {
  margin-top: 14px;
}

.inline-text p mark {
  background-color: #FFF9ED;
  color: #596680;
}

.inline-text p small {
  font-weight: 400;
  font-size: 12px;
  line-height: 15px;
}

.unordered__list {
  list-style: disc;
}

.unordered__list li {
  margin-left: 10px;
}

.unordered__list li:not(:first-child) {
  margin-top: 20px;
}

.ordered__list li {
  margin-left: -10px;
}

.unordered__list li,
.ordered__list li {
  font-weight: normal;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.unordered__list li:not(:first-child),
.ordered__list li:not(:first-child) {
  margin-top: 20px;
}

.typography__area .item-content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

@media screen and (max-width: 767px) {
  .typography__area .item-content {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-align: start;
        -ms-flex-align: start;
            align-items: flex-start;
  }
  .typography__area .item-content .item-content__left {
    margin-bottom: 20px;
  }
}

.typography__area.font__area .font__area__top {
  padding-bottom: 25px;
  border-bottom: 1px solid #DCDFE5;
  margin-bottom: 35px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

.typography__area.font__area .font__area__content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.typography__area.font__area .font__area__content .item__left .font-item:not(:first-child),
.typography__area.font__area .font__area__content .item__right .font-item:not(:first-child) {
  margin-top: 40px;
}

@media screen and (max-width: 480px) {
  .typography__area.font__area .font__area__content {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
  .typography__area.font__area .font__area__content .item__right {
    margin-top: 40px;
  }
}

.font-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.font-item__left {
  margin-right: 24px;
}

.font-item h2 {
  font-weight: 600;
  font-size: 48px;
  line-height: 50px;
  color: #273041;
}

.font-item h3 {
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;
  color: #273041;
}

.font-item h4 {
  font-weight: 600;
  font-size: 18px;
  line-height: 24px;
  color: #273041;
}

.font-item.thin h2, .font-item.thin h4 {
  font-weight: 100;
}

.font-item.ex-light h2, .font-item.ex-light h4 {
  font-weight: 200;
}

.font-item.light h2, .font-item.light h4 {
  font-weight: 300;
}

.font-item.regular h2, .font-item.regular h4 {
  font-weight: 400;
}

.font-item.medium h2, .font-item.medium h4 {
  font-weight: 500;
}

.font-item.semi-bold h2, .font-item.semi-bold h4 {
  font-weight: 600;
}

.font-item.bold h2, .font-item.bold h4 {
  font-weight: 700;
}

.font-item.ex-bold h2, .font-item.ex-bold h4 {
  font-weight: 800;
}

.font-item.black h2, .font-item.black h4 {
  font-weight: 900;
}

blockquote {
  max-width: 496px;
  padding-left: 25px;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  position: relative;
}

@media screen and (max-width: 575px) {
  blockquote {
    max-width: 100%;
  }
}

blockquote::before {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  width: 5px;
  height: 100%;
  background-color: #DCDFE5;
}

blockquote span {
  margin-top: 16px;
  padding-top: 4px;
  position: relative;
}

blockquote span::before {
  position: absolute;
  content: "";
  top: 0;
  left: 0;
  width: 100px;
  height: 1px;
  background-color: #DCDFE5;
}

blockquote.right {
  padding-left: 0px;
  padding-right: 25px;
  text-align: right;
}

blockquote.right::before {
  left: unset;
  right: 0;
}

blockquote.right span::before {
  left: unset;
  right: 0;
}

.footer__area {
  padding: 22px 0px 21px 0;
  background: #F0F2F5;
}

.footer__copyright {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

@media screen and (max-width: 480px) {
  .footer__copyright {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
  }
  .footer__copyright__left {
    margin-bottom: 10px;
  }
}

.footer__copyright h2 {
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

/*--------------------------------
04. Components style
--------------------------------*/
.breadcrumb__content {
  padding: 10px 0 20px 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

@media screen and (max-width: 575px) {
  .breadcrumb__content {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-align: start;
        -ms-flex-align: start;
            align-items: flex-start;
    -ms-flex-line-pack: start;
        align-content: flex-start;
  }
  .breadcrumb__content .breadcrumb__title {
    margin-bottom: 15px;
  }
}

.breadcrumb__title h2 {
  text-transform: capitalize;
  font-weight: 600;
  font-size: 24px;
  line-height: 32px;
  color: #ffffff;
}

.breadcrumb__content .breadcrumb {
  margin-bottom: 0;
}

.breadcrumb__content .breadcrumb li {
  text-transform: capitalize;
  font-size: 14px;
  line-height: 24px;
  color: #828A99;
}

.breadcrumb__content .breadcrumb li.active {
  color: #D9E0EF;
}

.section__title {
  text-align: center;
  max-width: 60%;
  margin: 0 auto 50px;
}

.section__title h2 {
  font-size: 32px;
  font-weight: 500;
  line-height: 38px;
  color: #000000;
  margin-bottom: 20px;
}

.section__title p {
  font-size: 16px;
  font-weight: 400;
  line-height: 28px;
  color: #000000;
}

.btn {
  padding: 13px;
  border-radius: 10px;
  text-transform: capitalize;
  font-weight: 600;
  font-size: 16px;
  line-height: 19px;
  color: #ffffff;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.btn:focus {
  -webkit-box-shadow: none;
          box-shadow: none;
}

.btn span {
  margin-left: 10px;
}

.btn.rounded {
  border-radius: 50px !important;
}

.btn.rounded-1 {
  border-radius: 10px !important;
}

.btn.large {
  padding: 16px 25px !important;
}

.btn.small {
  padding: 12px 25px !important;
}

.btn.ex-small {
  padding: 9px 25px !important;
}

.btn.btn-blue {
  background-color: #4D88FF;
  color: #ffffff;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.btn.btn-blue:hover {
  background-color: #34579e;
  color: #ffffff;
}

.btn.btn-green {
  background-color: #4CBF4C;
  color: #ffffff;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.btn.btn-green:hover {
  background-color: #3ab83a;
  color: #ffffff;
}

.btn.btn-grey {
  background: #EBEDF0;
  color: #596680;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.btn.btn-grey:hover {
  background-color: #b4bac4;
  color: #596680;
}

.btn.btn-reply {
  background: #F0DAFF;
  font-weight: 500;
  font-size: 15px;
  line-height: 18px;
  color: #BE63F9;
}

.btn.btn-reply:hover {
  background: #F0DAFF;
}

.btn.btn-forward {
  background: #DAE9FF;
  font-weight: 500;
  font-size: 15px;
  line-height: 18px;
  color: #5991FF;
}

.btn.btn-forward:hover {
  background: #DAE9FF;
}

.btn.btn-purple {
  background-color: #BE63F9;
  color: #ffffff;
}

.btn.btn-purple:hover {
  background-color: #4b0d42;
  color: #ffffff;
}

.btn.btn-outline-purple {
  border-color: #4b0d42;
  background-color: transparent;
  color: #4b0d42;
}

.btn.btn-outline-purple:hover {
  background-color: #4b0d42;
  color: #ffffff;
}

.btn.btn-red {
  background-color: #F96363;
  color: #ffffff;
}

.btn.btn-red:hover {
  background-color: #c73b3b;
  color: #ffffff;
}

.btn.btn-export {
  padding: 12px 20px 12px;
  background: #F7EBFF;
  border-radius: 8px;
  font-weight: 500;
  font-size: 14px;
  line-height: 17px;
  color: #BE63F9;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.btn.btn-export:hover {
  background-color: #bdb1c4;
  color: #ffffff;
}

.btn.btn-cart {
  background: #FFFFFF;
  border: 1px solid #5991FF;
  border-radius: 10px;
  font-weight: 600;
  font-size: 14px;
  line-height: 17px;
  text-align: center;
  color: #5991FF;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.btn.btn-cart span {
  margin-left: 9px;
}

.btn.btn-cart:hover {
  background: #5991FF;
  color: #ffffff;
}

.btn.btn-cart:hover img {
  -webkit-filter: brightness(0) invert(1);
          filter: brightness(0) invert(1);
}

.btn.btn-dropdown {
  padding: 0;
  border-radius: 0;
  color: #6B83B2;
}

.btn.btn-dropdown:focus {
  -webkit-box-shadow: none;
          box-shadow: none;
}

.btn.btn-success {
  background-color: #4CBF4C;
  color: #ffffff;
}

.btn.btn-success:hover {
  background-color: #228322;
  color: #ffffff;
}

.btn.btn-info {
  background-color: #33BBFF;
  color: #ffffff;
}

.btn.btn-info:hover {
  background-color: #1f80b1;
  color: #ffffff;
}

.btn.btn-warning {
  background-color: #FF9F38;
  color: #ffffff;
}

.btn.btn-warning:hover {
  background-color: #c07423;
  color: #ffffff;
}

.btn.btn-danger {
  background-color: #FF6628;
  color: #ffffff;
}

.btn.btn-danger:hover {
  background-color: #a0370d;
  color: #ffffff;
}

.btn.btn-dark {
  background-color: #273041;
  color: #ffffff;
}

.btn.btn-dark:hover {
  background-color: #0d121a;
  color: #ffffff;
}

.btn.btn-light {
  background-color: #EBEDF0;
  color: #273041;
}

.btn.btn-light:hover {
  background-color: #dadde0;
  color: #273041;
}

.btn.btn-outline-primary {
  border: 1px solid #5991FF;
  color: #5991FF;
}

.btn.btn-outline-primary:hover {
  background-color: #5991FF;
  color: #ffffff;
}

.btn.btn-outline-primary:hover img {
  -webkit-filter: brightness(0) invert(1);
          filter: brightness(0) invert(1);
}

.btn.btn-outline-secondary {
  border: 1px solid #BE63F9;
  color: #BE63F9;
}

.btn.btn-outline-secondary:hover {
  background-color: #BE63F9;
  color: #ffffff;
}

.btn.btn-outline-success {
  border: 1px solid #4CBF4C;
  color: #4CBF4C;
}

.btn.btn-outline-success:hover {
  background-color: #4CBF4C;
  color: #ffffff;
}

.btn.btn-outline-info {
  border: 1px solid #33BBFF;
  color: #33BBFF;
}

.btn.btn-outline-info:hover {
  background-color: #33BBFF;
  color: #ffffff;
}

.btn.btn-outline-warning {
  border: 1px solid #FF9F38;
  color: #FF9F38;
}

.btn.btn-outline-warning:hover {
  background-color: #FF9F38;
  color: #ffffff;
}

.btn.btn-outline-danger {
  border: 1px solid #FF6628;
  color: #FF6628;
}

.btn.btn-outline-danger:hover {
  background-color: #FF6628;
  color: #ffffff;
}

.btn.btn-outline-dark {
  border: 1px solid #273041;
  color: #273041;
}

.btn.btn-outline-dark:hover {
  background-color: #273041;
  color: #ffffff;
}

.btn.btn-outline-light {
  border: 1px solid #EBEDF0;
  color: #273041;
}

.btn.btn-outline-light:hover {
  background-color: #EBEDF0;
  color: #273041;
}

.btn.bg-primary-light-varient {
  background: #DAE9FF;
  border-radius: 10px;
  color: #5991FF;
}

.btn.bg-secondary-light-varient {
  background: #F0DAFF;
  border-radius: 10px;
  color: #BE63F9;
}

.btn.bg-success-light-varient {
  background: #C8F7C8;
  border-radius: 10px;
  color: #4CBF4C;
}

.btn.bg-info-light-varient {
  background: #DAF3FF;
  border-radius: 10px;
  color: #33BBFF;
}

.btn.bg-warning-light-varient {
  background: #FAE3CA;
  border-radius: 10px;
  color: #FF9F38;
}

.btn.bg-danger-light-varient {
  background: #FFDED1;
  border-radius: 10px;
  color: #FF6628;
}

.btn.btn-print {
  background: #D5FAD5;
  border-radius: 8px;
  font-weight: 500;
  font-size: 15px;
  line-height: 18px;
  color: #4CBF4C;
}

.btn.btn-download {
  background: #F0DAFF;
  border-radius: 8px;
  font-weight: 500;
  font-size: 15px;
  line-height: 18px;
  color: #BE63F9;
}

.btn.btn-send {
  background: #DAE9FF;
  border-radius: 8px;
  font-weight: 500;
  font-size: 15px;
  line-height: 18px;
  color: #5991FF;
}

.text-link {
  text-transform: capitalize;
  font-weight: 500;
  font-size: 14px;
  line-height: 17px;
  color: #5991FF;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.text-link:hover {
  opacity: 0.7;
}

.btn-reply {
  text-transform: capitalize;
  font-weight: 600;
  font-size: 14px;
  line-height: 17px;
  color: #BE63F9;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.btn-reply:hover {
  color: #af55eb;
}

.user-btn {
  border-radius: 4px;
  padding: 6px 12px;
  text-transform: capitalize;
  font-weight: 500;
  font-size: 12px;
  line-height: 15px;
}

.user-btn.bg-blue {
  background: #DAE9FF;
  color: #5991FF;
}

.user-btn.bg-purple {
  background: #F0DAFF;
  color: #BE63F9;
}

.button__area .btn {
  padding: 13px 25px;
}

.header-area {
  position: fixed;
  z-index: 9999;
  top: 0;
  left: 0;
  width: 100%;
  background-color: #eff4f7;
  padding: 15px 0;
  -webkit-transition: 0.5s;
  transition: 0.5s;
}

.header-area.sticky {
  padding: 5px 0;
  background-color: #ffffff;
  -webkit-box-shadow: rgba(0, 0, 0, 0.05) 0px 10px 30px;
          box-shadow: rgba(0, 0, 0, 0.05) 0px 10px 30px;
}

.pagination__area {
  padding: 25px 0 40px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

@media screen and (max-width: 480px) {
  .pagination__area {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-align: start;
        -ms-flex-align: start;
            align-items: flex-start;
  }
  .pagination__area .pagination__left {
    margin-bottom: 20px;
  }
}

.pagination__left h2 {
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #666B87;
}

.pagination__left .total {
  margin-top: 0;
  display: unset;
}

.pagination__list {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

.pagination__list li:not(:first-child) {
  margin-left: 8px;
}

.pagination__list li a {
  padding: 8px;
  width: 30px;
  height: 30px;
  background: #FFFFFF;
  border: 1px solid #DFE3EB;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  border-radius: 6px;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.pagination__list li a.active, .pagination__list li a:hover {
  background: #5991FF;
  border: 1px solid transparent;
  color: #ffffff;
}

.input__group input,
.input__group textarea,
.input__group select {
  border: 1px solid #DFE3EB;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.input__group input:focus,
.input__group textarea:focus,
.input__group select:focus {
  border: 1px solid #5991FF;
  -webkit-box-shadow: 0px 0px 2px rgba(89, 145, 255, 0.7);
          box-shadow: 0px 0px 2px rgba(89, 145, 255, 0.7);
}

.input__group__between {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.input__group__between .input__group {
  -ms-flex-preferred-size: 48%;
      flex-basis: 48%;
}

.input__group__between .input__group input,
.input__group__between .input__group button {
  width: 100%;
}

@media screen and (max-width: 575px) {
  .input__group__between {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
  .input__group__between .input__group {
    width: 100%;
  }
  .input__group__between .input__group:not(:first-child) {
    margin-top: 15px;
  }
}

.input-overlay input:focus + .overlay img {
  -webkit-filter: invert(67%) sepia(55%) saturate(5137%) hue-rotate(198deg) brightness(99%) contrast(104%);
          filter: invert(67%) sepia(55%) saturate(5137%) hue-rotate(198deg) brightness(99%) contrast(104%);
}

.input-overlay {
  position: relative;
}

.input-overlay .overlay {
  position: absolute;
  top: 50%;
  left: 14px;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}

.input-overlay .overlay img {
  width: 20px;
  height: 20px;
}

.input-overlay input {
  width: 100%;
  padding: 13px 13px 13px 49px;
}

.form-editor__item {
  padding: 25px 25px 30px;
  margin-bottom: 40px;
}

.form-editor__item .ck.ck-content {
  height: 220px;
}

.form-wizard {
  padding: 25px;
  margin-bottom: 30px;
}

.form-wizard__area .step-progress {
  margin-bottom: 30px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

.form-wizard__area .step-progress li {
  width: calc(100% / 4);
}

.form-wizard__area .step-progress li.active .step-progress__item {
  background-color: transparent;
  color: #273041;
}

.form-wizard__area .step-progress li.active .step-progress__item span {
  background-color: #5991FF;
}

.form-wizard__area .step-progress li.active .step-progress__item h2 {
  color: #273041;
}

.form-wizard__area .step-progress li {
  position: relative;
}

.form-wizard__area .step-progress li.active-bar::before {
  background-color: #5991FF;
}

.form-wizard__area .step-progress li::before {
  position: absolute;
  z-index: 0;
  content: "";
  top: 25px;
  left: calc(50% + 15%);
  width: 70%;
  height: 2px;
  background-color: #DFE2EB;
}

.form-wizard__area .step-progress li:last-child::before {
  display: none;
}

.form-wizard__area .step-progress__item {
  padding: 0 20px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  text-transform: capitalize;
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  color: rgba(39, 48, 65, 0.7);
}

.form-wizard__area .step-progress span {
  z-index: 1;
  margin-bottom: 15px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  background-color: #8E9AB2;
  font-weight: 600;
  color: #FFFFFF;
}

@media screen and (max-width: 767px) {
  .form-wizard__area .step-progress {
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
  }
  .form-wizard__area .step-progress li {
    width: auto;
  }
  .form-wizard__area .step-progress li::before {
    top: 45px;
    left: calc(50% + 10px);
    width: 90%;
  }
  .form-wizard__area .step-progress__item {
    padding: 20px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
  }
}

@media screen and (max-width: 575px) {
  .form-wizard__area .step-progress {
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
  }
  .form-wizard__area .step-progress li::before {
    top: 35px;
  }
  .form-wizard__area .step-progress__item {
    padding: 10px;
    font-size: 14px;
  }
}

@media screen and (max-width: 480px) {
  .form-wizard__area .step-progress {
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
  }
  .form-wizard__area .step-progress li::before {
    display: none;
  }
}

.form-wizard__area .progress-steps {
  counter-reset: step;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  text-align: center;
}

.form-wizard__area .progress-steps li {
  position: relative;
  width: calc(100%/4);
}

.form-wizard__area .progress-steps li::before {
  content: counter(step);
  counter-increment: step;
  width: 20px;
  line-height: 20px;
  display: block;
  font-size: 10px;
  color: #fff;
  background: #679b9b;
  border-radius: 3px;
  margin: 0 auto 5px auto;
}

.form-wizard__area .progress-steps li::after {
  content: "";
  width: 85%;
  height: 2px;
  background: red;
  position: absolute;
  left: calc(-50% + 16px);
  top: 9px;
  z-index: 0;
}

.form-wizard__area .progress-steps li:first-child::after {
  content: none;
}

.wizard-steps__item__content {
  min-height: 354px;
}

.wizard-steps__item__content .input__button {
  -webkit-box-pack: right;
      -ms-flex-pack: right;
          justify-content: right;
  margin-left: 0;
  margin-right: -20px;
}

.wizard-steps__item__content .input__button a {
  padding: 13px 25px;
  margin-right: 20px;
}

.wizard-steps__item__content .finish-text {
  text-align: center;
}

.wizard-steps__item__content .finish-text img {
  width: 50px;
  height: 50px;
  margin-bottom: 20px;
}

.wizard-steps__item__content .finish-text h2 {
  text-transform: capitalize;
  font-size: 24px;
  font-weight: 600;
  color: #273041;
}

.wizard-steps__item__content .input__checkbox {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.wizard-steps__item__content .input__checkbox label {
  text-transform: capitalize;
  font-weight: 500;
  font-size: 14px;
  line-height: 17px;
  color: #273041;
  margin-right: 5px;
}

.wizard-steps__item__content .input__checkbox input[type='checkbox'] {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  position: relative;
  width: 45px;
  height: 25px;
  background: #94A0B8;
  border-radius: 27px;
}

.wizard-steps__item__content .input__checkbox input[type='checkbox']::before {
  position: absolute;
  content: "";
  top: 50%;
  left: 3px;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #FFFFFF;
  -webkit-box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
          box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.wizard-steps__item__content .input__checkbox input[type='checkbox']:checked {
  background: #5991FF;
}

.wizard-steps__item__content .input__checkbox input[type='checkbox']:checked::before {
  left: 23px;
}

.wizard-steps__buttons {
  margin-top: 25px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
  margin-right: -20px;
}

.wizard-steps__buttons button {
  margin-right: 20px;
  padding: 13px 25px;
}

@media screen and (max-width: 480px) {
  .wizard-steps__buttons {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
  .wizard-steps__buttons button {
    width: 100%;
  }
  .wizard-steps__buttons button:not(:first-child) {
    margin-top: 10px;
  }
}

.form-wizard__area #first {
  display: none;
}

.form-wizard__area #second {
  display: block;
}

.form-wizard__area #third {
  display: none;
}

.form-wizard__area #four {
  display: none;
}

.form-wizard__area.vertical .step-progress {
  width: 100%;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
  margin-left: 0px;
}

.form-wizard__area.vertical .step-progress li {
  width: 100%;
  margin-left: 0px;
  position: relative;
  z-index: 1;
}

.form-wizard__area.vertical .step-progress li.active-bar::before {
  background: #5991FF;
}

.form-wizard__area.vertical .step-progress li::before {
  position: absolute;
  content: "";
  z-index: -1;
  top: 50px;
  left: 25px;
  width: 2px;
  height: 100%;
  background: #DFE2EB;
  border-radius: 2px;
}

.form-wizard__area.vertical .step-progress li:not(:first-child) {
  margin-top: 20px;
}

.form-wizard__area.vertical .step-progress li:last-child::before {
  display: none;
}

.form-wizard__area.vertical .step-progress__item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
}

.form-wizard__area.vertical .step-progress__item span {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 50px;
          flex: 0 0 50px;
  margin-bottom: 0;
  margin-right: 16px;
}

.form-wizard__area.vertical .step-progress__item h2 {
  white-space: nowrap;
  text-transform: capitalize;
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  color: rgba(39, 48, 65, 0.7);
}

.form-wizard__area.vertical .wizard-steps__item__content {
  min-height: unset;
}

.form-wizard__area.vertical .wizard-steps__item {
  margin: 17px 0 0 86px;
}

.form-wizard__area.vertical .step-progress__item {
  padding: 20px 0;
}

@media screen and (max-width: 767px) {
  .form-wizard__area.vertical .wizard-steps__item {
    margin: 17px 0 0 76px;
  }
}

.form-wizard__area #firstv {
  display: none;
}

.form-wizard__area #secondv {
  display: block;
}

.form-wizard__area #thirdv {
  display: none;
}

.form-wizard__area #fourv {
  display: none;
}

.file-upload__item {
  padding: 25px 25px 30px;
  margin-bottom: 40px;
}

.form-vertical__item {
  padding: 25px 25px 30px;
  margin-bottom: 30px;
  height: calc(100% - 30px);
}

.form-horizontal__item {
  padding: 25px 25px 30px;
  margin-bottom: 30px;
  height: calc(100% - 30px);
}

.form-horizontal__item .input__group {
  -webkit-box-orient: horizontal;
  -webkit-box-direction: normal;
      -ms-flex-direction: row;
          flex-direction: row;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.form-horizontal__item .input__group label {
  margin-bottom: 0;
}

.form-horizontal__item .input__group input,
.form-horizontal__item .input__group .input-overlay {
  -ms-flex-preferred-size: 70%;
      flex-basis: 70%;
}

.form-horizontal__item .input__button {
  margin-left: calc(30% - 25px);
}

@media screen and (max-width: 480px) {
  .form-horizontal__item .input__group {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-align: start;
        -ms-flex-align: start;
            align-items: flex-start;
    -webkit-box-pack: justify;
        -ms-flex-pack: justify;
            justify-content: space-between;
  }
  .form-horizontal__item .input__group label {
    margin-bottom: 10px;
  }
  .form-horizontal__item .input__group input,
  .form-horizontal__item .input__group .input-overlay {
    width: 100%;
  }
  .form-horizontal__item .input__button {
    margin-left: -20px;
  }
}

.input__button {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-left: -20px;
}

.input__button button {
  margin-left: 20px;
  padding: 13px 25px;
}

.comment__area {
  padding: 25px;
  margin-bottom: 30px;
}

@media screen and (max-width: 480px) {
  .comment__area {
    padding: 10px;
  }
}

.comment__item__top {
  margin-bottom: 20px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

@media screen and (max-width: 480px) {
  .comment__item__top {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-align: start;
        -ms-flex-align: start;
            align-items: flex-start;
  }
  .comment__item__top .item-left {
    margin-bottom: 15px;
  }
}

.comment__item__top .item-right .dropdown button {
  padding: 10px 12px 9px 14px;
  background: #F2F4F7;
  border-radius: 6px;
  border: 0;
  -webkit-box-shadow: none;
          box-shadow: none;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.comment__item__top .item-right .dropdown .dropdown-menu {
  background: #FFFFFF;
  border: 1px solid #E9EDF5;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  -webkit-box-shadow: 0px 0px 10px rgba(163, 177, 204, 0.12), 0px 10px 20px -5px rgba(163, 177, 204, 0.2);
          box-shadow: 0px 0px 10px rgba(163, 177, 204, 0.12), 0px 10px 20px -5px rgba(163, 177, 204, 0.2);
  border-radius: 6px;
}

.comment__item__top .item-right .dropdown .dropdown-menu.show {
  position: absolute;
  inset: 0px 0px auto auto !important;
  margin: 0px;
  -webkit-transform: translate(0px, 26px);
          transform: translate(0px, 26px);
}

.comment__item__top .item-right .dropdown .dropdown-item {
  padding: 5px 12px;
  text-transform: capitalize;
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #596680;
}

.comment__item__top .item-right .dropdown .dropdown-item span {
  margin-left: 10px;
}

.comment__content .comment__list li {
  margin-top: 20px;
}

.comment__content .comment__list .reply__list .list-item {
  padding-left: 55px;
}

@media screen and (max-width: 480px) {
  .comment__content .comment__list .reply__list .list-item {
    padding-left: 15px;
  }
}

.comment__content .comment__list .list-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.comment__content .comment__list .list-item .item-left {
  -webkit-box-flex: 0;
      -ms-flex: 0 0 40px;
          flex: 0 0 40px;
  margin-right: 15px;
}

.comment__content .comment__list .list-item .item-right {
  width: 100%;
}

.comment__content .comment__list .list-item .user-img img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}

.comment__content .comment__list .list-item .user-info {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

.comment__content .comment__list .list-item .user-info h2 {
  text-transform: capitalize;
  font-weight: 500;
  font-size: 14px;
  line-height: 24px;
  color: #273041;
}

.comment__content .comment__list .list-item .user-info h3 {
  font-weight: 500;
  font-size: 12px;
  line-height: 15px;
  color: #596680;
}

.comment__content .comment__list .list-item .item-right p {
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #596680;
}

.comment__content .comment__list .list-item .item-status {
  margin-top: 10px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.comment__content .comment__list .list-item .item-status a {
  text-transform: capitalize;
  font-weight: 500;
  font-size: 12px;
  line-height: 15px;
  color: #596680;
}

.comment__content .comment__list .list-item .item-status a:not(:first-child) {
  margin-left: 30px;
}

.comment__content .comment__list .list-item .item-status a:hover, .comment__content .comment__list .list-item .item-status a.active {
  color: #BE63F9;
}

.comment__content .comment__loading {
  margin: 30px 0 23px;
  text-align: center;
}

.comment__content .comment__loading a {
  text-transform: capitalize;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #5991FF;
}

.comment__content .comment__loading a img {
  width: 20px;
  height: 20px;
  margin-right: 10px;
}

.comment__create {
  display: -ms-grid;
  display: grid;
  -ms-grid-columns: 40px auto 40px;
      grid-template-columns: 40px auto 40px;
  grid-column-gap: 10px;
}

@media screen and (max-width: 480px) {
  .comment__create {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
  .comment__create .user-img,
  .comment__create .comment__text {
    margin-bottom: 10px;
  }
}

.comment__create .user-img img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}

.comment__create .send-button button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

.comment__create .comment__text {
  position: relative;
}

.comment__create .comment__text input {
  width: 100%;
  padding: 12px 16px 11px;
  border: 0;
  background: #F2F4F7;
  border-radius: 60px;
}

.comment__create .comment__text .overlay {
  position: absolute;
  top: 50%;
  right: 15px;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.comment__create .comment__text .overlay .overlay-item img {
  width: 20px;
  height: 20px;
}

.comment__create .comment__text .overlay .overlay-item:not(:first-child) {
  margin-left: 10px;
}

.comment__create .comment__text .overlay .overlay-item input {
  display: none;
}

/*--------------------------------
05. Utilities style
--------------------------------*/
.w-22 {
  width: 22%;
}

.w-35 {
  width: 35%;
}

.w-65 {
  width: 65%;
}

.mt-5 {
  margin-top: 5px !important;
}

.mt-157 {
  margin-top: 157px;
}

.mb-10 {
  margin-bottom: 10px;
}

.mb-14 {
  margin-bottom: 14px;
}

.mb-15 {
  margin-bottom: 15px;
}

.mb-20 {
  margin-bottom: 20px;
}

.mb-24 {
  margin-bottom: 24px;
}

.mb-25 {
  margin-bottom: 25px;
}

.mb-27 {
  margin-bottom: 27px;
}

.mb-28 {
  margin-bottom: 28px;
}

.mb-30 {
  margin-bottom: 30px;
}

.mb-33 {
  margin-bottom: 33px;
}

.mb-34 {
  margin-bottom: 34px;
}

.mb-45 {
  margin-bottom: 45px;
}

.mb-46 {
  margin-bottom: 46px;
}

.mb-47 {
  margin-bottom: 47px;
}

.mb-50 {
  margin-bottom: 50px;
}

.mb-55 {
  margin-bottom: 55px;
}

.mb-60 {
  margin-bottom: 60px;
}

.mb-178 {
  margin-bottom: 178px;
}

.mtb-100 {
  margin-top: 100px;
  margin-bottom: 100px;
}

.mtb-147 {
  margin-top: 147px;
  margin-bottom: 147px;
}

.mtb-156 {
  margin-top: 156px;
  margin-bottom: 156px;
}

.mtb-250 {
  margin-top: 250px;
  margin-bottom: 250px;
}

.text-right{
    text-align: right !important;
}

.ck-editor__editable {
    min-height: 250px;
}


.removeClass{
    /* margin-top: 20px; */
}
