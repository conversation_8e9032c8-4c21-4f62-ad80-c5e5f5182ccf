<?php

namespace App\Http\Controllers;

use App\Models\Topic;
use Illuminate\Http\Request;
use App\Traits\ToastrMessage;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class TopicController extends Controller
{
    use ToastrMessage;
    public function index(Request $request)
    {
        $query = Topic::with('chapter');

        if ($request->search_name != null) {
            $topics = $query
            ->where('title', 'like', '%' . $request->search_name . '%')
            ->orWhereHas('chapter', function ($query) use ($request) {
                $query->where('title', 'like', '%' . $request->search_name . '%');
            })
            ->paginate(10);
        } else {
            $topics = $query->paginate(10);
        }
        return view('admin.topic.index', compact('topics'));
    }

   
    public function add(){

        $chapters = DB::table('chapters')->where('created_by',auth()->id())->where('status',1)->get();
        return view('admin.topic.create',compact('chapters'));

    }
    public function topicStore(Request $request){

        $validator = Validator::make($request->all(), [
            'title' => 'required',
            'chapter_id' => 'required',
            'status' => 'required'
        ]);
        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }
        Topic::create([
            'title' => $request->title,
            'chapter_id' => $request->chapter_id,
            'status' => $request->status,
            'created_by' => auth()->id()
        ]);
        $this->success(__('Topic successfully Created'));
        return redirect()->back();
    }
    public function edit($id){

        $chapters = DB::table('chapters')->where('created_by',auth()->id())->where('status',1)->get();
        $topic = Topic::where('created_by',auth()->id())->where('status',1)->where('id',$id)->first();

        return view('admin.topic.edit',compact('chapters','topic')); 

    }

    public function update($id, Request $request){

        $validator = Validator::make($request->all(), [
            'title' => 'required',
            'chapter_id' => 'required',
            'status' => 'required'
        ]);
        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        Topic::where('id',$id)->update([
            'title' => $request->title,
            'chapter_id' => $request->chapter_id,
            'status' => $request->status,
            'created_by' => auth()->id()
        ]);

        $this->success(__('Topic successfully Updated'));
        return redirect()->route('instructor.topic.index');
    }
    
    public function delete($id){

        if(DB::table('academy_questions')->where('academy_questions.created_by',auth()->id())->where('topic_id',$id)->exists()){
            $this->success(__('Topic cannot be deleted because it contains questions.'));
        }else{
            Topic::where('created_by',auth()->id())->where('id',$id)->delete();
            $this->success(__('Topic successfully deleted'));
        }
        return redirect()->route('instructor.topic.index');
    }
}
