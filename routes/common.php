<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\BatchController;
use App\Http\Controllers\Common\FollowController;
use App\Http\Controllers\Common\WalletController;
use App\Http\Controllers\ExamManagementController;
use App\Http\Controllers\Instructor\SaasController;
use App\Http\Controllers\Common\AffiliateController;
use App\Http\Controllers\Student\ExamCenterController;
use App\Http\Controllers\Common\WalletRechargeController;
/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/




Route::group(['as' => 'affiliate.','prefix'=>'affiliate'], function () {

    Route::get('become-an-affiliate', [AffiliateController::class, 'becomeAffiliate'])->name('become-an-affiliate');
    Route::post('create-affiliate-request', [AffiliateController::class, 'becomeAffiliateApply'])->name('create-affiliate-request');
    Route::group(['middleware' => ['affiliate']], function () {
        Route::get('dashboard', [AffiliateController::class, 'dashboard'])->name('dashboard');
    });
    Route::get('my-affiliate-list',[AffiliateController::class, 'myAffiliations'])->name('my-affiliate-list');


//    Route::get('request-list', [AffiliateController::class, 'requestList'])->name('request-list');
//    Route::post('become-affiliate-apply', [AffiliateController::class, 'becomeAffiliateApply'])->name('become-affiliate.apply');


//    Route::get('your-application', function(){
//        return view('frontend.affiliator.your-application');
//    });
//
//    Route::get('affiliate-add-payment-method', function(){
//        return view('frontend.affiliator.affiliate-add-payment-method');
//    });
// Suraiya Static for Affiliate Pages End
});

Route::group(['middleware' => ['common']],function(){
    Route::get('follow', [FollowController::class,'follow'])->name('follow');
    Route::get('unfollow', [FollowController::class,'unfollow'])->name('unfollow');
    Route::get('saas-list', [SaasController::class, 'saasList'])->name('saas_panel');
    Route::get('saas-plan', [SaasController::class, 'saasPlan'])->name('saas_plan');
    Route::get('saas-plan-details/{id}', [SaasController::class, 'saasPlanDetails'])->name('saas_plan_details');
});

Route::group(['as' => 'wallet.','prefix'=>'wallet','middleware' => ['common']], function () {
    Route::get('/', [WalletController::class, 'index'])->name('/');
    Route::get('transaction-history', [WalletController::class, 'transactionHistory'])->name('transaction-history');
    Route::get('withdrawal-history', [WalletController::class, 'WithdrawalHistory'])->name('withdrawal-history');
    Route::get('wallet-recharge-history', [WalletController::class, 'rechargeHistory'])->name('recharge-history');
    Route::post('process-withdraw', [WalletController::class, 'withdrawProcess'])->name('process-withdraw');
    Route::get('beneficiary', [WalletController::class, 'myBeneficiary'])->name('my-beneficiary');

    Route::get('wallet-recharge/checkout', [WalletRechargeController::class, 'checkout'])->name('wallet_recharge.checkout');
    Route::post('wallet-recharge/pay', [WalletRechargeController::class, 'pay'])->name('wallet_recharge.pay');
    Route::post('wallet-recharge/razor-pay-payment', [WalletRechargeController::class, 'razorPayPayment'])->name('wallet_recharge.razor_pay_payment');

    Route::post('save-beneficiary', [WalletController::class, 'saveBeneficiary'])->name('save.my-beneficiary');
    Route::post('status-change-beneficiary/{beneficiary:uuid}', [WalletController::class, 'statusChangeBeneficiary'])->name('beneficiary_status.change');
    Route::post('save-paypal', [WalletController::class, 'savePaypal'])->name('save.paypal');
    Route::get('download-receipt/{uuid}', [WalletController::class, 'downloadReceipt'])->name('download-receipt');

});

Route::group(['prefix'=>'common-exam-Managment','middleware' => ['common']], function () {
    
    Route::get('/list', [ExamManagementController::class,'all'])->name('common.exam.index');
    Route::get('/add', [ExamManagementController::class,'add'])->name('common.exam.add');
    Route::post('/create', [ExamManagementController::class,'create'])->name('common.exam.create');

    Route::get('/edit/{id}', [ExamManagementController::class,'edit'])->name('common.exam.edit');
    Route::post('/update/{id}', [ExamManagementController::class,'update'])->name('common.exam.update');

    Route::get('/configaration/{slug}', [ExamManagementController::class,'examConfiguration'])->name('common.exam.examConfiguration');
    Route::post('/configaration-store', [ExamManagementController::class, 'examConfigurationStore'])->name('common.exam.examConfigurationStore');

    Route::get('edit-examConfiguration/{slug}', [ExamManagementController::class, 'editExamConfiguration'])->name('common.exam.editExamConfiguration');
    Route::post('update-examConfiguration', [ExamManagementController::class, 'updateExamConfiguration'])->name('common.exam.updateExamConfiguration');



    Route::get('/get-chapters/{subject_id}', [ExamManagementController::class, 'getChapters']);
    Route::get('/get-topics',[ExamManagementController::class, 'getTopics']);
   
    Route::get('/exam-questions-manage/{slug}', [ExamManagementController::class, 'examManageQuestions'])->name('common.exam.examManageQuestions');
    Route::post('/exam-questions-manage-store', [ExamManagementController::class, 'examManageQuestionsStore'])->name('common.exam.examManageQuestionsStore');
    Route::post('/exam-approved-unapproved', [ExamManagementController::class, 'examApprovedAndUnapproved'])->name('common.exam.examApprovedAndUnapproved');
    Route::get('/exam-report/{id}', [ExamManagementController::class, 'examReport'])->name('common.exam.examReport');

    Route::delete('/delete/{id}', [ExamManagementController::class,'delete'])->name('common.exam.delete');

    Route::get('genarate-exam-link', [ExamManagementController::class, 'genarateExamLink'])->name('common.exam.genarateExamLink');

    Route::get('/batch-management', [BatchController::class, 'index'])->name('common.exam.batch.index');
    Route::get('/batch-management/create', [BatchController::class, 'create'])->name('common.exam.batch.create');
    Route::post('/batch-management/store', [BatchController::class, 'store'])->name('common.exam.batch.store');
    Route::get('/batch-management/{id}', [BatchController::class, 'edit'])->name('common.exam.batch.edit');
    Route::post('/batch-management/update', [BatchController::class, 'update'])->name('common.exam.batch.update');
    Route::delete('/batch-management-delete/{id}', [BatchController::class,'delete'])->name('common.exam.batch.delete');

    Route::get('/get-batch-list', [BatchController::class, 'getBatchList'])->name('common.exam.getBatchList');
});






