{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.0.2", "devianl2/laravel-scorm": "^4.0", "doctrine/dbal": "^3.5", "firebase/php-jwt": "^6.1", "google/apiclient": "^2.12", "guzzlehttp/guzzle": "^7.4", "intervention/image": "^2.7", "ivanomatteo/laravel-device-tracking": "^0.2.1", "joisarjignesh/bigbluebutton": "^2.3", "ladumor/laravel-pwa": "^0.0.3", "laravel/framework": "^9.2", "laravel/sanctum": "^2.14.1", "laravel/socialite": "^5.5", "laravel/tinker": "^2.7", "laravel/ui": "^3.4", "league/flysystem-aws-s3-v3": "3.0", "league/omnipay": "^3.2", "mercadopago/dx-php": "2.5.3", "mews/purifier": "^3.3", "mollie/laravel-mollie": "^2.19", "omnipay/mollie": "^5.5", "omnipay/paypal": "^3.0", "paypal/rest-api-sdk-php": "1.13.0", "psr/simple-cache": "^3.0", "pusher/pusher-php-server": "^7.2", "razorpay/razorpay": "^2.8", "spatie/laravel-backup": "^8.1", "spatie/laravel-cookie-consent": "^3.2", "spatie/laravel-permission": "^5.5", "stripe/stripe-php": "^7.125", "unicodeveloper/laravel-paystack": "^1.0", "vimeo/vimeo-api": "^3.0", "vinkla/hashids": "^10.0", "yajra/laravel-datatables-oracle": "~9.0", "yoeunes/toastr": "^1.2"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.6", "fakerphp/faker": "^1.9.1", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^6.1", "phpunit/phpunit": "^9.5.10", "spatie/laravel-ignition": "^1.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Helper/coreconstant.php", "app/Helper/corearray.php", "app/Helper/helper.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"php-http/discovery": true}}, "minimum-stability": "dev", "prefer-stable": true}